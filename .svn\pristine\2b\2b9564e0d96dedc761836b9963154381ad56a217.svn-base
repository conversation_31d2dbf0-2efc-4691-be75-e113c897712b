import Vue from 'vue'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import QuillEditor from 'vue-quill-editor' //富文本
import Quill from 'quill' //富文本
import ImageResize from 'quill-image-resize-module' // 引用，调整图片大小
Quill.register('modules/imageResize', ImageResize)
let fontSizeStyle = Quill.import('attributors/style/size')
fontSizeStyle.whitelist = ['12px', '14px', '16px', '18px', '20px', '22px', '24px', '26px', '28px', '30px', '32px', '34px']
Quill.register(fontSizeStyle, true)
Vue.use(QuillEditor)
