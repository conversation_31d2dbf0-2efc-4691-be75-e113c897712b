<template>
  <div>
    <tsingle :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'SINGLE'"></tsingle>
    <tmultiple :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'MULTIPLE'"></tmultiple>
    <tjudge :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'JUDGE'"></tjudge>
    <tcompletion :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'COMPLETION'"></tcompletion>
    <tshort :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'SHORT'"></tshort>
    <tcompatiblity :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'COMPATIBILITY'" :splited="splited"></tcompatiblity>
    <tcomprehensive :form="form" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'COMPREHENSIVE'" :splited="splited"></tcomprehensive>
  </div>
</template>

<script>
import tsingle from '@/views/question/view/tsingle'
import tmultiple from '@/views/question/view/tmultiple'
import tjudge from '@/views/question/view/tjudge'
import tcompletion from '@/views/question/view/tcompletion'
import tshort from '@/views/question/view/tshort'
import tcomprehensive from '@/views/question/view/tcomprehensive'
import tcompatiblity from '@/views/question/view/tcompatiblity'
export default {
  components: {
    tsingle,
    tmultiple,
    tjudge,
    tcompletion,
    tshort,
    tcomprehensive,
    tcompatiblity
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    difficultys: {
      type: Array,
      required: true
    },
    letters: {
      type: Array,
      required: true
    },
    splited: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>
