<template>
  <div>
    <el-dialog title="" :visible="showDialog" width="width" @opened="initjsMind">
      <div id="jsmind_container"></div>
    </el-dialog>
  </div>
</template>
<script>
// 引入 jsMind
import jsMind from 'jsmind'
// import 'jsmind/draggable-node'
import 'jsmind/style/jsmind.css'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      jm: null
    }
  },
  created() {},
  mounted() {
    // this.initjsMind()
  },
  methods: {
    initjsMind() {
      // jsMind 的配置项
      const options = {
        container: 'jsmind_container', // 容器的ID
        theme: 'primary', // 主题
        editable: false // 是否允许编辑
        // 其他配置项...
      }

      // 思维导图的初始数据
      const mindData = {
        meta: {
          name: 'questionViews',
          author: 'cxh',
          version: '0.1'
        },
        format: 'node_tree',
        data: {
          id: 'root',
          topic: '问诊试图',
          children: [
            {
              id: 'easy',
              topic: 'Easy',
              children: [
                { id: 'easy1', topic: 'Easy to show' },
                { id: 'easy2', topic: 'Easy to edit' },
                { id: 'easy3', topic: 'Easy to store' },
                { id: 'easy4', topic: 'Easy to embed' }
              ]
            },
            {
              id: 'open',
              topic: 'Open Source',
              children: [
                { id: 'open1', topic: 'on GitHub' },
                { id: 'open2', topic: 'BSD License' }
              ]
            }
          ]
        }
      }

      // 初始化 jsMind
      const jm = new jsMind(options)
      jm.show(mindData)

      // 如果需要，保存 jsMind 实例到组件的数据属性中，以便后续操作
      this.jm = jm
    }
  }
}
</script>
<style scoped lang="scss">
#jsmind_container {
  width: 100%;
  height: 500px;
  border: 1px solid #ccc;
  /* 根据需要调整容器样式 */
}
</style>
