import request from '@/utils/request'

//正式考试试卷分页列表
export function examinePaperList(data) {
  return request({
    url: '/study/paper/examinePaperList',
    method: 'post',
    data
  })
}
//添加正式考试试卷
export function saveExaminePaper(data) {
  return request({
    url: '/study/paper/saveExaminePaper',
    method: 'post',
    data
  })
}
//修改正式考试试卷
export function updateExaminePaper(data) {
  return request({
    url: '/study/paper/updateExaminePaper',
    method: 'post',
    data
  })
}
//删除正式考试试卷（逻辑删除）
export function removeExaminePaper(data) {
  return request({
    url: '/study/paper/removeExaminePaper',
    method: 'delete',
    params: data
  })
}
//正式考试试卷详情
export function selectExaminePaperDetailById(data) {
  return request({
    url: '/study/paper/selectExaminePaperDetailById',
    method: 'get',
    params: data
  })
}
//发布正式考试试卷
export function releaseExaminePaper(data) {
  return request({
    url: '/study/paper/releaseExaminePaper',
    method: 'post',
    data
  })
}
//智能组卷
export function saveAutoExaminePaper(data) {
  return request({
    url: '/study/paper/saveAutoExaminePaper',
    method: 'post',
    data
  })
}
