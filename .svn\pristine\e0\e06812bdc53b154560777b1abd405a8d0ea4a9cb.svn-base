import request from '@/utils/requestAi'
/** 获取应用绑定的会话角色和模型 */
export function chatRoleModel(appId, headers) {
  return request({
    url: '/api/chat/chatRoleModel',
    method: 'GET',
    params: {
      appId
    },
    headers
  })
}
/** 获取应用绑定的会话角色和模型 */
export function chatRoleInfo(chatRoleId, headers) {
  return request({
    url: '/api/chat/chatRoleInfo',
    method: 'GET',
    params: {
      chatRoleId
    },
    headers
  })
}

/** 获取应用配置 */
export function appConf(appId, headers) {
  return request({
    url: '/api/chat/appConf',
    method: 'GET',
    params: {
      appId
    },
    headers
  })
}

/** 流式对话 */
export function generateStream(data, headers) {
  return request({
    url: '/api/chat/generateStream',
    method: 'post',
    headers,
    data
  })
}

/** 音色 */
export function chatTts(headers) {
  return request({
    url: '/api/chat/tts',
    method: 'get',
    headers
  })
}
