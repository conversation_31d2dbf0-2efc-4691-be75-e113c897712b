<template>
  <div class="">
    <el-dialog custom-class="createVoiceDialog" :visible="showDialog" center title="选择您喜欢的音色" top="35vh" :show-close="false" @open="getVoiceList" @close="close" :destroy-on-close="true">
      <div class="toneBox">
        <ul>
          <li v-for="item in toneList" :key="item.name">
            <div class="imgView" @click="play(item)">
              <img :src="item.voiceIcon" alt="" />
              <img v-if="playTone !== item.refId || item.audioStatus === 2" class="playIcon" src="@/assets/case/ai/play.png" alt="" />
              <img v-else class="pauseIcon" src="@/assets/case/ai/pause.png" alt="" />
              <template v-if="playTone === item.refId && item.audioStatus !== 0">
                <div class="maskLayer"></div>
                <svg class="progressBar">
                  <circle class="progress-ring__circle" stroke="#274e6a" stroke-width="5" fill="transparent" r="52" cx="55" cy="55" />
                </svg>
              </template>
              <div class="checkedIcon" :class="{ userChecked: playTone === item.refId }">
                <i class="el-icon-check"></i>
              </div>
            </div>
            <p class="name">{{ item.voiceName }}</p>
          </li>
        </ul>
      </div>
      <audio v-if="showAudio" ref="audioRef" autoplay>
        <source :src="playUrl" type="audio/wav" />
        您的浏览器不支持 audio 标签。
      </audio>
      <template v-slot:footer>
        <el-button @click="confirm">应用</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { caseUpdateRefId, caseBatchCreateVoice, getSecret, caseCreateVoice } from '@/api/case.js'
import { chatTts } from '@/api/ai.js'
import { gsap } from 'gsap'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      isOne: false,
      questionId: null,

      playUrl: null,
      playTone: null, // 正在播放的音色
      checkedItem: null,
      toneList: [],
      tween: null,
      flag: false,
      showAudio: false
    }
  },
  created() {},
  methods: {
    async getVoiceList() {
      const { data } = await getSecret()
      chatTts({ aiToken: data.aiToken, Appid: data.appId }).then((res) => {
        this.toneList = res.data
        this.showAudio = true
        this.toneList.forEach((item) => {
          this.$set(item, 'audioStatus', 0)
        })
      })
    },
    close() {
      this.playUrl = null
      this.playTone = null
      this.isOne = false
      this.questionId = null

      // 停止当前的动画
      if (this.tween) {
        this.tween.revert()
        const circle = document.querySelector('.progress-ring__circle')
        gsap.killTweensOf(circle) // 停止当前动画
      }

      const audio = this.$refs['audioRef']
      audio.load()

      this.showAudio = false
      this.$emit('update:showDialog')
      console.log('关了了')
    },
    async confirm() {
      if (this.playTone) {
        if (this.isOne) {
          caseUpdateRefId({ refId: this.playTone, caseId: this.$route.params.id }).then(async () => {
            caseCreateVoice({ caseId: this.$route.params.id, questionId: this.questionId })
            this.$message.success('语音生成成功!')
            this.close()
            this.$emit('success')
          })
        } else {
          caseUpdateRefId({ refId: this.playTone, caseId: this.$route.params.id }).then(async () => {
            caseBatchCreateVoice({ caseId: this.$route.params.id })
            this.$message.success('语音生成成功!')
            this.close()
            this.$emit('success')
          })
        }
      } else {
        this.$message.error('请选择音色')
      }
    },
    play(item) {
      this.checkedItem = item
      const audio = this.$refs['audioRef']
      if (this.playTone && this.playTone === item.refId) {
        if (item.audioStatus === 2) {
          item.audioStatus = 1
          audio.play()
          this.tween.resume() // 停止当前动画
        } else {
          item.audioStatus = 2
          audio.pause()
          this.tween.pause() // 停止当前动画
        }
      } else {
        this.playUrl = window.config.VUE_AI_BASE_PATH + item.auditionFile
        this.playTone = item.refId
        item.audioStatus = 1
        this.flag = false
        this.$nextTick(() => {
          audio.load()
          audio.addEventListener('loadedmetadata', () => {
            // 在元数据加载完成后，准备动画
            this.playAnimation(audio.duration)
          })
        })
      }
    },

    playAnimation(duration) {
      if (this.flag) {
        return
      }
      this.flag = true
      // 停止当前的动画
      if (this.tween) {
        this.tween.revert()
      }

      const circle = document.querySelector('.progress-ring__circle')
      this.tween = gsap.fromTo(
        circle,
        { strokeDashoffset: 345 },
        {
          strokeDashoffset: 0,
          duration: duration,
          ease: 'none',
          onComplete: () => {
            this.flag = false
            this.playTone = null
            console.log('动画执行结束')

            this.play(this.checkedItem)
          }
        }
      )
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .createVoiceDialog {
    width: 926px;
    height: 345px;
    background: #ffffff;
    border-radius: 20px;
  }
  .el-dialog__header {
    padding: 0;
    padding: 32px;
    & > span {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #000000;
    }
  }
  .el-dialog__body {
    padding: 30px 53px;
    padding-top: 0;
    .toneBox {
      ul {
        display: flex;
        align-items: center;
        li {
          margin-right: 34px;
          &:last-of-type {
            margin-right: 0;
          }
          .imgView {
            position: relative;
            width: 110px;
            height: 110px;
            cursor: pointer;
            img {
              width: 100%;
              height: 100%;
            }
            .pauseIcon,
            .playIcon {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              font-size: 52px;
              width: 52px;
              height: 52px;
              z-index: 2;
            }
            .maskLayer {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              background: rgba($color: #000000, $alpha: 0.4);
              border-radius: 50%;
            }
            .progressBar {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
            }
            .progress-ring__circle {
              transform: rotate(-90deg);
              transform-origin: 50% 50%;
              stroke-dasharray: 345, 345;
              stroke-dashoffset: 345;
            }
            .checkedIcon {
              position: absolute;
              right: 0;
              bottom: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 28px;
              height: 28px;
              background: #92a2ae;
              border-radius: 50%;
              i {
                font-size: 18px;
                color: #fff;
              }
            }
            .userChecked {
              background: #274e6a;
            }
          }
          .name {
            margin: 0;
            margin-top: 9px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            text-align: center;
          }
        }
      }
    }
  }
  .el-dialog__footer {
    text-align: center;
    .el-button {
      width: 92px;
      height: 50px;
      padding: 0;
      border: none;
      background: #274e6a;
      border-radius: 63px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
    }
  }
}
</style>
