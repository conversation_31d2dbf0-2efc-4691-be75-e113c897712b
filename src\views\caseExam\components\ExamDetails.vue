<template>
  <div>
    <el-dialog custom-class="examDetailsDialog" title="考核详情" top="25px" center :visible="detailsDialog" @close="close">
      <div class="exam-detail-wrapper">
        <el-row>
          <el-col :span="12">
            <span class="label">考试名称</span>
            <span class="value">{{ examInfo.name }}</span>
          </el-col>
          <el-col :span="12">
            <span class="label">考试时间</span>
            <span class="value">{{ examInfo.startTime }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <span class="label">考试限时</span>
            <span class="value">{{ examInfo.time }}分钟</span>
          </el-col>
          <el-col :span="12">
            <span class="label">及格分</span>
            <span class="value">{{ examInfo.passScore }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="case-details">
            <span class="label">考核病例</span>
            <div class="caseBox">
              <div v-for="item in caseList" :key="item.caseId" class="caseItem">
                <div class="case-item_header">
                  <span>分数权重</span>
                  <el-select popper-class="noXScroll" v-model="item.weight" placeholder="选择权重" disabled @change="weightChnage($event, item.caseId)">
                    <el-option v-for="item in weightOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                  </el-select>
                  <div class="question" @click="lookCaseDetails(item)">病例详情 <i class="el-icon-arrow-right"></i></div>
                </div>
                <div class="case-item_body">
                  <div class="caseItem_top_left">
                    <casePhoto :sex="item.sex" :age="item.age" />
                  </div>
                  <div class="caseItem_top_right">
                    <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
                      <div class="caseName">{{ item.name }}</div>
                    </el-tooltip>
                    <div class="patientInfo">
                      <span :class="{ man: item.sex === 'M' }" class="woman">（{{ item.sex === 'M' ? '男' : '女' }}）</span>
                      <span class="sex"> {{ item.age }} 岁</span>
                    </div>
                    <div class="score">
                      <span>病例总分</span>
                      <span> {{ item.allScore }}分</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="student-detils">
            <span class="label">班级学生</span>
            <div class="checkedStudents">
              <div v-for="item in studentList" :key="item.id">
                <div class="checkedClass">{{ item.className }}</div>
                <ul class="students">
                  <li v-for="li in item.children" :key="li.id">
                    <el-tag>{{ li.name }}</el-tag>
                  </li>
                </ul>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div slot="footer">
        <el-button type="primary" class="confirm__button" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 病例详情 -->
    <LookCaseDetails ref="LookCaseDetails" :showDialog.sync="lookCaseDetailsDialog" />
  </div>
</template>
<script>
import casePhoto from '@/components/casePhoto'
import LookCaseDetails from '@/views/caseExam/components/LookCaseDetails'

export default {
  name: '',
  props: {
    detailsDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    casePhoto,
    LookCaseDetails
  },
  data() {
    return {
      examInfo: {},
      caseList: [],
      studentList: [],
      weightOptions: [
        { value: 10, label: '10%' },
        { value: 20, label: '20%' },
        { value: 30, label: '30%' },
        { value: 40, label: '40%' },
        { value: 50, label: '50%' },
        { value: 60, label: '60%' },
        { value: 70, label: '70%' },
        { value: 80, label: '80%' },
        { value: 90, label: '90%' },
        { value: 100, label: '100%' }
      ],
      lookCaseDetailsDialog: false
    }
  },
  created() {},
  methods: {
    getInfo(data) {
      this.examInfo = { ...data }
      this.examInfo.cases.forEach((item) => {
        this.caseList.push({
          caseId: item.exam_case_id,
          allScore: item.all_score,
          weight: item.weight,
          sex: item.sex,
          name: item.name,
          age: item.age,
          form: item.form,
          mainDemands: item.main_demands
        })
      })
      const parentIds = data.clbumIds.split(',')
      const studentList = []
      parentIds.forEach((item) => {
        studentList.push({
          classId: parseInt(item),
          className: null,
          children: []
        })
      })
      studentList.forEach((li) => {
        data.students.forEach((item) => {
          if (li.classId === item.clbum_id) {
            li.className = item.clbum_name
            li.children.push({
              id: item.student_id,
              name: item.name
            })
          }
        })
      })
      this.studentList = studentList
    },
    // 查看病例详情
    lookCaseDetails(item) {
      console.log(item)
      this.lookCaseDetailsDialog = true
      this.$refs['LookCaseDetails'].getCaseDetails(item, 'examDetails')
    },
    close() {
      this.examInfo = {}
      this.caseList = []
      this.studentList = []
      this.$emit('update:detailsDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .examDetailsDialog {
    width: 1185px;

    height: 860px;
    background: #ffffff;
    border-radius: 30px;
    .el-dialog__header {
      padding-bottom: 20px;
      .el-dialog__title {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
        line-height: 26px;
        text-align: center;
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          font-size: 24px;
          font-weight: 600;
          color: #666666;
        }
      }
    }
    .el-dialog__body {
      height: 700px;
      padding: 0 40px;
      border-radius: 40px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 6px;
        background: transparent;
      }

      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #81909a;
        border-radius: 40px;
      }

      .exam-detail-wrapper {
        width: 100%;
        background: #f5f5f5;
        border-radius: 20px;
        .el-row {
          min-height: 60px;
          padding: 18px 27px;
          border-bottom: 1px solid #fff;
          .label {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #07121e;
          }
          .value {
            margin-left: 10px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 18px;
            color: #666666;
          }
        }
        .case-details,
        .student-detils {
          display: flex;
        }
      }
    }
    .el-dialog__footer {
      padding: 0;
      padding-top: 20px;

      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }
      .confirm__button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 93px;
        height: 50px;
        background: #274e6a;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #fff;
      }
    }
  }
}
// 选择后病例样式
.caseBox {
  flex: 1;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-left: 13px;
  .caseItem {
    width: 470px;
    height: 256px;
    margin-right: 20px;
    margin-bottom: 20px;
    padding: 20px 22px 20px 25px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(177, 177, 177, 0.15);
    border-radius: 20px 20px 20px 20px;
    border: 1px solid #e8e8e8;
    &:nth-of-type(2n) {
      margin-right: 0;
    }
    .case-item_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      & > span {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
      }
      .el-select {
        .el-input__inner {
          width: 232px;
          height: 45px;
          background: #eef0f2;
          border-radius: 10px;
          border: none;
          color: #333333;
          font-size: 20px;
        }
      }
      .question {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #65849a;
        cursor: pointer;
      }
    }

    .case-item_body {
      display: flex;
      margin-top: 25px;
      .caseItem_top_left {
        width: 115px;
        height: 140px;
        box-shadow: inset 8px 10px 50px 0px rgba(255, 255, 255, 0.3);
        border-radius: 10px 10px 10px 10px;
        ::v-deep {
          img {
            width: 100%;
            height: 100%;
            object-fit: scale-down;
          }
        }
      }
      .caseItem_top_right {
        flex: 1;
        padding: 10px;
        line-height: 24px;
        overflow: hidden;
        .caseName {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #07121e;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .patientInfo {
          margin: 17px 0 15px;
          .woman {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 20px;
            color: #f494b7;
          }
          .man {
            color: #3381b9;
          }
          .sex {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 20px;
            color: #666666;
          }
        }
        .score {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 292px;
          height: 50px;
          background: #eef0f2;
          border-radius: 10px 10px 10px 10px;
          & > span:first-of-type {
            font-size: 20px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #07121e;
          }
          & > span:last-of-type {
            margin-left: 25px;
            font-size: 20px;
            font-family: PingFang SC;
            font-weight: bold;
            color: #3381b9;
          }
        }
      }
    }
  }
}
.checkedStudents {
  flex: 1;
  margin-left: 13px;
  padding: 0 30px;
  padding-bottom: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 7px 0px rgba(204, 198, 198, 0.25);
  border-radius: 14px 14px 14px 14px;
  border: 1px solid #edeef2;

  .checkedClass {
    margin-top: 23px;
    margin-bottom: 13px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #274e6a;
  }
  .students {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    li {
      margin-right: 20px;
      margin-bottom: 12px;
      .el-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        padding: 0 16px;
        background: #f3f5f9;
        border-radius: 8px 8px 8px 8px;
        border: none;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #07121e;
        .el-icon-close {
          top: 2px;
          width: 20px;
          height: 20px;
          margin-left: 8px;
          font-size: 20px;
          color: #274e6a;
          &:hover {
            cursor: pointer;
            background: transparent;
          }
        }
      }
    }
  }
}
</style>
<style>
.caseNameTooltip {
  font-size: 18px;
}
</style>
