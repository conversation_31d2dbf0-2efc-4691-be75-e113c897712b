import request from '@/utils/request'
import download from '@/utils/downloader.js'

//学生分页列表(根据学校id查询)
export function studentList(data) {
  return request({
    url: '/system/student/studentList',
    method: 'post',
    data
  })
}
//添加学生
export function addStudent(data) {
  return request({
    url: '/system/student/addStudent',
    method: 'post',
    data
  })
}
//修改学生信息
export function updateStudent(data) {
  return request({
    url: '/system/student/updateStudent',
    method: 'post',
    data
  })
}
// 重置学生密码
export function studentPwdReset(data) {
  return request({
    url: '/system/student/studentPwdReset',
    method: 'put',
    params: data
  })
}
// 删除学生
export function removeStudent(data) {
  return request({
    url: '/system/student/removeStudent',
    method: 'delete',
    params: data
  })
}
//导入学生
export function importStudent(data) {
  return request({
    url: '/system/student/importStudent',
    method: 'post',
    data
  })
}
//导出学生
export function studentExport(data) {
  return download({
    url: '/system/student/studentExport',
    method: 'post',
    data
  })
}
//修改启用/禁用状态
export function updateState(data) {
  return request({
    url: '/system/student/updateState',
    method: 'post',
    params: data
  })
}
//查看学生个人用户详情
export function selectStudentDetail(data) {
  return request({
    url: '/system/student/selectStudentDetail',
    method: 'get',
    params: data
  })
}
