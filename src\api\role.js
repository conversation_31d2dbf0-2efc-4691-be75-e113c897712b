import request from '@/utils/request'

//根据学校查询角色
export function roleList(data) {
  return request({
    url: '/system/role/roleList',
    method: 'get',
    params: data
  })
}

// 添加角色
export function roleSave(data) {
  return request({
    url: '/system/role/roleSave',
    method: 'post',
    data
  })
}
// 修改角色
export function roleUpdate(data) {
  return request({
    url: '/system/role/roleUpdate',
    method: 'post',
    data
  })
}

//删除角色
export function roleRemove(data) {
  return request({
    url: '/system/role/roleRemove',
    method: 'delete',
    params: data
  })
}
