<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    <div class="right-menu">
      <!-- <div>超级管理员</div> -->
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="baseurl + userinfo.icon" class="user-avatar" v-if="userinfo.icon" />
          <img src="@/assets/images/1.png" class="user-avatar" v-if="!userinfo.icon" />
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <span style="display: block" @click="openPassword">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item>
            <span style="display: block" @click="openEdit">修改个人信息</span>
          </el-dropdown-item>
          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <el-dropdown-item>
            <span style="display: block" @click="logout">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div style="float: right; line-height: 60px">{{ name }}</div>
    <el-dialog title="修改密码" width="400px" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="showpassword">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label-width="100px" label="旧密码" prop="password">
          <el-col :span="22">
            <el-input type="password" v-model="ruleForm.password" placeholder="请输入旧密码" autocomplete="off" clearable></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label-width="100px" label="新密码" prop="newPassword">
          <el-col :span="22">
            <el-input type="password" v-model="ruleForm.newPassword" placeholder="请输入新密码" autocomplete="off" clearable></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label-width="100px" label="确认密码" prop="confirmPassword">
          <el-col :span="22">
            <el-input type="password" v-model="ruleForm.confirmPassword" placeholder="请再次输入新密码" autocomplete="off" clearable></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showpassword = false">取 消</el-button>
        <el-button @click="submitForm" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改个人信息" :close-on-click-modal="false" :visible.sync="showinfo" width="720px">
      <el-form :model="editForm" :rules="rules" ref="editForm" :inline="true" style="width: calc(100% - 20px)">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" label-width="110px">
              <el-input v-model="editForm.loginName" style="width: 207px" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name" label-width="110px">
              <el-input placeholder="请输入姓名" v-model="editForm.name" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex" label-width="110px">
              <el-select v-model="editForm.sex" placeholder="请选择性别" style="width: 207px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="mobile" label-width="110px">
              <el-input placeholder="请输入电话号码" v-model="editForm.mobile" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在组织" label-width="110px">
              <el-input v-model="editForm.organizationName" style="width: 207px" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属角色" label-width="110px">
              <el-input v-model="editForm.roleName" style="width: 207px" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="所属专业" label-width="110px">
              <div v-if="editForm.majors && editForm.majors.length > 0">
                <span v-for="item in editForm.majors">【{{ item.majorName }}】</span>
              </div>
              <div v-else>暂无</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="管理班级" label-width="110px">
              <div v-if="editForm.clbums && editForm.clbums.length > 0">
                <span v-for="item in editForm.clbums">【{{ item.clbumName }}】</span>
              </div>
              <div v-else>暂无</div>
            </el-form-item>
          </el-col>
          <el-form-item label="头像" prop="icon" label-width="110px">
            <div class="avataruploads">
              <div class="avatarupcover avatarupcover-add" @click="$refs.attachment_upload_edit.click()">
                <i class="el-icon-plus"></i>
              </div>
              <img :src="baseurl + editForm.icon" v-if="editForm.icon != ''" />
              <input ref="attachment_upload_edit" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="handleOssPutEdit($event)" />
            </div>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetEdit">取 消</el-button>
        <el-button type="primary" @click="editUser">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectTeacherById, teacherUpdatePwd, updateTeacherDetail } from '@/api/teacher.js'
import { putProgress } from '@/utils/oss.js'
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  data() {
    var validatePass0 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入旧密码'))
      } else {
        callback()
      }
    }
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else {
        if (this.ruleForm.confirmPassword !== '') {
          this.$refs.ruleForm.validateField('confirmPassword')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      userinfo: {},
      name: '',
      showinfo: false,
      showpassword: false,
      userMajorSele: false,
      options: [
        {
          value: 'M',
          label: '男'
        },
        {
          value: 'F',
          label: '女'
        }
      ],
      ruleForm: {
        password: '',
        newPassword: '',
        confirmPassword: ''
      },
      editForm: {
        userId: '',
        name: '',
        mobile: '',
        sex: '',
        icon: ''
      },
      rules: {
        password: [
          {
            validator: validatePass0,
            trigger: 'blur'
          }
        ],
        newPassword: [
          {
            validator: validatePass,
            trigger: 'blur'
          }
        ],
        confirmPassword: [
          {
            validator: validatePass2,
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入姓名'
          }
        ],
        sex: [
          {
            required: true,
            message: '请选择性别'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device'])
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.showpassword = false
          teacherUpdatePwd(this.ruleForm).then((res) => {
            if (res.code == '200') {
              this.$alert('修改成功，请重新登陆！', '提示', {
                confirmButtonText: '确定',
                type: 'warning'
              })
                .then(() => {
                  this.logout()
                })
                .catch(() => {
                  this.logout()
                })
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        }
      })
    },
    openPassword() {
      this.ruleForm = {
        password: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.showpassword = true
    },
    openEdit() {
      this.editForm = Object.assign({}, this.userinfo)
      this.showinfo = true
    },
    getUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
    handleOssPutEdit(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/icon/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.editForm.icon = key
        this.$refs.attachment_upload_edit.value = ''
      })
    },
    editUser() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          updateTeacherDetail({
            userId: this.editForm.userId,
            name: this.editForm.name,
            sex: this.editForm.sex,
            mobile: this.editForm.mobile,
            icon: this.editForm.icon
          }).then(async (res) => {
            if (res.code == '200') {
              this.resetEdit()
              location.reload()
              return
              this.$alert('修改成功，请重新登陆！', '提示', {
                confirmButtonText: '确定',
                type: 'warning'
              })
                .then(() => {
                  this.logout()
                })
                .catch(() => {
                  this.logout()
                })
            } else {
              this.$message({
                type: 'error',
                message: res.msg
              })
            }
          })
        }
      })
    },
    resetEdit() {
      this.showinfo = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.name = res.data.name
      })
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  .breadcrumb-container {
    float: left;
  }
  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }
  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    &:focus {
      outline: none;
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;
        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .avatar-container {
      margin-right: 30px;
      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
