<template>
  <div>
    <!-- 选择病例 -->
    <el-dialog title="选择病例" :visible="selectCaseDialog" width="965px" custom-class="selectCase" @close="close">
      <el-form ref="form" :model="queryInfo" label-width="80px" inline>
        <el-form-item label="病例名称:" label-width="80px">
          <el-input v-model="queryInfo.name" clearable placeholder="请输入病例名称" @clear="getCaseList" @keydown.native.enter="getCaseList"></el-input>
        </el-form-item>
        <!-- <el-form-item label="训练次数:" label-width="80px">
          <el-radio-group v-model="queryInfo.orderby" @change="getCaseList">
            <el-radio :label="1">倒序</el-radio>
            <el-radio :label="2">正序</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item>
          <el-button type="success" size="small" @click="getCaseList">查询</el-button>
          <el-button type="primary" size="small" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="caseBox">
        <div v-for="(item, index) in list" :key="item.caseId" :class="[{ isChecked: checkedCaseId === item.caseId }, 'caseItem']" @click="checked(item, index)">
          <i v-if="checkedCaseId === item.caseId" class="el-icon-success"></i>

          <!-- <i v-if="item.isChecked" class="el-icon-success"></i> -->
          <div class="caseItem_top">
            <div class="caseItem_top_left">
              <div class="score">
                总分: <span>{{ item.allScore }}</span> 分
              </div>
              <casePhoto :height="'186px'" :width="'136px'" :sex="item.sex" :age="item.age" :type="'case'" />
            </div>
            <div class="caseItem_top_right">
              <div>
                <span>
                  {{ item.name }}
                  <svg-icon :icon-class="item.sex === 'M' ? 'nan' : 'nv'"></svg-icon>
                </span>
                <span> {{ item.age }} 岁</span>
                <span>{{ item.form | caseForm }}</span>
              </div>

              <div>
                <span> {{ item.mainDemands }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <el-pagination style="text-align: center; margin-top: 15px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next, jumper" :total="total" @size-change="getCaseList" @current-change="getCaseList"> </el-pagination>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confimSelect">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseList } from '@/api/case'
import casePhoto from '@/components/casePhoto'

export default {
  name: '',
  components: {
    casePhoto
  },
  props: {
    selectCaseDialog: {
      type: Boolean,
      require: true
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      // 选择病例
      queryInfo: {
        name: null,
        sort: 1,
        caseType: 2,
        isEnable: 1,
        orderby: 1,
        pageNum: 1,
        pageSize: 4
      },
      list: [],
      total: 0,
      checkedCaseId: null
    }
  },
  created() {},
  methods: {
    async getCaseList(type) {
      console.log(this.selectList)
      const { data } = await caseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      if (!type) {
        // 多选逻辑
        // const selectIds = this.selectList.map((item) => item.caseId)
        // if (this.selectList.length) {
        //   this.list.forEach((item) => {
        //     if (selectIds.includes(item.caseId)) {
        //       this.$set(item, 'isChecked', true)
        //     }
        //   })
        // }
        // 单选逻辑
        this.checkedCaseId = this.selectList[0].caseId
      }
    },
    reset() {
      this.queryInfo = {
        name: null,
        sort: 1,
        caseType: 2,
        isEnable: 1,
        orderby: 1,
        pageNum: 1,
        pageSize: 4
      }
      this.getCaseList()
    },
    checked(item, index) {
      this.checkedCaseId = item.caseId
      // await this.getCaseList(true)

      // 多选逻辑
      // if (item.isChecked) {
      //     this.$set(this.list[index], 'isChecked', false)
      //   } else {
      //     this.$set(this.list[index], 'isChecked', true)
      //   }
    },
    confimSelect() {
      // 多选逻辑
      // const list = this.list.filter((item) => {
      //   return item.isChecked
      // })

      // 单选
      const list = [this.list.find((item) => item.caseId === this.checkedCaseId)]
      this.close()
      this.$emit('success', list)
    },
    close() {
      this.$emit('update:selectCaseDialog', false)
    }
  }
}
</script>
<style scoped lang="scss"></style>
