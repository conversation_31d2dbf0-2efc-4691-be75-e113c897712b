<template>
  <div class="student-grade universal-layout">
    <el-row class="student-grade__header universal-layout__header">
      <div class="header-back" @click="closeCheck">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="header-title">考生成绩详情</div>
    </el-row>
    <div class="student-grade__body universal-layout__body">
      <div class="question-info-view">
        <div class="menuinfo">
          <template v-if="questionType">
            <div class="topic_des" :class="form.questionType">
              {{ showQuestionType(form.questionType) }}
            </div>
            <div>
              <questionview ref="questionview" @beforeCorrect="beforeCorrect" :edit="type == 'edit'" :splited="true" :letters="letters" :form="form" :difficultys="difficultys" v-if="questionType"></questionview>
            </div>
          </template>
        </div>
        <div class="button-group">
          <el-button @click="jumpQuestion(-1)" :disabled="!beforeBtn" type="primary" v-if="viewIndex >= 0">上一题</el-button>
          <el-button @click="jumpQuestion(1)" :disabled="!afterBtn" type="primary" v-if="viewIndex >= 0">下一题</el-button>
        </div>
      </div>

      <div class="student-answer-panel">
        <div class="student-info">
          <div class="student-info__photo">
            <div class="avatar">
              <img :src="baseUrl + topform.icon" alt="" />
              <img v-if="topform.sex === 'M'" class="sexIcon" src="@/assets/case/manIcon.png" alt="" />
              <img v-else class="sexIcon" src="@/assets/case/womanIcon.png" alt="" />
            </div>
            <div class="name">
              <span>{{ topform.name }}</span>
              <span>{{ topform.loginName }}</span>
            </div>
          </div>
          <div class="student-info__item border_item small">
            <span>得分<i>(分)</i></span>
            <span>{{ topform.paperScore }}</span>
          </div>

          <div class="student-info__item border_item small">
            <span>用时<i>(分)</i></span>
            <span>{{ topform.duration }}</span>
          </div>
          <!-- <div class="statistics_item border_item">
            <span>考试时间<i>(分)</i></span>
            <span>2023-11-
              09 14:15 ~ 15:35</span>
          </div> -->
          <div class="student-info__item border_item large">
            <span>考试名称</span>
            <span>{{ topform.paperName }}</span>
          </div>
        </div>

        <div class="menuinfo_list">
          <div class="menuinfo_top">
            <span class="menuinfo-title">考试题</span>
            <span class="menuinfo-total">题目总数：{{ topform.questionExerciseReqList.length }}</span>
          </div>
          <div class="menuinfo_info" v-for="questionType in questionTypes" :key="questionType.value" v-show="getArray(questionType.value).length > 0">
            <div class="menuinfo_infoname">{{ questionType.name }}（共{{ getArray(questionType.value).length }}题, 共 {{ questionTypeTotalScore(getArray(questionType.value)) }}分）</div>
            <div class="menuinfo_infolist clearfix" v-show="getArray(questionType.value).length > 0">
              <div v-for="(item, index) in getArray(questionType.value)" :key="index" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
                <span class="question-type" :class="questionType.value">{{ questionType.name }}</span>
                <el-tooltip effect="dark" :content="stripHtmlTags(item.question)" placement="top-start">
                  <span class="question-content"> {{ stripHtmlTags(item.question) }}</span>
                </el-tooltip>
                <div class="question-operate">
                  <span class="question-operate__details" @click.stop="openList(item, index)">
                    <i class="el-icon-document"></i>
                    详情</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="questionContent">
          <div class="topform_cont clearfix"></div>
          <div class="menulist_cont">
            <div class="menulist_conttop">
            <el-button type="primary" @click="closeCheck" v-if="type == 'view'">返回</el-button>
            <el-button type="primary" @click="openCheck" :disabled="addLoading" v-if="type == 'edit'">批阅</el-button>
          </div>
          
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { paperUserRecordDetail, paperUserRecordCorrect } from '@/api/correct.js'
import { selectTeacherById } from '@/api/teacher.js'
import questionview from '@/views/volume/correct/view/index'
import { questionTypes } from '@/filters'
import { stripHtmlTags } from '@/utils'

export default {
  components: {
    questionview
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    var checkArray = (rule, value, callback) => {
      if (!value || value.length <= 0) {
        return callback(new Error('请添加试题'))
      } else {
        return callback()
      }
    }
    return {
      baseUrl: window.config.VUE_FILE_BASE_PATH,
      paperUserRecordId: this.$route.query.paperUserRecordId,
      type: this.$route.query.type || 'view',
      labelwidth: '110px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: null,
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      SINGLE: [],
      MULTIPLE: [],
      JUDGE: [],
      COMPLETION: [],
      SHORT: [],
      COMPATIBILITY: [],
      COMPREHENSIVE: [],
      viewIndex: -1,
      viewType: -1,
      beforeBtn: true,
      afterBtn: true,
      topform: {
        paperName: '',
        sumNumll: 0,
        paperScore: 0,
        questionExerciseReqList: [],
        oldList: []
      },
      socorRegular: [],
      form: {
        questionScore: '',
        majorId: '',
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: []
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入考试名称'
          }
        ],
        questionScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入分数'
          }
        ],
        questionExerciseReqList: [
          {
            validator: checkArray
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      //试题选择
      questionMajorId: '',
      questionSeleType: '',
      questionSele: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      selections: [],
      selectionIndex: 0,
      tableshow: false,
      addLoading: false
    }
  },
  computed: {
    questionTypeTotalScore() {
      return (data) => {
        let totalScore = 0
        data.forEach((question) => {
          totalScore += question.list.reduce((pre, next) => {
            return pre + next.score
          }, 0)
        })
        return totalScore
      }
    },
    showQuestionType() {
      return (val) => {
        return questionTypes.filter((item) => item.value == val)[0].name
      }
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    stripHtmlTags,
    openCheck() {
      if (this.topform.sumNumll > 0) {
        this.$confirm('还有未批改的试题，是否保存？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.openSendScore()
        })
      } else {
        this.openSendScore()
      }
    },
    openSendScore() {
      var data = {
        paperUserRecordId: this.paperUserRecordId,
        paperScore: this.topform.paperScore,
        paperUserAnswerCorrectReqs: []
      }
      var questionTypes = this.questionTypes
      var questionExerciseReqList = []
      questionTypes.map((item) => {
        questionExerciseReqList = questionExerciseReqList.concat(this[item.value])
      })
      questionExerciseReqList.map((item, index) => {
        if (item.list && item.list.length > 0) {
          item.list.map((it) => {
            data.paperUserAnswerCorrectReqs.push({
              paperUserAnswerId: it.paperUserAnswerId,
              questionScore: it.questionScore || 0,
              questionCorrectContent: it.questionCorrectContent || ''
            })
          })
        }
      })
      console.log(data)
      paperUserRecordCorrect(data).then((res) => {
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: '批阅成功！'
          })
          this.closeCheck()
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    beforeCorrect(data) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      this[viewType][viewIndex].list[0].questionScore = data.questionScore
      this[viewType][viewIndex].list[0].questionCorrectContent = data.questionCorrectContent
      if (this[viewType][viewIndex].splited) {
        var allIndexId = this[viewType][viewIndex].allIndexId
        var index = allIndexId.split('-')[0]
        var listIndex = allIndexId.split('-')[1]
        this.topform.oldList[index].list[listIndex].questionScore = data.questionScore
        this.topform.oldList[index].list[listIndex].questionCorrectContent = data.questionCorrectContent
      }
      this.sumSelection()
    },
    sumSelection() {
      var sumNumll = 0
      var paperScore = 0
      var questionExerciseReqList = []
      var questionTypes = this.questionTypes
      questionTypes.map((item) => {
        questionExerciseReqList = questionExerciseReqList.concat(this[item.value])
      })
      questionExerciseReqList.map((item, index) => {
        if (item.list && item.list.length > 0) {
          item.list.map((it) => {
            var questionScore = it.questionScore
            questionScore = Number(questionScore)
            if (!isNaN(questionScore)) {
              paperScore += questionScore
            } else {
              sumNumll++
            }
          })
        }
      })
      this.topform.questionExerciseReqList = questionExerciseReqList
      this.topform.paperScore = paperScore
      this.topform.sumNumll = sumNumll
    },
    openList(item, index) {
      var socorRegular = this.socorRegular
      this.questionType = null
      this.viewIndex = index
      this.viewType = item.questionType
      this.beforeBtn = true
      this.afterBtn = true
      var questionArr = ['', '', '', '']
      var optionsArr = ['', '', '', '']
      var answers = []
      if (item.questionType == 'COMPATIBILITY') {
        this.splited = true
        try {
          var questionObject = JSON.parse(item.question)
          questionArr = Object.values(questionObject)
        } catch (err) {}
      } else {
        this.splited = false
      }
      if (item.options) {
        try {
          var optionObject = JSON.parse(item.options)
          optionsArr = Object.values(optionObject)
        } catch (err) {}
      }
      this.form = Object.assign({}, item, {
        questionArr,
        optionsArr,
        answers: item.answer ? item.answer.split('') : []
      })
      this.$nextTick(() => {
        this.questionType = this.form.questionType

        var typeIndex = ''
        this.questionTypes.map((item, index) => {
          if (item.value == this.viewType) {
            typeIndex = index
          }
        })
        if (this.viewIndex == 0) {
          var newInfo = this.getBefore(typeIndex, -1)
          if (!newInfo || !newInfo.hasitem) {
            this.beforeBtn = false
          }
        } else if (this.viewIndex == this[this.viewType].length - 1) {
          var newInfo = this.getBefore(typeIndex, 1)
          if (!newInfo || !newInfo.hasitem) {
            this.afterBtn = false
          }
        }
      })
    },
    pushList(item) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      if (viewType == -1 || (viewType != -1 && item.questionType != viewType)) {
        this[item.questionType].push(item)
        if (viewType != -1) {
          this[viewType].splice(viewIndex, 1)
        }
      } else {
        this[item.questionType][viewIndex] = item
      }
      this.sumSelection()
    },
    getArray(questionType) {
      return this[questionType]
    },
    jumpQuestion(type) {
      this.beforeBtn = true
      this.afterBtn = true
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      var questionTypes = this.questionTypes
      var typeIndex = ''
      questionTypes.map((item, index) => {
        if (item.value == viewType) {
          typeIndex = index
        }
      })
      if (type < 0) {
        if (viewIndex > 0) {
          viewIndex--
          this.openList(this[viewType][viewIndex], viewIndex)
          if (viewIndex == 0) {
            var newInfo = this.getBefore(typeIndex, type)
            if (!newInfo || !newInfo.hasitem) {
              this.beforeBtn = false
            }
          }
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = newInfo.theArray.length - 1
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.beforeBtn = false
          }
        }
      }
      if (type > 0) {
        if (viewIndex < this[viewType].length - 1) {
          viewIndex++
          this.openList(this[viewType][viewIndex], viewIndex)
          if (viewIndex == this[viewType].length - 1) {
            var newInfo = this.getBefore(typeIndex, type)
            if (!newInfo || !newInfo.hasitem) {
              this.afterBtn = false
            }
          }
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = 0
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.afterBtn = false
          }
        }
      }
    },
    getBefore(typeIndex, type) {
      var questionTypes = this.questionTypes
      if (type < 0) {
        if (typeIndex <= 0) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex--
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
      if (type > 0) {
        if (typeIndex >= questionTypes.length - 1) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex++
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
    },
    getInfo() {
      paperUserRecordDetail({
        paperUserRecordId: this.paperUserRecordId
      }).then((res) => {
        this.topform = res.data
        var questionExerciseReqList = res.data.questions
        questionExerciseReqList.map((item, index) => {
          item.list = item.questionAnswers || []
          var indexId = this[item.questionType].length + 1
          if (item.questionType != 'COMPATIBILITY' && item.questionType != 'COMPREHENSIVE') {
            var newItem = Object.assign({}, item)
            newItem.indexId = indexId
            newItem.allIndexId = index
            this.pushList(newItem)
          } else {
            if (item.questionAnswers && item.questionAnswers.length > 0) {
              item.questionAnswers.map((it, ide) => {
                var newItem = Object.assign({}, item)
                newItem.indexId = indexId + '-' + (ide + 1)
                newItem.allIndexId = index + '-' + ide
                newItem.list = [it]
                newItem.splited = true
                this.pushList(newItem)
              })
            }
          }
        })
        this.topform.oldList = questionExerciseReqList
        this.$nextTick(() => {
          //打开第一个
          if (questionExerciseReqList && questionExerciseReqList.length > 0) {
            var openFirst = false
            this.questionTypes.map((item) => {
              if (this[item.value] && this[item.value].length > 0) {
                if (!openFirst) {
                  this.openList(this[item.value][0], 0)
                  openFirst = true
                }
              }
            })
          }
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (this.paperUserRecordId) {
          this.getInfo()
        }
      })
    },
    closeCheck() {
      this.$router.push('/volume')
      // var view = this.$route
      // this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
      //   if (this.isActive(view)) {
      //     this.toLastView(visitedViews, view)
      //   }
      // })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.student-grade {
  .student-grade__body {
    display: flex;
    justify-content: space-between;
    padding: 20px 30px;
    .question-info-view {
      position: relative;
      width: 964px;
      height: 100%;
      .menuinfo {
        position: relative;
        width: 100%;
        height: 670px;
        padding: 20px 25px;
        background: #ffffff;
        border-radius: 20px 20px 20px 20px;
        border: 1px solid #dddee3;
        overflow: auto;
        &::-webkit-scrollbar {
          background: transparent;
        }
        & > .topic_des {
          position: absolute;
          left: 100px;
          top: 20px;

          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 26px;
          border-radius: 4px 4px 4px 4px;

          font-family: PingFang SC;
          font-weight: 400;
          font-size: 12px;
          &.SINGLE {
            background: #3d8aff;
            color: #fff;
          }
          &.MULTIPLE {
            background: #2eb2ff;
            color: #fff;
          }
        }
      }
      .button-group {
        position: absolute;
        left: 50%;
        bottom: 10px;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 93px;
          height: 50px;
          background: #274e6a;
          border-color: #274e6a;
          border-radius: 63px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          &:last-of-type {
            margin-left: 20px;
          }
          &--info {
            color: #274e6a;
            background: #fff;
          }
          &.is-disabled {
            opacity: 0.6;
          }
        }
      }
    }
    .student-answer-panel {
      flex: 1;
      margin-left: 30px;
      .student-info {
        display: flex;
        align-items: center;
        width: 100%;
        height: 90px;
        padding: 0 30px;
        background: #f4f7ff;
        border-radius: 10px 10px 10px 10px;
        border: 1px solid #dddee3;
        .student-info__photo {
          display: flex;
          align-items: center;
          margin-right: 26px;
          .avatar {
            position: relative;
            img {
              width: 52px;
              height: 52px;
              border-radius: 50%;
              object-fit: cover;
            }
            .sexIcon {
              position: absolute;
              bottom: 3px;
              right: -2px;
              width: 18px;
              height: 18px;
            }
          }
          .name {
            display: flex;
            flex-direction: column;
            margin-left: 8px;
            & > span:first-of-type {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 18px;
              color: #333333;
            }
            & > span:last-of-type {
              margin-top: 8px;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 16px;
              color: #999999;
            }
          }
        }
        .student-info__item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          & > span:first-of-type {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            i {
              font-style: normal;
              font-size: 12px;
            }
          }
          & > span:last-of-type {
            margin-top: 8px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 18px;
            color: #274e6a;
          }
        }
        .border_item {
          border-left: 1px dashed #d1e9ff;
        }
        .student-info__item.small {
          width: 120px;
        }
        .student-info__item.large {
          flex: 1;
        }
      }
      .menuinfo_list {
        height: calc(100% - 110px);
        margin-top: 20px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #dddee3;
        transition: all 0.3s ease;
        overflow: auto;
        .menuinfo_top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 60px;
          padding: 0 30px;
          background: #fafafa;
          border-bottom: 1px solid #dddee3;
          .menuinfo-title {
            position: relative;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #000000;
            &::before {
              content: '';
              position: absolute;
              left: -10px;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 20px;
              background: #274e6a;
            }
          }
          .menuinfo-total {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #000000;
            margin-left: 35px;
          }
        }
        .menuinfo_info {
          padding: 20px;
          .menuinfo_infoname {
            margin-bottom: 20px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 20px;
            color: #333333;
          }
          .menuinfo_infolist {
            .question_answer {
              position: relative;
              display: flex;
              align-items: center;

              width: 100%;
              height: 60px;
              padding: 0 20px;
              margin-bottom: 16px;

              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #dddee3;
              &.doing {
                background: rgba($color: #3d8aff, $alpha: 0.1);
              }
              .question-type {
                width: 60px;
                height: 26px;
                line-height: 26px;
                border-radius: 4px 4px 4px 4px;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #ffffff;
                text-align: center;
                &.SINGLE {
                  background: #3d8aff;
                }
                &.MULTIPLE {
                  background: #2eb2ff;
                }
                &.JUDGE {
                  background: #009900;
                }
              }
              .question-content {
                margin-left: 20px;
                max-width: 65%;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 18px;
                color: #000000;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .question-operate {
                position: absolute;
                right: 20px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 16px;
                &__details {
                  color: #3d8aff;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
}

// .studentGradeDetais {
//   padding: 0 25px;
//   .pageTop {
//     position: relative;
//     margin-top: 30px;
//     margin-bottom: 22px;
//     .goBack {
//       position: absolute;
//     }
//     .title {
//       font-size: 25px;
//       text-align: center;
//     }
//   }
//   .gradeInfo {
//     width: 100%;
//     border: 1px solid #c7cbd0;
//     .studentInfo {
//       padding: 30px 20px;
//       border-bottom: 1px solid #c7cbd0;
//       .title {
//         font-size: 20px;
//         font-family:
//           PingFang SC,
//           PingFang SC;
//         font-weight: bold;
//         color: #333333;
//       }
//       .studentContent {
//         display: flex;
//         align-items: center;
//         justify-content: space-around;
//         margin-top: 24px;
//         width: 100%;
//         height: 90px;
//         background: #f4f7ff;
//         border-radius: 4px;
//         .student {
//           display: flex;
//           align-items: center;
//           .avatar {
//             position: relative;
//             img {
//               width: 52px;
//               height: 52px;
//               border-radius: 50%;
//               object-fit: cover;
//             }
//             .sexIcon {
//               position: absolute;
//               bottom: 3px;
//               right: -2px;
//               width: 18px;
//               height: 18px;
//             }
//           }
//           .name {
//             display: flex;
//             flex-direction: column;
//             margin-left: 15px;
//             & > span:first-of-type {
//               font-size: 18px;
//               font-family:
//                 PingFang SC,
//                 PingFang SC;
//               font-weight: 500;
//               color: #333333;
//             }
//             & > span:last-of-type {
//               margin-top: 8px;
//               font-size: 16px;
//               font-family:
//                 PingFang SC,
//                 PingFang SC;
//               font-weight: 500;
//               color: #999999;
//             }
//           }
//         }
//         .statistics_item {
//           display: flex;
//           flex-direction: column;
//           justify-content: center;
//           align-items: center;
//           min-width: 146px;

//           & > span:first-of-type {
//             font-size: 16px;
//             font-family:
//               PingFang SC,
//               PingFang SC;
//             font-weight: 500;
//             color: #333333;
//             i {
//               font-style: normal;
//               font-size: 12px;
//             }
//           }
//           & > span:last-of-type {
//             margin-top: 14px;
//             font-size: 18px;
//             font-family:
//               PingFang SC,
//               PingFang SC;
//             font-weight: bold;
//             color: #1890ff;
//           }
//         }
//         .border_item {
//           position: relative;
//           &::after {
//             content: '';
//             position: absolute;
//             right: -52px;
//             height: 47px;
//             border-left: 1px dashed #d6ebff;
//           }
//         }
//       }
//     }
//     .questionContent {
//       display: flex;
//     }
//   }

//   .topform_cont {
//     .topicinfo_top {
//       border-bottom: none;
//     }
//   }
// }
</style>
