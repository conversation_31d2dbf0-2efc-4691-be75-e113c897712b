.div-top {
  color: rgb(32, 29, 29);
  border-bottom: 1px solid rgba(32, 30, 30, 0.432);
  height: 70px;
  font-size: 20px;
  padding-top: 30px;
  padding-left: 50px;
  margin-bottom: 20px;
}

.avataruploads {
  height: 150px;
  width: 150px;
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  line-height: 150px;
  text-align: center;
  font-size: 22px;
  position: relative;
}

.avataruploads:hover {
  border: 1px solid #5cb6ff;
  color: #5cb6ff;
}

.avatarupcover {
  position: absolute;
  left: 0;
  top: 0;
  height: 150px;
  width: 150px;
  z-index: 1;
  cursor: pointer;
  line-height: 150px;
  text-align: center;
  font-size: 22px;
}

.avatarupcover:hover {
  color: #5cb6ff;
}

.avataruploads:hover .avatarupcover-change {
  display: block;
}

.avataruploads img {
  height: 150px;
  width: 150px;
}

.imagePreview:hover {
  cursor: pointer;
}

.eltreeBox {
  width: 100%;
  height: auto;
  max-height: 400px;
  overflow-y: auto;
}

.search_label {
  margin: 0 0 0 20px;
  display: inline-block;
}

.user_lable {
  width: 100%;
  height: 40px;
  text-align: center;
  background: rgba(49, 188, 122, 0.9);
  color: #ffffff;
  line-height: 40px;
}

.user_left {
  width: 100%;
  height: calc(100vh - 125px);
  min-height: 400px;
  border: 1px solid #cccccc;
  margin: 15px;
  overflow-y: auto;
}

.user_leftitem {
  width: 90%;
  margin: 5px auto;
  height: 35px;
  line-height: 35px;
  font-size: 15px;
  text-indent: 1em;
  cursor: pointer;
  border-radius: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user_leftitem.active,
.user_leftitem:hover {
  background: #31bc7a;
  color: #ffffff;
}

.user_right {
  width: calc(100% - 30px);
  height: calc(100vh - 125px);
  min-height: 400px;
  border: 1px solid #cccccc;
  margin: 15px;
  overflow-y: auto;
}

.user_alert {
  width: 100%;
  height: 40px;
  text-align: center;
  background: #ff4d51;
  color: #ffffff;
  line-height: 40px;
}

.mini_dialog {
  .el-dialog__body {
    padding: 15px 20px;
  }
}

.topic_question {
  min-height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  box-sizing: border-box;
  padding: 0 120px 0 0;
  color: #000000;
}

.topic_selection {
  width: 100%;
  height: auto;
  line-height: 30px;
  font-size: 14px;
}

.topic_selectionit {
  box-sizing: border-box;
  padding: 0 0 0 30px;
  position: relative;
}

.topic_question p,
.topic_question div,
.topic_answer p,
.topic_answer div,
.topic_selectionit p,
.topic_selectionit div {
  display: inline-block !important;
  margin: 0;
}

.topic_selectionitall {
  padding: 0 0 0 0;
}

.topic_answermr {
  margin: 15px 0 0 0;
}

.topic_answer {
  height: auto;
  line-height: 26px;
  font-size: 14px;
  padding: 0 0 0 10px;
}

.topic_des {
  height: auto;
  line-height: 26px;
  font-size: 18px;
  margin: 10px 0;
}

.topicinfo {
  box-sizing: border-box;
  padding: 20px;
}

.small_input .ql-editor {
  height: auto;
  min-height: 100px;
  max-height: 200px;
}

.small_input .el-form-item {
  margin: 0 0 20px 0;
  position: relative;
}

.small_inputbtn {
  box-sizing: border-box;
  padding: 20px;
}

.small_input .el-tabs__item {
  height: 36px !important;
  line-height: 36px !important;
}

.mainColor {
  color: #1482f0;
}

.right_name {
  height: 40px;
  float: right;
  line-height: 40px;
  margin: 0 15px 0 0;
}

.menuinfo {
  box-sizing: border-box;
  padding: 20px;
  width: 100%;
  float: left;
}

.menulist_cont {
  width: 400px;
  float: right;
}

.menuinfo_list {
  box-sizing: border-box;
  width: 390px;
  margin: 20px 20px 15px 0;
  float: left;
  border: 1px solid #e5e5e0;
  min-height: 100px;
  padding: 0 0 25px 0;
}

.topform_cont {
  width: calc(100% - 400px);
  float: left;
}

.topicinfo_top {
  padding: 20px 20px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #e4e4e4;
}

.menuinfo_top {
  box-sizing: border-box;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #e4e4e4;
  font-weight: bold;
  span {
    float: right;
  }
}

.menuinfo_info {
  width: 100%;
  box-sizing: border-box;
  padding: 15px 15px 0 15px;
  height: auto;
  max-height: 500px;
  overflow-y: auto;
}
.menuinfo_info::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background: #999999;
  border-radius: 5px;
}

.menuinfo_info::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 5px;
}

.menuinfo_info::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #999999;
}
.menuinfo_infoname {
  height: 30px;
  line-height: 30px;
  font-size: 15px;
  margin: 0 0 15px 0;
}

.question_answer {
  height: 40px;
  width: 40px;
  background: #ffffff;
  margin: 0 18px 10px 0;
  float: left;
  cursor: pointer;
  text-align: center;
  line-height: 40px;
  text-align: center;
  box-sizing: border-box;
  border: 1px solid #e5e5e0;
  position: relative;
}

.question_answer.over {
  background: #409eff;
  color: #ffffff;
}

.question_answer.doing {
  line-height: 38px;
  border: 1px solid #409eff;
}

.question_delete {
  position: absolute;
  right: -8px;
  top: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ff0000;
  color: #ffffff;
  cursor: pointer;
  line-height: 16px;
  text-align: center;
  font-size: 12px;
  border: 1px solid #ff0000;
}

.question_delete:hover {
  border: 1px solid #ffffff;
}

.menuinfo_botttom {
  box-sizing: border-box;
  padding: 20px 20px 0 20px;
  width: 100%;
  float: left;
}

.menulist_conttop {
  height: 79px;
  box-sizing: border-box;
  padding: 20px 20px 0 0;
  text-align: right;
  border-bottom: 1px solid #e4e4e4;
}

.simulation_cont {
  box-sizing: border-box;
  padding: 15px;
}

.simulation_all {
  font-size: 19px;
  font-weight: bold;
}

.simulation_info {
  margin: 15px 0;
}

.top_info {
  line-height: 45px;
  padding: 20px;
  box-sizing: border-box;
  border-bottom: 1px solid #cccccc;
}

.info_label {
  margin: 0 0 0 35px;
  display: inline-block;
}
.tip_btn {
  height: 36px;
  width: 36px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  line-height: 36px;
  font-size: 36px;
  cursor: pointer;
  margin: 0 10px;
  display: inline-block;
  color: #e6a23c;
  overflow: hidden;
  float: right;
}

.editor_box > p {
  margin: 0;
  //  display: inline-block;
}

.editor_box img {
  max-width: 100%;
}

.list_question span p,
.list_question span div {
  display: inline-block !important;
  margin: 0;
}

.login_container {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.login_containerback {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.login_containericon {
  position: absolute;
  left: 335px;
  top: 355px;
}

.login_title {
  position: absolute;
  top: 120px;
  left: 0;
  text-align: center;
  width: 100%;
}

.login_title1 {
  height: 40px;
  font-size: 30px;
  font-family:
    Source Han Serif CN-SemiBold,
    Source Han Serif CN;
  font-weight: 600;
  color: #ffffff;
  line-height: 40px;
  letter-spacing: 1px;
  margin: 0 0 4px 0;
}

.login_title2 {
  height: 16px;
  font-size: 11px;
  font-family:
    Source Han Serif CN-ExtraLight,
    Source Han Serif CN;
  font-weight: 200;
  color: #ffffff;
  line-height: 16px;
  letter-spacing: 0.5px;
}

.login_form {
  position: absolute;
  right: 350px;
  top: 305px;
  width: 460px;
  height: 455px;
}

.login_formback {
  position: absolute;
  right: 0;
  top: 0;
  width: 460px;
  height: 455px;
}

.login_formcont {
  width: 460px;
  height: 455px;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 55px 45px 0 45px;
}

.login_formtitle {
  width: 100%;
  height: 30px;
  line-height: 30px;
  margin: 0 0 50px 0;
  text-align: center;
  font-size: 20px;
  font-family:
    Source Han Serif CN-Regular,
    Source Han Serif CN;
  font-weight: 400;
  color: #ffffff;
  letter-spacing: 1px;
}

.login_formicon {
  color: #1890ff;
  font-size: 16px;
  line-height: 42px;
}

.login_form .el-input__prefix {
  width: 30px;
  text-align: center;
  left: 0;
}

.login_form .el-form-item {
  margin: 0 0 30px 0;
}

.login_formbtn {
  width: 80%;
  height: 40px;
  font-size: 15px;
  font-weight: 700;
  margin: 20px 10% 0;
}

.login_bottom {
  width: 100%;
  position: absolute;
  left: 0;
  height: 50px;
  text-align: center;
  bottom: 0;
  font-size: 14px;
  font-family:
    Source Han Serif CN-Light,
    Source Han Serif CN;
  font-weight: 300;
  color: #ffffff;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.correct_complain .el-dialog__body {
  padding: 10px 20px;
}
