<template>
  <div>
    <div class="div-top">
      <span>设备管理</span>
    </div>
    <div style="margin: 10px">
      <el-form ref="form" :model="queryInfo" inline label-width="80px">
        <el-form-item label="设备编号:">
          <el-input v-model="queryInfo.code" placeholder="请输入设备编号" maxlength="40" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>
        <el-form-item label="设备名称:">
          <el-input v-model="queryInfo.name" placeholder="请输入设备名称" maxlength="40" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>
        <el-form-item label="设备型号:">
          <el-input v-model="queryInfo.model" placeholder="请输入设备型号" maxlength="40" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="small" @click="getList">搜索</el-button>
          <el-button type="primary" icon="el-icon-circle-plus-outline" @click="addDialog = true">添加设备</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column prop="code" label="设备编号" width="width" align="center"> </el-table-column>
        <el-table-column prop="name" label="设备名称" width="width" align="center"> </el-table-column>
        <el-table-column prop="model" label="设备型号" width="width" align="center"> </el-table-column>
        <el-table-column prop="macAddress" label="绑定电脑地址" width="width" align="center"> </el-table-column>
        <el-table-column prop="remark" label="备注" width="width" align="center"> </el-table-column>
        <el-table-column prop="realName" label="创建人" width="width" align="center"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
        <el-table-column label="操作" width="width" align="center">
          <template v-slot="{ row }">
            <el-button type="primary" size="small" @click="edit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="remove(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>
    <!-- 新增、修改设备 -->
    <AddCaseDevice ref="AddCaseDevice" :addDialog.sync="addDialog" @success="getList" />
  </div>
</template>
<script>
import { caseDeviceList, caseDeviceRemove } from '@/api/caseDevice'
import AddCaseDevice from './add.vue'
export default {
  name: 'caseDevice',
  components: {
    AddCaseDevice
  },
  data() {
    return {
      queryInfo: {
        name: null,
        model: null,
        code: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      addDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await caseDeviceList(this.queryInfo)
      console.log(data.list)
      this.list = data.list
      this.total = data.total
    },
    edit(row) {
      this.$refs['AddCaseDevice'].form = { ...row }
      this.addDialog = true
    },
    remove(row) {
      this.$confirm('确定删除该设备吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseDeviceRemove({ id: row.deviceId })
          this.getList()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style scoped lang="scss"></style>
