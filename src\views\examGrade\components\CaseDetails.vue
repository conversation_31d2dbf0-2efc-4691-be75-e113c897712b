<template>
  <div>
    <el-dialog custom-class="caseDetailsDialog" center top="80px" :visible="showDialog" @close="close" @open="getList">
      <template v-slot:title> <div>病例详情</div> </template>
      <div>
        <div class="caseData" v-if="caseInfo">
          <div class="caseItem">
            <div class="caseItem_header">病例{{ caseInfo.index + 1 }}</div>
            <div class="caseItem_body">
              <Avatar :age="caseInfo.age" :sex="caseInfo.sex" />
              <div class="caseName">
                <div>{{ caseInfo.name }}</div>
                <span>{{ caseInfo.real_name }}</span>
                <span>{{ caseInfo.age }}岁</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 搜索 -->
        <el-row class="searchRow" type="flex" align="middle" justify="space-between">
          <el-form label-width="80px" inline>
            <el-form-item label="姓名" label-width="80px">
              <el-input class="studentName" v-model="queryInfo.studentName" size="small" placeholder="考生姓名"></el-input>
            </el-form-item>
            <el-form-item label="选择班级">
              <el-select class="selectClbum" v-model="queryInfo.clbumId" placeholder="选择班级" @focus="getClbum" @change="getList">
                <el-option v-for="item in clbumList" :key="item.clbumId" :label="item.clbumName" :value="item.clbumId"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否及格">
              <el-select class="select-isPass" v-model="queryInfo.isPass" @change="getList">
                <el-option label="是" :value="1"> </el-option>
                <el-option label="否" :value="0"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态" label-width="50px">
              <el-select class="select-state" v-model="queryInfo.state" placeholder="状态" @change="getList">
                <el-option label="未参考" :value="1"> </el-option>
                <el-option label="考试中" :value="2"> </el-option>
                <el-option label="已结束" :value="3"> </el-option>
                <el-option label="异常" :value="4"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="提交时间" style="margin-top: 20px">
              <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
            </el-form-item>

            <el-form-item class="search-button-group" style="margin-top: 20px">
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="primary" plain @click="reset">重置</el-button>
              <el-button type="primary" plain @click="exportExcel">导出Excel</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-table :data="list" style="width: 100%" header-cell-class-name="tableHeader" cell-class-name="tableCell" @sort-change="sortChange">
          <el-table-column align="center" label="序号" width="70" type="index"> </el-table-column>
          <el-table-column align="center" prop="name" label="学生信息" width="240">
            <template v-slot="{ row }">
              <span>{{ row.name }} ({{ row.sex === 'F' ? '女' : '男' }}) {{ row.loginName }} {{ row.clbumName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="score" label="总得分" width="width" sortable="custom"> </el-table-column>
          <el-table-column align="center" prop="time" label="总用时" width="width" sortable="custom">
            <template v-slot="{ row }">
              <span>{{ parseFloat(parseFloat(row.time / 60).toFixed(1)) }}分钟</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="state" label="状态" width="90">
            <template v-slot="{ row }">
              <el-tag :type="rowStateStyle(row)">{{ row.state === 1 ? '未参考' : row.state === 2 ? '考试中' : row.state === 3 ? '已结束' : '异常' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否及格" width="90">
            <template v-slot="{ row }">
              <span>{{ row.isPass ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="提交时间" width="width">
            <template v-slot="{ row }">
              <span>{{ row.endTime | formatDate('yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="time" label="操作" width="80">
            <template v-slot="{ row }">
              <el-button class="student-detalis_button" type="text" @click="studentDetails(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination class="pagination" style="text-align: center; margin-top: 15px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next" :total="total" @size-change="getList" @current-change="getList"> </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseExamExamStudentList, caseExamClbumList, caseExamExportStudentList } from '@/api/caseExam'
import { formatDate } from '@/filters'
import Avatar from '@/views/casePractise/components/Avatar'

export default {
  name: '',
  components: {
    Avatar
  },
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },

  data() {
    return {
      caseInfo: null,
      caseId: null,
      queryInfo: {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        clbumId: null, // 班级id
        sort: null, // 1 总分数 2 总用时
        orderby: null, // 1 倒叙 2 正序
        pageNum: 1,
        pageSize: 5
      },
      clbumList: [],
      time: null,
      total: 0,
      list: []
    }
  },
  created() {},
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    async getList() {
      const { data } = await caseExamExamStudentList({ ...this.queryInfo, caseId: this.caseId })
      this.total = data.total
      this.list = data.list
    },
    reset() {
      this.queryInfo = {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 5
      }
      this.getList()
    },
    async exportExcel() {
      const { data } = await caseExamExportStudentList({ ...this.queryInfo, caseId: this.caseId })
      console.log(data)
      if (!data.length) return false
      // 生成包含动态病例键的模板
      const headers = this.generateDynamicTemplate(data[0])
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        // var tHeader = ['考试名称', '考试开始时间', '考试结束时间', '考试限时', '成绩公布时间', '考试人数', '考试病例', '总分', '及格分', '状态', '创建人', '创建时间']
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '考生成绩列表' // 非必填
        })
      })
    },
    generateDynamicTemplate(data) {
      // 基础模板
      let template = {
        姓名: 'name',
        性别: 'sex',
        学号: 'login_name',
        班级: 'clbum_name',
        提交时间: 'endTime',
        总得分: 'score',
        总用时: 'time'
      }
      // 检查数据中的动态“病例”键
      const dynamicCases = Object.keys(data).filter((key) => key.startsWith('病例'))

      // 将这些动态键添加到模板中
      dynamicCases.forEach((caseKey) => {
        template[caseKey] = caseKey // 例如，'病例1': '病例1'
      })

      // 在动态病例之后添加
      template['状态'] = 'stateName'
      template['是否及格'] = 'isPass'

      return template
    },
    // 处理导出数据格式
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (key === '性别') {
            item[headers[key]] = item[headers[key]] === 'M' ? '男' : '女'
          } else if (key === '是否及格') {
            item[headers[key]] = item[headers[key]] ? '是' : '否'
          } else if (key === '提交时间') {
            item[headers[key]] = formatDate(item[headers[key]], 'yyyy-MM-dd')
          }
          return item[headers[key]]
        })
      })
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    async getClbum() {
      const { data } = await caseExamClbumList({ examId: this.$route.params.id })
      this.clbumList = data
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? '' : row.state === 3 ? 'warning' : 'danger'
      return type
    },
    studentDetails(row) {
      this.$router.push(`/examGrade/student/${row.examStudentId}`)
    },
    sortChange(val) {
      if (val.prop === 'score') {
        this.queryInfo.sort = 1
      } else if (val.prop === 'time') {
        this.queryInfo.sort = 2
      }
      if (val.order) {
        this.queryInfo.orderby = val.order === 'ascending' ? 2 : 1
      } else {
        this.queryInfo.orderby = null
      }
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .caseDetailsDialog {
    width: 1040px;
    height: 790px;
    background: #ffffff;
    border-radius: 30px 30px 30px 30px;
    .el-dialog__header {
      // padding: 0;
      & > div {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
      }
      .el-dialog__headerbtn {
        font-size: 24px;
        color: #666666;
        font-weight: 600;
      }
    }
    .el-dialog__body {
      padding: 30px;
    }
  }
  .searchRow {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .el-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
    .el-form-item__label {
      font-size: 16px;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      color: #121212;
    }
    .studentName,
    .selectClbum,
    .select-isPass,
    .select-state {
      .el-input {
        width: 204px;
        height: 40px;
        .el-input__inner {
          width: 100%;
          height: 100%;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #dcdfe6;
        }
      }
    }
    .select-isPass > .el-input {
      width: 111px;
    }
    .select-state > .el-input {
      width: 111px;
    }
    .el-date-editor {
      width: 330px;
      height: 40px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #dcdfe6;
      .el-range__close-icon {
        line-height: 33px;
      }
    }
    .el-range-editor--small .el-range__icon {
      line-height: 33px;
    }
    .el-date-editor .el-range-separator {
      line-height: 33px;
      width: 10%;
    }

    .search-button-group {
      position: relative;
      left: 215px;
      .el-button {
        width: 100px;
        height: 44px;
        line-height: 44px;
        padding: 0;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #dcdfe6;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        text-align: center;
        &--primary {
          background: #274e6a;
          border-color: #274e6a;
          color: #fff;
        }
      }
    }
    .el-button--medium {
      width: 100px;
      height: 44px;
      border-radius: 8px;
    }
  }
  .el-table {
    border: 1px solid #dfe6ec;
    border-bottom: none;
    margin-top: 17px;
    .tableHeader {
      height: 40px;
      background: #f4f7ff;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #333333;
    }
    .tableCell {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #6e6f6d;
    }

    .student-detalis_button {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #274e6a;
    }
  }
}
.caseData {
  .caseItem {
    display: inline-block;
    min-width: 261px;
    height: 129px;
    padding-right: 10px;
    margin-right: 16px;
    background: #f4f6f8;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e0e4e8;
    cursor: pointer;
    .caseItem_header {
      padding: 10px 0 10px 20px;
      border-bottom: 1px solid #e0e4e8;
      font-size: 18px;
      font-family: PingFang SC;
      color: #274e6a;
    }
    .caseItem_body {
      display: flex;
      align-items: center;
      padding-top: 18px;
      padding-left: 20px;
      .caseName {
        margin-left: 8px;
        & > div {
          margin-bottom: 5px;
          font-size: 20px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #000000;
        }
        span {
          font-size: 16px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #666666;
          &:last-of-type {
            margin-left: 3px;
          }
        }
      }
    }
  }
}
</style>
