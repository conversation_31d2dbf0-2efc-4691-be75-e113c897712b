// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-image-viewer__close {
  color: #ffffff;
}

.el-button--primary.is-plain,
.el-button--primary.is-plain:hover,
.el-button--primary.is-plain:focus {
  background: #fff;
  border-color: #dcdfe6;
  color: #333333;
}

.caseConfirm {
  position: relative;
  padding: 30px;
  min-width: 450px;
  max-width: 1100px;
  width: inherit;
  border-radius: 20px;
  .el-message-box__header {
    position: static;
    padding: 0;
    .el-message-box__title {
      span {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #000000;
        text-align: center;
      }
    }
    .el-message-box__headerbtn {
      right: 15px;
      top: 15px;
      .el-icon-close {
        font-size: 24px;
      }
    }
  }
  .el-message-box__content {
    padding: 0;
    padding-top: 50px;
    .el-message-box__message {
      p {
        font-family: PingFang SC;
        font-size: 24px;
        color: #666666;
        line-height: 24px;
        text-align: center;
      }
    }
  }
  .el-message-box__btns {
    padding-top: 50px;
    & > .el-button {
      &:first-of-type,
      &:last-of-type {
        padding: 0;
        width: 93px;
        height: 50px;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        text-align: center;
        color: #274e6a;
        line-height: 50px;
        cursor: pointer;
        &:hover {
          background: rgba($color: #fff, $alpha: 0.8);
        }
      }
      &:last-of-type {
        width: 132px;
        margin-left: 12px;
        background: #274e6a;
        border: none;
        color: #ffffff;
        &:hover {
          background: rgba($color: #274e6a, $alpha: 0.8);
        }
      }
    }
  }
}
