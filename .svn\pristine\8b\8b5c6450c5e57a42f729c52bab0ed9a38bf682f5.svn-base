<template>
  <div class="casePhoto">
    <template>
      <template v-if="sex === 'M'">
        <img v-if="age >= 35" src="@/assets/case/man_2.png" alt="" />
        <img v-else-if="age >= 59" src="@/assets/case/man_1.png" alt="" />
        <img v-else src="@/assets/case/man_0.png" alt="" />
      </template>
      <template v-if="sex === 'F'">
        <img v-if="age >= 35" src="@/assets/case/woman_2.png" alt="" />
        <img v-else-if="age >= 59" src="@/assets/case/woman_1.png" alt="" />
        <img v-else src="@/assets/case/woman_0.png" alt="" />
      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: 'CasePhoto',
  props: ['sex', 'age'],
  data() {
    return {}
  }
}
</script>

<style scoped lang="scss"></style>
