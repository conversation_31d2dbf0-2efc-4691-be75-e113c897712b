<template>
  <div>
    <!-- 播放音频 -->
    <audio ref="audioRef" loop autoplay>
      <source :src="audioUrl" type="audio/wav" />
    </audio>
  </div>
</template>

<script>
export default {
  name: 'voiceToText',
  props: {
    recordingStatus: {
      type: Boolean,
      default: false
    },
    InDialogue: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      audioUrl: '' // 语音播报
    }
  },
  mounted() {
    document.addEventListener('keydown', this.keyboard_down) // 键盘按下
    document.addEventListener('keyup', this.keyboard_up) // 键盘抬起
    document.addEventListener('paste', this.pasteHandle) // 粘贴事件
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keyboard_down)
    document.removeEventListener('keyup', this.keyboard_up)
    document.removeEventListener('paste', this.pasteHandle)
  },
  methods: {
    // #region 语音录入相关代码
    keyboard_down(event) {
      if (event.key === 'F2') {
        if (this.InDialogue) return
        this.$refs['audioRef'].pause()
        console.log('开始录音')
        this.$emit('update:InDialogue', true)
      }
    },
    keyboard_up(event) {
      if (event.key === 'F2') {
        console.log('录音完成')
        this.$emit('update:InDialogue', false)
      }
    },
    pasteHandle(event) {
      // 获取剪贴板数据
      const clipboardData = event.clipboardData || window.clipboardData
      const pastedData = clipboardData.getData('Text')

      // 输出剪贴板内容
      console.log('粘贴的内容:', pastedData)
      if (pastedData.trim() && !this.InDialogue) {
        this.$nextTick(() => {
          this.$emit('recognitionOver', pastedData)
        })
      }
    },
    // #endregion
    playAudio(data) {
      if (data.answerFileUrl) {
        this.audioUrl = window.config.VUE_FILE_BASE_PATH + data.answerFileUrl
        this.$refs['audioRef'].load()
      }
    },
    clearAudio() {
      this.audioUrl = null
      this.$refs['audioRef'].pause()
    }
  }
}
</script>

<style lang="sass" scoped></style>
