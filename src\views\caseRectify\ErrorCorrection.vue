<template>
  <div>
    <el-dialog title="选择纠错数据" :visible="dialog" width="700px" @close="close">
      <el-descriptions v-if="caseInfo" title="上报纠错的信息" border>
        <el-descriptions-item label="上报纠错">{{ caseInfo.problem }}</el-descriptions-item>
        <el-descriptions-item label="病例信息">
          <el-tag style="margin-right: 10px">姓名:{{ caseInfo.name }}</el-tag>
          <el-tag style="margin-right: 10px">年龄:{{ caseInfo.age }}</el-tag>
          <el-tag>性别:{{ caseInfo.sex === 'F' ? '女' : '男' }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <div class="selectData">
        <div class="title">选择相关数据进行修改</div>
        <div class="content">
          <div class="content_item" v-for="item in list" :key="item.questionId">
            <span>{{ item.problem }}</span>
            <el-button type="primary" size="small" @click="redress(item)">纠错</el-button>
          </div>
        </div>
      </div>
      <div class="alert">
        <span>无相关问题修改，可以新增问题</span>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="addQuestion">设为新增问题</el-button>
      </div>
    </el-dialog>

    <RedressDialog ref="RedressDialog" :addDialog.sync="addDialog" @success="redressSuccess" />
  </div>
</template>
<script>
import { caseRectifySelectQuestion, caseRectifyUpdate } from '@/api/caseRectify'
import RedressDialog from '@/views/caseRectify/RedressDialog'
export default {
  name: 'ErrorCorrection',
  components: {
    RedressDialog
  },
  props: {
    dialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      caseInfo: null,
      list: [],
      addDialog: false
    }
  },
  created() {},
  methods: {
    close() {
      this.$emit('update:dialog', false)
    },
    async searchInfo() {
      const { data } = await caseRectifySelectQuestion({ caseId: this.caseInfo.caseId, answer: this.caseInfo.problem })
      this.list = data
    },
    redress(item) {
      this.$refs['RedressDialog'].showData(item)
      this.addDialog = true
    },
    async redressSuccess() {
      const info = {
        rectifyId: this.caseInfo.rectifyId,
        caseId: this.caseInfo.caseId,
        problem: this.caseInfo.problem,
        answer: this.caseInfo.answer,
        isRectify: 1
      }
      await caseRectifyUpdate(info)
      this.$message.success('纠错成功')
      this.$emit('success')
      this.close()
    },
    addQuestion() {
      this.$refs['RedressDialog'].formInfo.caseId = this.caseInfo.caseId
      this.$refs['RedressDialog'].formInfo.problem = this.caseInfo.problem
      this.$refs['RedressDialog'].formInfo.answer = this.caseInfo.answer
      this.addDialog = true
    }
  }
}
</script>
<style scoped lang="scss">
.selectData {
  .title {
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
  .content {
    .content_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      padding: 5px 10px;
      border-radius: 8px;
      border: 1px solid #c4c2c2;
      color: #1a1a1a;
      cursor: pointer;
      &:hover {
        border-color: #45a6ff;
      }
    }
  }
}
.alert {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 400px;
  margin: 0 auto;
  margin-top: 30px;
  background: #f2f2f2;
  padding: 5px 10px;
  border-radius: 8px;
  span {
    color: #f59a23;
  }
}
</style>
