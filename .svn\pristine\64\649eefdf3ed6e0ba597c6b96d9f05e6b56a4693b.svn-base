<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="variables.menuBg" :text-color="variables.menuText" :unique-opened="false" :active-text-color="variables.menuActiveText" :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="route in checkedRoutes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      checkedRoutes: []
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar']),

    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  mounted() {
    const _this = this
    // 根据自己需要来监听对应的key
    window.addEventListener('setItemEvent', function (e) {
      // e.key : 是值发生变化的key
      // 例如 e.key==="token";
      // e.newValue : 是可以对应的新值
      if (e.key === 'sp_admin_modelType') {
        _this.cahngeType(e.newValue)
      }
    })
    this.setData()
  },
  methods: {
    setData() {
      const routes = []
      if (window.localStorage.getItem('sp_admin_modelType')) {
        this.permission_routes.forEach((item) => {
          if (item.meta && item.meta.moduleType === parseInt(window.localStorage.getItem('sp_admin_modelType'))) {
            routes.push(item)
          }
        })
        this.checkedRoutes = routes
      }
    },
    cahngeType(type) {
      const routes = []
      this.permission_routes.forEach((item) => {
        if (item.meta && item.meta.moduleType === parseInt(type)) {
          routes.push(item)
        }
      })
      this.checkedRoutes = routes
    }
  }
}
</script>
