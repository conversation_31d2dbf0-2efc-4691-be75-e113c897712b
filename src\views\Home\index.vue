<template>
  <div class="home-container">
    <div class="home_top">
      <div class="title">SP开放性人机对话系统</div>
      <!-- <img src="@/assets/images/home_top.png" alt="" /> -->
    </div>
    <div class="systemModule-userOperate">
      <div class="systemModule" @click="jump(1)">
        <img src="@/assets/home/<USER>" alt="" />
        <span>系统管理</span>
        <img src="@/assets/home/<USER>" alt="" />
      </div>
      <i class="division"></i>
      <div class="userOperate">
        <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img src="@/assets/home/<USER>" alt="" />
            <span>{{ name }}</span>
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <span style="display: block" @click="openPassword">修改密码</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span style="display: block" @click="openEdit">修改个人信息</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span style="display: block" @click="logout">退出</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="bigBox">
      <section>
        <!-- <div v-if="isExist('system:system')" class="box" @click="jump(1)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>系统管理</span>
        </div> -->
        <div v-if="isExist('case:case')" class="box" @click="jump(2)">
          <div class="moduleName">
            <span>病例库</span>
            <i class="icon"></i>
          </div>
          <img src="@/assets/home/<USER>" alt="" />
        </div>
        <div v-if="isExist('caseExam:caseExam')" class="box" @click="jump(4)">
          <div class="moduleName">
            <span>病例考核</span>
            <i class="icon"></i>
          </div>
          <img src="@/assets/home/<USER>" alt="" />
        </div>
        <div v-if="isExist('volume:volume')" class="box" @click="jump(5)">
          <div class="moduleName">
            <span>理论考核</span>
            <i class="icon"></i>
          </div>
          <img src="@/assets/home/<USER>" alt="" />
        </div>
        <div v-if="isExist('question:question')" class="box" @click="jump(6)">
          <div class="moduleName">
            <span>题库</span>
            <i class="icon"></i>
          </div>
          <img src="@/assets/home/<USER>" alt="" />
        </div>
        <div v-if="isExist('casePractise:casePractise')" class="box" @click="jump(7)">
          <div class="moduleName">
            <span>病例训练记录</span>
            <i class="icon"></i>
          </div>
          <img src="@/assets/home/<USER>" alt="" />
        </div>
        <div v-if="isExist('examGrade:examGrade')" class="box" @click="jump(8)">
          <div class="moduleName">
            <span>病例考核成绩</span>
            <i class="icon"></i>
          </div>
          <img src="@/assets/home/<USER>" alt="" />
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { selectTeacherById } from '@/api/teacher.js'

export default {
  name: '',
  data() {
    return {
      userinfo: {},
      name: ''
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    ...mapGetters(['permission_routes', 'roles'])
  },
  methods: {
    isExist(role) {
      return this.roles.includes(role)
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.name = res.data.name
      })
    },
    jump(type) {
      // type: 1系统管理
      window.localStorage.setItem('sp_admin_modelType', type)
      this.$router.push(this.getFirstRoute(type))
    },
    getFirstRoute(type) {
      const route = this.permission_routes.filter((item) => {
        if (item.meta && item.meta.moduleType === type) {
          return item
        }
      })
      return route[0].path
    },
    openPassword() {},
    openEdit() {},
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    }
  }
}
</script>
<style scoped lang="scss">
.home-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('~@/assets/home/<USER>') no-repeat;
  overflow: hidden;
  .home_top {
    position: relative;
    padding-top: 80px;
    padding-bottom: 60px;
    width: 100%;
    .title {
      display: flex;
      justify-content: center;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #000000;
    }
  }
  .systemModule-userOperate {
    position: absolute;
    top: 40px;
    right: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 440px;
    height: 60px;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0px 1px 15px 0px rgba(9, 15, 22, 0.16);
    border-radius: 14px 14px 14px 14px;
    border: 1px solid #ffffff;
    cursor: pointer;
    .systemModule {
      display: flex;
      align-items: center;
      span {
        margin-left: 6px;
        margin-right: 20px;
      }
    }
    .division {
      width: 1px;
      height: 20px;
      margin-left: 35px;
      margin-right: 37px;
      background: #8092aa;
    }
    .userOperate {
      display: flex;
      align-items: center;

      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        img {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
        span {
          margin-right: 20px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #333333;
        }
        i {
          font-size: 20px;
          color: #252f3c;
        }
      }
    }
  }
  .bigBox {
    position: relative;
    z-index: 1;

    margin: 0 auto;
    padding: 0 395px;
    section {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .box {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 318px;
        height: 300px;
        margin-right: 88px;
        margin-bottom: 60px;
        background: url('~@/assets/home/<USER>') no-repeat;
        background-size: cover;
        border-radius: 14px;
        cursor: pointer;
        &:hover {
          background: url('~@/assets/home/<USER>') no-repeat;
          background-size: cover;
          box-shadow: 0 9px 15px rgba($color: #090f16, $alpha: 0.16);
          .moduleName {
            span {
              color: #fff;
            }
            i {
              background: url('~@/assets/home/<USER>') no-repeat;
              background-size: cover;
            }
          }
        }
        &:nth-of-type(3n) {
          margin-right: 0;
        }
        & > img {
          position: absolute;
          right: 10px;
          bottom: 10px;
          width: 170px;
          height: 170px;
        }
        .moduleName {
          position: absolute;
          left: 33px;
          top: 50px;
          span {
            font-family: Source Han Sans CN;
            font-weight: 500;
            font-size: 32px;
            color: #333333;
          }
          i {
            display: block;
            width: 36px;
            height: 36px;
            margin-top: 26px;
            background: url('~@/assets/home/<USER>') no-repeat;
            background-size: cover;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-dropdown-menu.el-popper {
  width: 132px;
  margin-top: 30px;
  padding: 8px 4px;
  background: rgba($color: #ffffff, $alpha: 0.5);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #d7dff5;
  .el-dropdown-menu__item {
    padding: 8px 0;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #252f3c;
    text-align: center;
    &:hover {
      background: #fff;
      color: #3072f9;
    }
  }
  .popper__arrow {
    &::after {
      border-bottom-color: rgba($color: #fff, $alpha: 0.5);
    }
  }
}
</style>
