<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="addDialog" width="1100px" top="60px" @close="close" custom-class="examDialog" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="140px" :rules="rules" inline>
        <el-form-item label="考试名称:" prop="name">
          <el-input v-model="form.name" maxlength="50" placeholder="请输入考试名称"></el-input>
        </el-form-item>
        <el-form-item label="考试开始时间:" prop="startTime">
          <el-date-picker v-model="startTime" type="datetime" placeholder="选择考试开始时间" format="yyyy-MM-dd HH:mm" :picker-options="startTimePickerOptions" @change="startTimePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="考试结束时间:" prop="endTime">
          <el-date-picker v-model="endTime" type="datetime" placeholder="选择考试结束时间" format="yyyy-MM-dd HH:mm" :picker-options="endTimePickerOptions" @change="endTimePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="考试限时:" prop="time"> <el-input-number v-model="form.time" :min="1" :max="timeLimit" label="考试限时"></el-input-number> 分钟 </el-form-item>
        <el-form-item label="及格分:" prop="passScore"> <el-input-number v-model="form.passScore" :min="1" :max="100" label="及格分"></el-input-number> </el-form-item>
        <el-form-item label="考核病例:" prop="caseIds" class="selectContent">
          <el-button type="primary" size="small" style="margin-bottom: 15px" @click="selectCase">选择病例</el-button>
          <div class="caseBox">
            <div v-for="item in selectList" :key="item.caseId" class="caseItem">
              <div class="weight">
                <span>分数权重</span>
                <el-select v-model="item.weight" placeholder="选择权重" :disabled="selectList.length <= 1 ? true : false" @change="weightChnage($event, item.caseId)">
                  <el-option v-for="item in weightOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </div>
              <div class="question" @click="lookCaseDetails(item)">病例详情 <i class="el-icon-arrow-right"></i></div>
              <div class="caseItemInfo">
                <div class="caseItem_top_left">
                  <casePhoto :sex="item.sex" :age="item.age" />
                </div>
                <div class="caseItem_top_right">
                  <div class="caseName">{{ item.name }}</div>
                  <div class="patientInfo">
                    <span> {{ item.realName }} </span>
                    <svg-icon :icon-class="item.sex === 'M' ? 'nan' : 'nv'"></svg-icon>
                    <span> {{ item.age }} 岁</span>
                  </div>
                  <div class="score">
                    <span>病例总分</span>
                    <span> {{ item.allScore }}分</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="考核设置:" prop="type">
          <el-radio-group v-model="form.type" :disabled="selectList.length <= 1">
            <el-radio :label="1">随机全考<span>所选病例，随机全部考，顺序随机</span> </el-radio>
            <el-radio :label="2">随机考一个 <span>所选病例，随机全部考，顺序随机</span></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="班级学生:" prop="studentIds" class="selectContent">
          <el-button type="primary" size="small" style="margin-bottom: 15px" @click="selectStudent">选择班级学生</el-button>
          <el-card v-if="checkedStudentList.length" class="checkedStudents">
            <div slot="header">选择的班级学生</div>
            <div v-for="item in checkedStudentList" :key="item.id">
              <div class="checkedClass">{{ item.className }}</div>
              <ul class="students">
                <li v-for="li in item.children" :key="li.id">
                  <el-tag type="success" closable @close="tagClose(li, item)">{{ li.name }}</el-tag>
                </li>
              </ul>
            </div>
          </el-card>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 选择病例 -->
    <SelectCase ref="SelectCase" :selectCaseDialog.sync="selectCaseDialog" :selectList="selectList" @success="selectCaseOver" />
    <!-- 选择班级学生 -->
    <SelectStudent ref="SelectStudent" :selectStudentDialog.sync="selectStudentDialog" @success="selectStudentOver" />
    <!-- 病例详情 -->
    <LookCaseDetails ref="LookCaseDetails" :showDialog.sync="lookCaseDetailsDialog" />
  </div>
</template>
<script>
import { caseExamAdd, caseExamUpdate, caseExamTimeJudge } from '@/api/caseExam'
import casePhoto from '@/components/casePhoto'
import { formatDate } from '@/filters'
import SelectCase from '@/views/caseExam/components/SelectCase'
import SelectStudent from '@/views/caseExam/components/SelectStudent'
import LookCaseDetails from '@/views/caseExam/components/LookCaseDetails'
export default {
  name: 'AddExamDialog',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    casePhoto,
    SelectCase,
    SelectStudent,
    LookCaseDetails
  },

  data() {
    return {
      form: {
        name: null,
        clbumIds: null,
        startTime: null,
        endTime: null,
        time: null, // 考试限时
        state: 1,
        passScore: null,
        caseIds: [],
        studentIds: [],
        type: null, // 类型 1 随机全考 2 随机抽考一个 3 考一个
        allScore: 0
      },
      startTime: null,
      endTime: null,
      startTimePickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      endTimePickerOptions: {
        disabledDate: (time) => {
          const date = Date.parse(formatDate(time, 'yyyy-MM-dd hh:mm:ss'))
          const startTime = Date.parse(this.form.startTime)
          return date < startTime - 8.64e7 || time.getTime() < Date.now() - 8.64e7
        }
      },
      selectCaseDialog: false,
      selectStudentDialog: false,
      rules: {
        name: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
        startTime: [{ required: true, message: '请选择考试开始时间', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择考试结束时间', trigger: 'change' }],
        time: [{ required: true, message: '请输入考试限时', trigger: 'blur' }],
        publishTime: [{ required: true, message: '请选择成绩公布时间', trigger: 'change' }],
        passScore: [{ required: true, message: '请输入及格分数', trigger: 'blur' }],
        caseIds: [{ type: 'array', required: true, message: '病例不能为空', trigger: 'change' }],
        studentIds: [{ type: 'array', required: true, message: '学生不能为空', trigger: 'change' }],
        type: [{ required: true, message: '请选择考核设置', trigger: 'change' }]
      },
      selectList: [],
      checkedStudentList: [],
      isCopy: false,
      weightOptions: [
        { value: 10, label: '10%' },
        { value: 20, label: '20%' },
        { value: 30, label: '30%' },
        { value: 40, label: '40%' },
        { value: 50, label: '50%' },
        { value: 60, label: '60%' },
        { value: 70, label: '70%' },
        { value: 80, label: '80%' },
        { value: 90, label: '90%' },
        { value: 100, label: '100%' }
      ],
      lookCaseDetailsDialog: false
    }
  },
  computed: {
    dialogTitle() {
      let label = null
      if (this.form.examId) {
        label = '编辑考核'
      } else {
        label = this.isCopy ? '复制考核' : '添加考核'
      }
      return label
    },
    timeLimit() {
      if (this.form.startTime && this.form.endTime) {
        // 使用示例
        const startTime = new Date(this.form.startTime).getTime()
        const endTime = new Date(this.form.endTime).getTime()
        const totalTime = endTime - startTime
        const maxTime = Math.floor(totalTime / 60000)
        return maxTime > 240 ? 240 : maxTime
      } else {
        return 240
      }
    }
  },
  created() {},
  methods: {
    startTimePickerChange(val) {
      if (val) {
        this.form.startTime = formatDate(val, 'yyyy-MM-dd hh:mm:ss')
      } else {
        this.form.startTime = null
      }
    },
    endTimePickerChange(val) {
      if (val) {
        const startTime = Date.parse(this.form.startTime)
        const endTime = Date.parse(val)
        if (startTime >= endTime) {
          this.endTime = null
          return this.$message.warning('考试结束时间不能小于考试开始时间')
        } else {
          this.form.endTime = formatDate(val, 'yyyy-MM-dd hh:mm:ss')
        }
      } else {
        this.form.endTime = null
      }
    },
    /** 选择病例*/
    selectCase() {
      this.$refs['SelectCase'].getCaseList()
      this.selectCaseDialog = true
    },
    selectCaseOver(list) {
      this.selectList = list
      this.form.caseIds = this.selectList.map((item) => {
        return {
          caseId: item.caseId,
          weight: item.weight
        }
      })
      // 单选病例
      if (this.form.caseIds.length === 1) {
        this.form.type = 3
      } else {
        this.form.type = null
      }
      this.$refs['form'].validateField('caseIds')
    },
    // 选中权重
    weightChnage(val, caseId) {
      console.log(this.form.caseIds)
      const allWeight = this.form.caseIds.reduce((accumulator, currentValue) => (currentValue.caseId !== caseId ? accumulator + currentValue.weight : accumulator), 0)
      if (allWeight + val > 100) {
        this.$message.warning('权重值不能超过100')
        this.selectList.forEach((item) => {
          if (item.caseId === caseId) {
            item.weight = null
          }
        })
        this.form.caseIds.forEach((item) => {
          if (item.caseId === caseId) {
            item.weight = 0
          }
        })
        return
      } else {
        this.form.caseIds.forEach((item) => {
          if (item.caseId === caseId) {
            item.weight = val
          }
        })
      }
    },
    // 查看病例详情
    lookCaseDetails(item) {
      this.lookCaseDetailsDialog = true
      this.$refs['LookCaseDetails'].getCaseDetails(item)
    },
    /** 选择班级学生 */
    selectStudent() {
      this.$refs['SelectStudent'].getClassAll()
      this.selectStudentDialog = true
    },
    selectStudentOver({ checkedList, checkIds }) {
      this.checkedStudentList = [...checkedList]
      this.form.studentIds = [...checkIds]
      this.$nextTick(() => {
        this.$refs['form'].validateField('studentIds')
      })
    },
    tagClose(li, data) {
      this.$refs['SelectStudent'].tagClose(li, data)
    },

    showData(data, type) {
      console.log(data)
      this.isCopy = type === 'copy' ? true : false
      this.form = {
        examId: type === 'copy' ? null : data.examId,
        name: data.name,
        clbumIds: data.clbumIds,
        startTime: data.startTime,
        endTime: data.endTime,
        time: data.time, // 考试限时
        state: data.state,
        passScore: data.passScore,
        caseIds: data.cases.map((item) => {
          return {
            caseId: item.case_id,
            weight: item.weight
          }
        }),
        studentIds: data.students.map((item) => item.student_id),
        type: data.type,
        allScore: parseFloat(data.allScore)
      }
      this.startTime = this.form.startTime
      this.endTime = this.form.endTime
      // 回显病例
      data.cases.forEach((item) => {
        this.selectList.push({
          caseId: item.case_id,
          weight: item.weight,
          allScore: item.all_score,
          sex: item.sex,
          name: item.name,
          age: item.age,
          form: item.form,
          mainDemands: item.main_demands
        })
      })
      this.$refs['SelectCase'].getCaseList()
      this.$refs['SelectStudent'].showData(data)
    },
    // 关闭
    close() {
      this.$emit('update:addDialog', false)
      this.form = {
        name: null,
        clbumIds: null,
        startTime: null,
        endTime: null,
        time: null, // 考试限时
        state: 1,
        passScore: null,
        caseIds: [],
        studentIds: [],
        type: null, // 类型 1 随机全考 2 随机抽考一个 3 考一个
        allScore: 0
      }
      this.startTime = null
      this.endTime = null
      this.selectList = []
      this.checkedStudentList = []
      this.$refs['SelectStudent'].checkStudents = []
      this.$refs['SelectStudent'].checkIds = []
      this.$refs['form'].resetFields()
    },

    confirm() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          // 判断当前时间下是否有考试
          const timeInfo = {
            startTime: this.form.startTime,
            endTime: this.form.endTime,
            examId: this.form.examId ? this.form.examId : null,
            type: this.form.examId ? 2 : 1
          }
          const startTime = new Date(timeInfo.startTime).getTime()
          const endTime = new Date(timeInfo.endTime).getTime()
          if (startTime > endTime) {
            return this.$message.warning('考试开始时间不能大于结束时间')
          }
          await caseExamTimeJudge(timeInfo)
          // 判断选中的病例权重是否全部选择,多选病例才有验证
          if (this.form.caseIds.length > 1) {
            const isWeightNull = this.form.caseIds.some((item) => !item.weight)
            if (isWeightNull) {
              return this.$message.error('病例的权重值不能为空')
            }
            // 判断所有病例的权重是否等于100
            const totalWeight = this.form.caseIds.reduce((accumulator, currentValue) => {
              return accumulator + currentValue.weight
            }, 0)
            console.log(totalWeight)
            if (totalWeight < 100) {
              return this.$message.error('病例权重值加起来必须等于100')
            }
          }
          // 根据权重值设置总分数
          this.form.allScore = await this.setAllScore()
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.form.clbumIds = this.checkedStudentList.map((item) => item.classId).join(',')
          if (this.form.examId) {
            caseExamUpdate(this.form)
              .then(() => {
                loading.close()
                this.$message.success('修改考核成功！')
                this.$emit('success')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          } else {
            caseExamAdd(this.form)
              .then(() => {
                loading.close()
                this.$message.success(`${this.isCopy ? '复制' : '添加'}考核成功！`)
                this.$emit('success')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    },
    setAllScore() {
      return new Promise((resolve, reject) => {
        const caseAllScore = this.selectList.reduce((accumulator, currentValue) => {
          return accumulator + parseFloat(currentValue.allScore) * (currentValue.weight / 100)
        }, 0)
        resolve(caseAllScore)
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .selectCase {
    .el-dialog__body {
      padding-bottom: 0;
    }
  }
}
::v-deep {
  .examDialog {
    .el-dialog__body {
      height: 700px;
      padding-left: 55px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 3px;
      }

      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #d2d2d2;
      }

      // 外面的背景
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
    }
    .el-form-item {
      margin-bottom: 30px;
    }
    .el-input-number,
    .el-date-editor,
    .el-input__inner {
      width: 300px;
    }
    .el-form-item__label {
      font-size: 18px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      color: #000000;
    }
    .selectContent {
      width: 100%;
      .el-form-item__content {
        width: calc(100% - 140px);
      }
    }
    .el-card__body {
      padding-top: 0;
    }
    .checkedStudents {
      .el-card__header {
        div {
          font-size: 22px;
          font-weight: bold;
        }
      }
      .checkedClass {
        padding-left: 10px;
        margin-top: 15px;
        font-size: 18px;
        font-weight: bold;
      }
      .students {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        padding-left: 10px;
        margin: 0;
        margin-top: 10px;
        list-style: none;
        li {
          margin-right: 10px;
        }
      }
    }
  }
  // 选择后病例样式
  .caseBox {
    display: flex;
    flex-wrap: wrap;
    .caseItem {
      position: relative;
      width: 399px;
      height: 186px;
      padding: 10px 18px;
      margin-right: 10px;
      background: #f0f0f0;
      border-radius: 10px;

      .weight {
        & > span {
          margin-right: 12px;
          font-size: 16px;
          font-family:
            Abyssinica SIL,
            Abyssinica SIL;
          font-weight: 400;
          color: #333333;
        }
        .el-select {
          .el-input__inner {
            width: 136px;
            height: 40px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #f4f7ff;
          }
        }
      }
      .question {
        position: absolute;
        right: 10px;
        top: 10px;
        font-size: 14px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #1890ff;
        cursor: pointer;
      }
      .caseItemInfo {
        display: flex;
        width: 363px;
        height: 108px;
        margin-top: 13px;
        background: #ffffff;
        border-radius: 10px;
        border: 1px solid #ffffff;
        overflow: hidden;
        .caseItem_top_left {
          ::v-deep {
            img {
              width: 100%;
              height: 100%;
              object-fit: scale-down;
            }
          }
        }
        .caseItem_top_right {
          padding: 10px;
          line-height: 24px;
          .caseName {
            font-size: 18px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #000000;
          }
          .patientInfo {
            & > span {
              font-size: 16px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #666666;
            }
            .svg-icon {
              margin: 0 4px;
            }
          }
          .score {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 246px;
            height: 42px;
            padding: 0 38px;
            background: #f0f0f0;
            border-radius: 8px;
            & > span:first-of-type {
              font-size: 16px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #333333;
            }
            & > span:last-of-type {
              font-size: 18px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: bold;
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}
</style>
