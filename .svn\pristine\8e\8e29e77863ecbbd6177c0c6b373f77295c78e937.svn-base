<template>
  <div class="casePractise-details">
    <div class="details_header">
      <div class="header_back" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="title">{{ $route.params.name }}</div>
    </div>
    <div class="details_body">
      <div class="details_body_header" v-if="caseInfo">
        <div class="case-user">
          <Avatar :age="caseInfo.age" :sex="caseInfo.sex" />
          <div class="info">
            <div>{{ caseInfo.name }}</div>
            <span>{{ caseInfo.realName }}</span>
            <span>{{ caseInfo.sex === 'F' ? '女' : '男' }}</span>
            <span>{{ caseInfo.age }}岁</span>
          </div>
        </div>

        <div class="case-stats">
          <div class="statistics_item">
            <span>{{ caseInfo.allScore }}</span>
            <span>总分 (分)</span>
          </div>
          <div class="statistics_item boderItem">
            <span> {{ caseInfo.maxScore ? caseInfo.maxScore : 0 }}</span>
            <span>最高得分 (分)</span>
          </div>
          <div class="statistics_item">
            <span>{{ caseInfo.avgScore ? caseInfo.avgScore : 0 }}</span>
            <span>平均得分 (分)</span>
          </div>
        </div>
        <div class="case-stats">
          <div class="statistics_item">
            <span> {{ caseInfo.allTime ? parseInt(caseInfo.allTime / 60) : 0 }}</span>
            <span>训练总用时 (分)</span>
          </div>
          <div class="statistics_item boderItem">
            <span>{{ caseInfo.count ? caseInfo.count : 0 }}</span>
            <span>操作总次数 (次)</span>
          </div>
          <div class="statistics_item">
            <span> {{ caseInfo.allStudentCount ? caseInfo.allStudentCount : 0 }}</span>
            <span>训练人数 (人)</span>
          </div>
        </div>
      </div>
      <div class="details_body_content">
        <el-form ref="form" :model="queryInfo" label-width="110px" inline>
          <el-form-item label="学号:" label-width="60px">
            <el-input v-model="queryInfo.loginName" placeholder="学号" @keydown.native.enter="getStudentList" clearable @clear="getStudentList"></el-input>
          </el-form-item>
          <el-form-item label="所在班级:">
            <el-select v-model="queryInfo.clbumId" placeholder="请选择所在班级" clearable @change="getStudentList">
              <el-option v-for="item in classList" :key="item.id" :label="item.clbumName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间:">
            <el-date-picker v-model="time" type="daterange" range-separator="—" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" format="yyyy-MM-dd " value-format="yyyy-MM-dd HH:mm:ss" prefix-icon="el-icon-date" @change="datePickerChange"> </el-date-picker>
          </el-form-item>
          <el-form-item style="float: right">
            <el-button class="search__button" type="primary" @click="getStudentList">查询</el-button>
            <el-button class="reset__button" type="primary" plain @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="list" style="width: 100%" header-cell-class-name="el_table_header_cell_class">
          <el-table-column prop="studentName" label="学生姓名" width="width" align="center"> </el-table-column>
          <el-table-column prop="prop" label="性别" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.sex === 'F' ? '女' : '男' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="loginName" label="学号" width="width" align="center"> </el-table-column>
          <el-table-column prop="clbumName" label="所在班级" width="width" align="center"> </el-table-column>
          <el-table-column prop="allCount" label="训练总次数" width="width" align="center"> </el-table-column>
          <el-table-column prop="allCount" label="平均得分" width="width" align="center"> </el-table-column>
          <el-table-column prop="avgTime" label="平均用时(分)" width="width" align="center">
            <template v-slot="{ row }">
              {{ secondsToMinutes(row.avgTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="endTime" label="最近一次训练时间" width="300" align="center"> </el-table-column>
          <el-table-column prop="prop" label="操作" width="150" align="center">
            <template v-slot="{ row }">
              <el-button class="details__button" type="primary" icon="el-icon-tickets" size="small" @click="details(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination">
          <el-pagination background :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="prev, pager, next" :total="total" @size-change="getStudentList" @current-change="getStudentList"> </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { caseDetail } from '@/api/case'
import { casePractiseStudentList } from '@/api/casePractise'
import { clbumTree } from '@/api/clbum'
import { selectTeacherById } from '@/api/teacher'
import Avatar from './components/Avatar.vue'

export default {
  name: '',
  components: {
    Avatar
  },
  data() {
    return {
      queryInfo: {
        clbumId: null,
        loginName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 7
      },
      time: null,
      classList: [],
      caseInfo: null,
      list: [],
      total: 0
    }
  },
  created() {
    this.getCaseDetails()
    this.getStudentList()
    this.getClassList()
  },
  methods: {
    goBack() {
      this.$router.push('/casePractise')
    },

    async getCaseDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.caseInfo = data
    },
    async getStudentList() {
      const { data } = await casePractiseStudentList({ ...this.queryInfo, caseId: this.$route.params.id })
      this.list = data.list
      this.total = data.total
    },
    getClassList() {
      selectTeacherById().then(async (res) => {
        const { data } = await clbumTree({ schoolId: res.data.schoolId })
        this.classList = data
      })
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getStudentList()
    },
    reset() {
      this.queryInfo = {
        clbumId: null,
        loginName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 7
      }
      this.time = null
      this.getStudentList()
    },
    details(item) {
      this.$router.push(`/casePractise/details/record/${item.practiseId}/${this.caseInfo.caseId}/${item.studentId}`)
    },
    secondsToMinutes(seconds) {
      var minutes = seconds / 60
      return minutes.toFixed(2)
    }
  }
}
</script>
<style scoped lang="scss">
.casePractise-details {
  width: 100%;
  height: 100%;
  padding: 0 50px;
  padding-top: 10px;
  background: #e3e8ed;
  overflow: hidden;
  .details_header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 18px;
    margin-bottom: 22px;
    .header_back {
      position: absolute;
      left: 0;
      top: -10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      text-align: center;
    }
  }
  .details_body {
    width: 100%;
    .details_body_header {
      display: flex;
      align-items: center;
      // justify-content: center;
      padding-left: 50px;
      padding-right: 20px;
      margin-bottom: 15px;
      width: 100%;
      height: 128px;
      background: #65849a;
      border-radius: 20px;
      .case-user {
        display: flex;
        align-items: center;
        padding-left: 20px;
        width: 406px;
        height: 84px;
        background: #7399b4;
        border-radius: 10px;
        .Avatar {
          width: 60px;
          height: 60px;
          ::v-deep {
            img {
              width: 100%;
              height: 100%;
            }
            .sexIcon {
              width: 20px;
              height: 20px;
            }
          }
        }
        .info {
          margin-left: 12px;
          & > div {
            margin-bottom: 10px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 20px;
            color: #ffffff;
          }
          & > span {
            margin: 0 3px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.7);
          }
        }
        .statistics_item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          & > span:first-of-type {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #333333;
            i {
              font-style: normal;
              font-size: 12px;
            }
          }
          & > span:last-of-type {
            margin-top: 14px;
            font-size: 18px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            color: #1890ff;
          }
        }
      }
      .case-stats {
        display: flex;
        align-items: center;
        height: 84px;
        padding-left: 44px;
        margin-left: 20px;
        background: #bfd6e3;
        border-radius: 10px 10px 10px 10px;
        .statistics_item {
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin-right: 80px;

          &::after {
            content: '';
            position: absolute;
            right: -40px;
            width: 2px;
            height: 34px;
            background: #7f7f7f;
          }
          &:last-of-type {
            margin-right: 44px;
            &::after {
              display: none;
            }
          }
          & > span:first-of-type {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 26px;
            color: #293543;
            text-align: center;
          }
          & > span:last-of-type {
            margin-top: 8px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: rgba(41, 53, 67, 0.8);
          }
        }
      }
    }

    .details_body_content {
      width: 100%;
      height: 670px;
      padding: 30px;
      background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
      border-radius: 30px 30px 30px 30px;
    }
  }
}
</style>
<style lang="scss" scoped>
.details_body_content {
  position: relative;
  ::v-deep {
    .el-form {
      display: flex;
      .el-form-item {
        display: flex;
        align-items: center;

        .el-form-item__label {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #666666;
        }
        .el-form-item__content {
          .el-input {
            .el-input__inner {
              height: 45px;
              background: #eef0f2;
              border: none;
              border-radius: 74px 74px 74px 74px;
              font-size: 20px;
              color: #333333;
              &::placeholder {
                color: #cccccc;
              }
            }
            .el-input__suffix {
              .el-input__suffix-inner {
                .el-icon-circle-close {
                  margin-right: 5px;
                  margin-top: 2px;
                  font-size: 20px;
                }
              }
            }
          }
          .el-select {
            .el-input {
              .el-input__suffix {
                .el-input__suffix-inner {
                  .el-icon-circle-close {
                    margin-right: 5px;
                    margin-top: -3px;
                    font-size: 20px;
                  }
                }
              }
            }
          }
          .el-date-editor {
            width: 420px;
            height: 45px;
            background: #eef0f2;
            border-radius: 74px 74px 74px 74px;
            border: none;
            .el-icon-date {
              font-size: 20px;
              margin-left: 20px;
              margin-top: 5px;
            }
            .el-range-separator {
              margin-top: 12px;
            }
            .el-range-input {
              background: transparent;
              font-size: 20px;
            }
            .el-range__close-icon {
              margin-right: 5px;
              margin-top: 5px;
              font-size: 20px;
            }
          }
        }
        .search__button {
          padding: 0;
          width: 70px;
          height: 45px;
          background: #65849a;
          border-radius: 63px 63px 63px 63px;
          border: none;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          text-align: center;
        }
        .reset__button {
          padding: 0;
          width: 70px;
          height: 45px;
          background: #fff;
          border-radius: 63px 63px 63px 63px;
          border: 1px solid #65849a;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #65849a;
          text-align: center;
        }
      }
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }

    .el-table {
      border: none;
      &::before {
        display: none;
      }
      .el-table__row {
        border-radius: 8px 8px 8px 8px;
        .el-table__cell {
          height: 55px;
          background: #f6f8fa;
          border-top: 2px solid #fff;
          .cell {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            text-align: center;
          }
        }
      }
      .el_table_header_cell_class {
        height: 55px;
        background: #f6f8fa;
        // border-radius: 8px 8px 8px 8px;
        text-align: center;
        & > .cell {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #999999;
        }
      }
      .details__button {
        padding: 0;
        width: 88px;
        height: 38px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e2e2e2;

        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #3381b9;
      }
    }
    .pagination {
      position: absolute;
      left: 50%;
      bottom: 15px;
      transform: translateX(-50%);
      .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #65849a;
        color: #eef0f2;
      }
      .el-pagination.is-background .el-pager li {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        background-color: #eef0f2;
        color: #65849a;
      }
      .el-pagination.is-background .btn-prev,
      .el-pagination.is-background .btn-next {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        background-color: #eef0f2;
        color: #65849a;
        font-size: 16px;
      }
      .el-pagination.is-background .btn-prev:disabled {
        color: rgba($color: #65849a, $alpha: 0.3);
      }
      .el-pagination__total {
        height: 40px;
        line-height: 40px;
        color: #fff;
        font-size: 15px;
      }
    }
  }
}
</style>

<style lang="scss">
.el-select-dropdown {
  .el-select-dropdown__wrap {
    overflow-x: hidden;
    .is-horizontal {
      display: none;
    }
  }
}
</style>
