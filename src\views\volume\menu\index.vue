<template>
  <div style="margin: 20px">
    <el-tabs v-model="majorId" type="border-card" @tab-click="handleClick">
      <el-tab-pane :label="item.majorName" :name="item.majorId" v-for="(item, index) in majors">
        <el-row :gutter="20" v-if="item.majorId == majorId">
          <el-col :span="8">
            <el-button type="primary" @click="openAdd(0)" size="medium">添加菜单</el-button>
            <div style="margin: 15px">
              <el-table @row-click="rowclick" :data="menus" height="750px" :default-expand-all="true" row-key="id" :tree-props="treeProps">
                <el-table-column prop="name" label="菜单名称">
                  <template slot-scope="scope">
                    <span :class="scope.row.id == parentId ? 'mainColor' : ''">{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button type="primary" @click="openAdd(scope.row.id)" size="mini"><i class="el-icon-plus"></i></el-button>
                    <el-button type="primary" @click="openEdit(scope.row)" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                    <el-button type="danger" @click="openDelete(scope.row.id, '菜单')" size="mini"><i class="el-icon-delete"></i></el-button>
                    <!--v-if="!scope.row.children"-->
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col :span="16">
            <span class="search_label">名称:</span>
            <el-input :disabled="!parentId" placeholder="请输入名称" prefix-icon="el-icon-search" v-model="name" style="width: 207px; margin-left: 10px" clearable />
            <el-button :disabled="!parentId" type="primary" @click="getGroups" size="medium" style="margin-left: 10px">搜索</el-button>
            <el-button :disabled="!parentId" type="primary" @click="openAddInfo" size="medium">添加练习</el-button>
            <!--<el-button :disabled='!parentId' type="success" @click="" icon="el-icon-upload2">导入练习</el-button>
						<el-button :disabled='!parentId' type="success" @click="" icon="el-icon-download">{{multipleSelect&& multipleSelect.length>0?'导出所选（'+multipleSelect.length+'）':'导出练习'}}</el-button>-->
            <div style="margin: 15px">
              <el-table :data="groups" border row-key="menuId" @selection-change="selectionChange">
                <el-table-column type="selection" width="55" align="center"> </el-table-column>
                <el-table-column prop="name" align="center" label="练习名称"> </el-table-column>
                <el-table-column prop="questionCount" align="center" label="题目数量"> </el-table-column>
                <el-table-column prop="menuNames" align="center" label="所在位置" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="createUserName" align="center" label="创建人"> </el-table-column>
                <el-table-column prop="createTime" align="center" label="创建时间"> </el-table-column>
                <el-table-column label="操作" align="center" width="280">
                  <template slot-scope="scope">
                    <el-button type="primary" @click="openViewInfo(scope.row)" size="mini">查看</el-button>
                    <el-button type="primary" @click="openEditInfo(scope.row)" size="mini">编辑</el-button>
                    <el-button type="danger" @click="openDelete(scope.row.menuId, '练习')" size="mini">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin: 10px; text-align: center" v-show="parentId">
                <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="添加菜单" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="permissionSaveDialog" width="350px">
      <el-form :model="addForm" :rules="rules" ref="addForm">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入菜单名称" maxlength="60" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionSaveReset">取 消</el-button>
        <el-button type="primary" @click="toAdd" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑菜单" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editDialog" width="350px">
      <el-form :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入菜单名称" maxlength="60" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editReset">取 消</el-button>
        <el-button type="primary" @click="edit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { menuTree, saveMenu, updateMenu, removeMenu, menuList } from '@/api/menu.js'
import { selectTeacherById } from '@/api/teacher.js'
import { putProgress } from '@/utils/oss.js'
import questionview from '@/views/question/view/index'
export default {
  components: {
    questionview
  },
  data() {
    return {
      userinfo: {},
      majors: [],
      majorId: '',
      name: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      menus: [],
      menusOri: [],
      groups: [],
      multipleSelect: [],
      parentId: '',
      dataloading: false,
      permissionSaveDialog: false,
      editDialog: false,
      treeProps: {
        children: 'children',
        hasChildren: null
      },
      menuSigns: [
        {
          name: '菜单',
          value: 0
        },
        {
          name: '题组',
          value: 1
        }
      ],
      addForm: {
        parentId: '',
        name: '',
        menuSign: '0'
      },
      editForm: {
        menuId: '',
        name: '',
        menuSign: '0'
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入菜单名称'
          }
        ],
        menuSign: [
          {
            required: true,
            message: '请输入菜单标识'
          }
        ]
      },
      dialogImport: false,
      importUserForm: {
        excelFile: '',
        name: ''
      },
      importUserRules: {
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        excelFile: [
          {
            required: true,
            message: '请选择导入文件'
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  watch: {
    parentId(val) {
      if (val) {
        this.getGroups()
      } else {
        this.pageNum = 1
        this.groups = []
      }
    }
  },
  methods: {
    getListFilter() {
      var name = this.name
      var menus = JSON.parse(JSON.stringify(this.menusOri))
      var newArr = []
      if (name) {
        function checkIt(checkArr) {
          checkArr.map((item) => {
            if (item.children && item.children.length > 0) {
              item.children = checkIt(item.children)
            }
          })
          var newArr = checkArr.filter((item) => {
            return item.name.includes(name) || (item.children && item.children.length > 0)
          })
          return newArr
        }
        newArr = checkIt(menus)
      } else {
        newArr = menus
      }
      this.menus = newArr
    },
    rowclick(row, column, event) {
      if (!row.children || row.children.length <= 0) {
        this.parentId = row.id
      } else {
        this.parentId = ''
      }
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var data = {
        name: this.name ? this.name : null,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        menuId: this.parentId,
        majorId: this.majorId,
        schoolId: this.userinfo.schoolId
      }
      menuList(data).then(async (res) => {
        this.groups = res.data.list
        this.total = res.data.total
      })
    },
    handleClick(tab, event) {
      this.reTree()
    },
    reTree() {
      this.name = ''
      this.getList()
    },
    getList() {
      var data = {
        menuSign: '0',
        majorId: this.majorId,
        schoolId: this.userinfo.schoolId
      }
      menuTree(data).then(async (res) => {
        this.menus = res.data
        this.menusOri = res.data
        this.parentId = ''
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        var majors = res.data.majors || []
        if (majors && majors.length > 0) {
          majors.map((item) => {
            item.majorId += ''
          })
          this.majorId = majors[0].majorId
          this.getList()
        }
        this.majors = majors
      })
    },
    openAdd(parentId) {
      this.permissionSaveDialog = true
      this.addForm.parentId = parentId
    },
    toAdd() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          saveMenu({
            parentId: this.addForm.parentId,
            name: this.addForm.name,
            menuSign: this.addForm.menuSign,
            majorId: this.majorId,
            schoolId: this.userinfo.schoolId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getList()
              this.permissionSaveReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    permissionSaveReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.permissionSaveDialog = false
    },
    openEdit(item) {
      this.editDialog = true
      this.editForm.menuId = item.id
      this.editForm.name = item.name
      this.editForm.menuSign = item.menuSign
    },
    edit() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          updateMenu({
            name: this.editForm.name,
            menuSign: this.editForm.menuSign,
            menuId: this.editForm.menuId,
            majorId: this.majorId,
            schoolId: this.userinfo.schoolId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getList()
              this.editReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    editReset() {
      this.editDialog = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    openViewInfo(item) {
      this.$router.replace({
        name: 'menuinfo',
        query: {
          menuId: item.menuId,
          parentId: item.parentId,
          majorId: item.majorId,
          name: item.name
        }
      })
    },
    openAddInfo() {
      this.$router.replace({
        name: 'menuadd',
        query: {
          parentId: this.parentId,
          majorId: this.majorId
        }
      })
    },
    openEditInfo(item) {
      this.$router.replace({
        name: 'menuadd',
        query: {
          menuId: item.menuId,
          parentId: item.parentId,
          majorId: item.majorId,
          name: item.name
        }
      })
    },
    openDelete(menuId, type) {
      this.$confirm('此操作将永久删除此' + type + ', 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeMenu({
            menuId: menuId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getList()
              this.parentId = ''
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>
