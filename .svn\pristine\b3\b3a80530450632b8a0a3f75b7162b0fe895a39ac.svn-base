<template>
  <div>
    <el-dialog title="病例权限" :visible="casePermissionDialog" destroy-on-close width="500px" custom-class="casePermissionDialog" @open="dialogOpen">
      <el-card>
        <div slot="header">班级列表</div>
        <el-tree ref="caseTree" class="tree" node-key="id" :data="classList" :props="props" check-on-click-node highlight-current @node-click="handleNodeClick"> </el-tree>
      </el-card>
      <el-card>
        <div slot="header"><el-checkbox v-show="studentId" v-model="allCase" @change="allChecked">病例列表</el-checkbox></div>
        <div v-show="studentId">
          <el-row v-for="item in casePermissionList" :key="item.caseId">
            <el-checkbox v-model="item.checked" @change="set_checkedItem(item)">{{ item.realName }}</el-checkbox>
          </el-row>
        </div>
      </el-card>

      <div slot="footer">
        <!-- <el-button @click="casePermissionDialog = false">取 消</el-button> -->
        <el-button type="primary" @click="permissionAllot">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { systemClbumAll } from '@/api/clbum'
import { selectTeacherById } from '@/api/teacher.js'
import { casePower_selectByStudentId, caseAllList, casePowerAdd } from '@/api/case'
export default {
  name: '',
  props: {
    casePermissionDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      casePermissionList: [],
      caseIds: [],
      allCase: false,
      classList: [],
      studentId: null,
      props: {
        label: 'name',
        children: 'children'
        // children: 'zones',
        // isLeaf: 'leaf'
      }
    }
  },

  created() {},
  methods: {
    dialogOpen() {
      this.getClbumAll()
      this.getCaseAll()
    },
    getClbumAll() {
      selectTeacherById().then(async (res) => {
        const { data } = await systemClbumAll({ schoolId: res.data.schoolId, clbumIds: res.data.clbums.map((item) => item.id) })
        this.classList = data
      })
    },
    // 查询所有病例
    async getCaseAll() {
      const { data } = await caseAllList({ caseType: 1 })
      this.casePermissionList = data
      this.casePermissionList.forEach((item) => {
        this.$set(item, 'checked', false)
      })
    },
    async allChecked(val) {
      if (!this.studentId) {
        return this.$message.warning('请选择用户')
      }
      if (val) {
        this.caseIds = this.casePermissionList.map((item) => item.caseId)
      } else {
        this.caseIds = []
      }
      this.casePermissionList.forEach((item) => {
        if (this.caseIds.includes(item.caseId)) {
          item.checked = true
        } else {
          item.checked = false
        }
      })
      await casePowerAdd({ caseIds: this.caseIds, studentId: this.studentId })
    },
    async set_checkedItem(item) {
      if (!this.studentId) {
        return this.$message.warning('请选择用户')
      }
      const isAllChecked = this.casePermissionList.some((list) => list.checked === false)
      if (isAllChecked) {
        this.allCase = false
      } else {
        this.allCase = true
      }
      this.caseIds = this.casePermissionList.filter((item) => item.checked).map((item) => item.caseId)

      await casePowerAdd({ caseIds: this.caseIds, studentId: this.studentId })
    },
    async handleNodeClick(val) {
      this.studentId = val.id
      const { data } = await casePower_selectByStudentId({ studentId: val.id })
      const caseIds = data.map((item) => item.caseId)
      this.casePermissionList = this.casePermissionList.map((item) => {
        if (caseIds.includes(item.caseId)) {
          return { ...item, checked: true }
        } else {
          return { ...item, checked: false }
        }
      })
      this.caseIds = this.casePermissionList
        .filter((item) => {
          if (item.checked) {
            return item
          }
        })
        .map((item) => item.caseId)
      if (this.caseIds.length === this.casePermissionList.length) {
        this.allCase = true
      } else {
        this.allCase = false
      }
      // this.studentId = null
      // this.caseIds = null
      // this.casePermissionList.forEach((item) => {
      //   item.checked = false
      // })
    },
    async permissionAllot() {
      this.$emit('update:casePermissionDialog', false)
      this.caseIds = []
      this.studentId = null
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .casePermissionDialog {
    .el-dialog__body {
      display: flex;
      justify-content: space-between;
      flex: 5;
      .el-card {
        .el-card__header,
        .el-checkbox__label {
          font-size: 16px;
          color: #1a1a1a;
        }
        &:first-of-type {
          flex: 2;
          margin-right: 15px;
          .el-card__body {
            padding: 10px;
            padding-top: 18px;
            max-height: 500px;
            overflow: auto;
            &::-webkit-scrollbar {
              width: 3px;
            }
            // 里面的滑块
            &::-webkit-scrollbar-thumb {
              background: #d2d2d2;
            }
            // 外面的背景
            &::-webkit-scrollbar-track-piece {
              background: transparent;
            }
          }
        }
        &:last-of-type {
          flex: 3;
          .el-card__body {
            padding: 10px;
            max-height: 500px;
            overflow: auto;
            &::-webkit-scrollbar {
              width: 3px;
            }
            // 里面的滑块
            &::-webkit-scrollbar-thumb {
              background: #d2d2d2;
            }
            // 外面的背景
            &::-webkit-scrollbar-track-piece {
              background: transparent;
            }
            .el-row {
              display: flex;
              align-items: center;
              // margin: 15px 0;
              height: 35px;
            }
          }
        }
        .el-checkbox {
          margin-right: 5px;
          .el-checkbox__inner {
            width: 20px;
            height: 20px;
            &::after {
              left: 6px;
              height: 11px;
              width: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
