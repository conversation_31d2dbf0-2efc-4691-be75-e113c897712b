import request from '@/utils/request'
//纠错

/**添加纠错 */
export function caseRectifyAdd(data) {
  return request({
    url: '/caseRectify/add',
    method: 'POST',
    data
  })
}
/**批量忽略纠错 */
export function caseRectifyRemoveBatch(data) {
  return request({
    url: '/caseRectify/removeBatch',
    method: 'POST',
    data
  })
}
/**列表 */
export function caseRectifyList(params) {
  return request({
    url: '/caseRectify/list',
    method: 'GET',
    params
  })
}
/**详情 */
export function caseRectifyDetail(params) {
  return request({
    url: '/caseRectify/detail',
    method: 'GET',
    params
  })
}

/**根据问题查询病例下匹配到关键字的问题(纠错时使用) */
export function caseRectifySelectQuestion(params) {
  return request({
    url: '/caseRectify/selectQuestion',
    method: 'GET',
    params
  })
}

/**修改(纠错时使用) */
export function caseRectifyUpdate(data) {
  return request({
    url: '/caseRectify/update',
    method: 'POST',
    data
  })
}
