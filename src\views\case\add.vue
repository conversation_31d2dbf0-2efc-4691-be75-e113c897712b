<template>
  <div>
    <el-dialog custom-class="addCaseDialog" :title="dialogTitle" :visible="addDialog" width="920px" :show-close="false" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" inline>
        <el-form-item label="疾病名称:" prop="name">
          <el-input v-model="form.name" size="small" maxlength="40" placeholder="请输入病例名称"></el-input>
        </el-form-item>
        <el-form-item label="病例类型:" prop="caseType">
          <el-radio-group v-model="form.caseType">
            <el-radio :label="1">学习病例</el-radio>
            <el-radio :label="2">考核病例</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="系统分类:" prop="form">
          <el-select v-model="form.form" placeholder="请选择系统分类" size="small">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名:" prop="realName">
          <el-input v-model="form.realName" size="small" maxlength="40" placeholder="请输入病例名称"></el-input>
        </el-form-item>
        <el-form-item label="年龄:" class="ageItem">
          <el-input-number v-model="form.age" :precision="0" size="small" :min="1" :max="120" label="请输入年龄"></el-input-number>
        </el-form-item>
        <el-form-item label="性别:">
          <el-radio-group v-model="form.sex">
            <el-radio label="F">女</el-radio>
            <el-radio label="M">男</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="病例简介:" class="mainDemands">
          <el-input v-model="form.mainDemands" type="textarea" size="small" maxlength="500" placeholder="请输入病例简介" resize="none" rows="8"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="closeBtn" @click="close">取 消</span>
        <span class="confirmBtn" @click="confirm">确 定</span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { caseFormList } from '@/filters'
import { caseAdd, caseUpdate } from '@/api/case'
export default {
  name: '',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    },
    isAi: {
      type: Boolean,
      require: false
    }
  },
  computed: {
    dialogTitle() {
      return this.form.caseId ? '编辑病例' : '添加病例'
    }
  },
  data() {
    return {
      form: {
        name: null,
        caseType: 1,
        form: null,
        realName: null,
        age: null,
        sex: 'F',
        mainDemands: null,
        createType: 1 //  创建类型 1 手动添加 2 AI创建
      },
      options: caseFormList,
      rules: {
        name: [{ required: true, message: '请输入病例名称', trigger: 'blur' }],
        caseType: [{ required: true, message: '请选择病例类型', trigger: 'change', type: 'number' }],
        form: [{ required: true, message: '请选择系统分类', trigger: 'change', type: 'number' }],
        realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    close() {
      this.form = {
        name: null,
        caseType: 1,
        form: null,
        realName: null,
        age: null,
        sex: 'F',
        mainDemands: null,
        createType: 1 //  创建类型 1 手动添加 2 AI创建
      }
      this.$refs['form'].resetFields()
      this.$emit('update:addDialog', false)
    },
    confirm() {
      this.$refs['form'].validate((val) => {
        if (val) {
          if (this.isAi) {
            const info = _.cloneDeep(this.form)
            this.close()
            this.$emit('success', info)
            return
          }
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.form.caseId) {
            caseUpdate(this.form)
              .then(() => {
                this.$message.success('修改病例成功！')
                loading.close()
                this.close()
                this.$emit('success')
              })
              .catch((err) => {
                console.log(err)
                loading.close()
              })
          } else {
            caseAdd({ ...this.form, allScore: 0 })
              .then(() => {
                this.$message.success('新增病例成功！')
                loading.close()
                this.close()
                this.$emit('success')
              })
              .catch((err) => {
                console.log(err)
                loading.close()
              })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .addCaseDialog {
    border-radius: 30px;
    .el-dialog__header {
      display: flex;
      align-items: center;
      justify-content: center;
      .el-dialog__title {
        color: #000;
        font-size: 30px;
      }
    }
    .el-dialog__body {
      padding-top: 20px;
      .el-form {
        display: flex;
        flex-wrap: wrap;
        .el-form-item {
          display: flex;
          align-items: center;
          width: 48%;
          .el-form-item__label {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #000000;
            &::before {
              color: #ff0000;
            }
          }
          .el-form-item__content {
            .el-input {
              .el-input__inner {
                width: 280px;
                height: 50px;
                background: #e9e9e9;
                border-radius: 10px;
                border: none;
                font-family: PingFang SC;
                font-size: 20px;
                color: #000;
                &::placeholder {
                  color: #999999;
                }
              }
            }
            .el-radio-group {
              display: flex;
              .el-radio {
                display: flex;
                align-items: center;
                .el-radio__inner {
                  width: 18px;
                  height: 18px;

                  background: #fff;
                  border-color: #b1b1b1;
                }
                .el-radio__label {
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  font-size: 20px;
                  color: #b1b1b1;
                }
              }

              .el-radio__input.is-checked .el-radio__inner {
                &::after {
                  background: #274e6a;
                  width: 12px;
                  height: 12px;
                  border-color: #274e6a;
                }
              }
              .el-radio__input.is-checked + .el-radio__label {
                color: #274e6a;
              }
            }
          }
        }
        .ageItem {
          .el-input-number {
            width: 200px;
            .el-input-number__increase,
            .el-input-number__decrease {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 53px;
              height: 50px;
              background: #274e6a;
              border-radius: 10px 0px 0px 10px;
              .el-icon-minus,
              .el-icon-plus {
                font-size: 30px;
                font-weight: bold;
                color: rgba($color: #fff, $alpha: 0.7);
                transition: all 0.2s;
                &:hover {
                  color: #fff;
                }
              }
            }
            .el-input-number__increase {
              border-radius: 0 10px 10px 0;
            }
            .is-disabled {
              .el-icon-minus,
              .el-icon-plus {
                color: rgba($color: #fff, $alpha: 0.3);
                &:hover {
                  color: rgba($color: #fff, $alpha: 0.3);
                }
              }
            }
            .el-input {
              .el-input__inner {
                width: 200px;
                height: 50px;
                background: #e9e9e9;
                border-radius: 10px;
                border: none;
                font-family: PingFang SC;
                font-size: 30px;
                color: #274e6a;
                &::placeholder {
                  color: #999999;
                }
              }
            }
          }
        }
        .mainDemands {
          width: 100%;
          .el-form-item__content {
            width: calc(100% - 140px);
            .el-textarea {
              .el-textarea__inner {
                width: 100%;
                height: 196px;
                background: #e9e9e9;
                border: none;
                border-radius: 10px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 20px;
                color: #000;

                &::placeholder {
                  color: #999999;
                }
              }
            }
          }
        }
      }
    }
    .el-dialog__footer {
      display: flex;
      align-items: center;
      justify-content: center;
      .closeBtn,
      .confirmBtn {
        width: 93px;
        height: 50px;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        text-align: center;
        color: #274e6a;
        line-height: 50px;
        cursor: pointer;
      }
      .confirmBtn {
        margin-left: 12px;
        background: #274e6a;
        border: none;
        color: #ffffff;
      }
    }
  }
}
</style>
<style lang="scss">
.el-select-dropdown__wrap {
  overflow-x: hidden;
  .el-select-dropdown__item {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #666666;
    &:hover {
      font-family: PingFang SC;
      color: #000000;
    }
  }
}
</style>
