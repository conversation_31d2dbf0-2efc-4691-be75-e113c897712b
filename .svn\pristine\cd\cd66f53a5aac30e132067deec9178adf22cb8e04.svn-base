import request from '@/utils/request'

// 权限树
export function permissionTree(data) {
  return request({
    url: '/system/permission/permissionTree',
    method: 'get',
    params: data
  })
}
// 根据角色id查询权限
export function permissionByRid(data) {
  return request({
    url: '/system/permission/permissionByRid',
    method: 'get',
    params: data
  })
}
// 根据角色id查询权限编码
export function permissionNumberByRid(data) {
  return request({
    url: '/system/permission/permissionNumberByRid',
    method: 'get',
    params: data
  })
}
// 新增权限
export function permissionSave(data) {
  return request({
    url: '/system/permission/permissionSave',
    method: 'post',
    data
  })
}
// 修改权限
export function permissionUpdate(data) {
  return request({
    url: '/system/permission/permissionUpdate',
    method: 'post',
    data
  })
}
// 删除权限
export function permissionRemove(data) {
  return request({
    url: '/system/permission/permissionRemove',
    method: 'delete',
    params: data
  })
}
