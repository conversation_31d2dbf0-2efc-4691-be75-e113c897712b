{"name": "vue-element-admin", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "format": "prettier --write ."}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "ali-oss": "^6.8.0", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "^2.6.11", "downloadjs": "^1.4.7", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "^2.13.0", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsmind": "^0.7.6", "jsonlint": "1.6.3", "jszip": "3.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "^0.0.2", "quill-image-resize-module": "^3.0.0", "screenfull": "4.2.0", "showdown": "1.9.0", "sortablejs": "1.8.4", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-quill-editor": "^3.0.6", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue2-editor": "^2.10.2", "vuedraggable": "2.20.0", "vuex": "3.1.0", "wangeditor": "^3.1.1", "xlsx": "0.14.1"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/preset-env": "^7.23.6", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "prettier": "^3.1.0", "runjs": "^4.3.2", "sass": "^1.26.8", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}