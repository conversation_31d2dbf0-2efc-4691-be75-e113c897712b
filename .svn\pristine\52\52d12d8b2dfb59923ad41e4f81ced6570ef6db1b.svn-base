<template>
  <div class="app-container">
    <div class="caseBody">
      <el-row type="flex" justify="center" align="middle">
        <el-button type="primary" @click="goBack" icon="el-icon-arrow-left">返回</el-button>
        <span class="moduleTitle">病例库</span>
      </el-row>
      <div class="caseDataContent">
        <el-form ref="form" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="病例名称:">
            <el-input v-model="queryInfo.name" maxlength="40" placeholder="请输入病例名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
          </el-form-item>
          <el-form-item label="病例类型:">
            <el-radio-group v-model="queryInfo.caseType" @change="getList">
              <el-radio :label="1">学习病例</el-radio>
              <el-radio :label="2">考核病例</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getList">查询</el-button>
            <el-button plain @click="reset">重置</el-button>
          </el-form-item>
          <el-form-item style="float: right">
            <el-button type="primary" @click="casePermissionDialog = true"> 病例权限 </el-button>
            <el-button type="primary" @click="addDialog = true">添加病例</el-button>
          </el-form-item>
        </el-form>
        <div class="caseContent">
          <ul>
            <li v-for="item in list" :key="item.caseId">
              <el-row type="flex" align="middle" justify="space-between">
                <div>
                  <Avatar :age="item.age" :sex="item.sex" />
                  <div class="info">
                    <div>
                      <span :class="`caseType${item.caseType}`">{{ item.caseType === 1 ? '学' : '考' }}</span>
                      <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
                        <span class="name">{{ item.name }}</span>
                      </el-tooltip>
                    </div>
                    <div>
                      <span>{{ item.realName }}</span>
                      <span>{{ item.age }}岁</span>
                    </div>
                  </div>
                </div>
                <div @click="historyCollect(item)">病史采集<i class="el-icon-arrow-right"></i></div>
              </el-row>
              <div class="statistics">
                <div>
                  <span>{{ item.questionCount }}</span>
                  <span>问题数量</span>
                </div>
                <div>
                  <span>{{ item.allScore }}</span>
                  <span>总分</span>
                </div>
              </div>
              <div class="operate">
                <div class="operateIcon">
                  <span @click="edit(item)">
                    <i class="el-icon-edit"></i>
                    编辑
                  </span>
                  <span @click="copy(item)">
                    <i class="el-icon-copy-document"></i>
                    复制
                  </span>
                  <span @click="del(item)">
                    <i class="el-icon-delete"></i>
                    删除
                  </span>
                </div>
                <div class="switch">
                  <el-switch v-model="item.isEnable" :width="38" :active-value="1" :inactive-value="0" active-color="#3f9cfd" inactive-color="#d5dae0" @change="switchChange($event, item)"> </el-switch>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div style="width: 100%; margin: 15px; text-align: center">
          <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total, prev, pager, next, jumper" :total="total"> </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增病例 -->
    <AddCase ref="AddCase" :addDialog.sync="addDialog" @success="getList" />
    <!-- 病例权限 -->
    <CasePermission :casePermissionDialog.sync="casePermissionDialog" />
  </div>
</template>
<script>
import { caseList, caseCopy, caseRemove, caseUpdate } from '@/api/case'
import AddCase from './add'
import CasePermission from '@/views/case/casePermission'
import Avatar from '@/views/case/Avatar'
export default {
  name: 'Case',
  components: {
    AddCase,
    CasePermission,
    Avatar
  },
  data() {
    return {
      queryInfo: {
        name: null,
        caseType: null,
        pageNum: 1,
        pageSize: 8
      },
      list: [],
      total: 0,
      addDialog: false,
      casePermissionDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null,
        caseType: null,
        pageNum: 1,
        pageSize: 8
      }
      this.getList()
    },
    historyCollect(item) {
      this.$router.push(`/case/historyCollect/${item.caseId}`)
    },
    copy(item) {
      this.$confirm('确定要复制该病例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseCopy({ ...item })
          this.$message({
            type: 'success',
            message: '复制成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消复制'
          })
        })
    },
    del(item) {
      this.$confirm('确定要删除该病例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseRemove({ id: item.caseId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    edit(item) {
      this.$refs['AddCase'].form = { ...item }
      this.addDialog = true
    },
    switchChange(val, item) {
      const typeLable = val ? '开启' : '禁用'
      this.$confirm(`确定要${typeLable}该病例吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          caseUpdate({ caseId: item.caseId, isEnable: val }).then(() => {
            this.$message.success(val ? '开启成功!' : '禁用成功!')
            this.getList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: `已取消${typeLable}`
          })
          this.getList()
        })
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  padding: 0;
  padding-bottom: 20px;
  background: #fff;
  .caseBody {
    height: 100%;
    width: 100%;
    padding: 20px 20px;
    padding-top: 30px;
    margin: 0 auto;
    & > .el-row > .el-button {
      position: absolute;
      left: 0;
    }
    .moduleTitle {
      font-size: 20px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      color: #333333;
    }
  }
}
::v-deep {
  .caseDataContent {
    .el-form {
      background: #fff;
      padding-top: 20px;
      padding-bottom: 20px;
      border-radius: 4px;
      border-bottom: 1px solid #e0e4e8;
      .el-form-item__label {
        font-size: 14px;
        font-family:
          Microsoft YaHei,
          Microsoft YaHei;
        font-weight: 400;
        color: #333333;
      }
      .el-form-item {
        margin-bottom: 0;
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked .el-radio__inner {
          background: #fff;
          &::after {
            background: #1890ff;
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }
}
.caseDataContent {
  height: calc(100% - 30px);
  margin-top: 25px;
  background: #ffffff;
  border: 1px solid #e0e4e8;
  .caseContent {
    display: flex;
    margin: 0 auto;
    padding-left: 49px;
    padding-top: 45px;
    ul {
      display: flex;
      flex-wrap: wrap;
      padding: 0;
      margin: 0;
      list-style: none;
      li {
        position: relative;
        width: 419px;
        height: 244px;
        padding: 18px;
        background: #ffffff;
        border-radius: 4px;
        border: 2px solid #e0e4e8;
        margin-right: 30px;
        margin-bottom: 30px;
        padding: 20px 24px;
        &:nth-of-type(4n) {
          margin-right: 0;
        }
        .el-row:first-of-type {
          width: 100%;
          margin-bottom: 14px;
          & > div:first-of-type {
            display: flex;

            align-items: center;
            .Avatar {
              position: relative;
              width: 49px;
              height: 49px;
              ::v-deep {
                .sexIcon {
                  position: absolute;
                  bottom: 0;
                  right: 0;
                  width: 16px;
                  height: 16px;
                }
              }
            }
            .info {
              margin-left: 7px;
              & > div:first-of-type {
                display: flex;
                align-items: center;
                .caseType1,
                .caseType2 {
                  width: 22px;
                  height: 22px;
                  line-height: 22px;
                  background: rgba(255, 120, 70, 0.3);
                  border-radius: 4px 4px 4px 4px;
                  font-size: 14px;
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  color: #ff7846;
                  text-align: center;
                }
                .caseType2 {
                  background: rgba(5, 191, 137, 0.2);
                  color: #05bf89;
                }
                .name {
                  max-width: 180px;

                  margin-left: 3px;
                  font-size: 20px;
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  color: #000000;

                  white-space: nowrap; /* 确保文本在同一行内显示 */
                  overflow: hidden; /* 隐藏溢出容器的文本 */
                  text-overflow: ellipsis; /* 使用省略号表示文本溢出部分 */
                }
              }
              & > div:last-of-type {
                margin-top: 5px;
                span {
                  margin-right: 10px;
                  font-size: 16px;
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  color: #666666;
                }
              }
            }
          }
          & > div:last-of-type {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #1890ff;
            cursor: pointer;
            i {
              font-weight: bold;
            }
          }
        }
        .statistics {
          display: flex;
          align-items: center;
          & > div {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 183px;
            height: 98px;
            background: #eeeeee;
            border-radius: 4px;
            & > span:first-of-type {
              font-size: 32px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: bold;
              color: #1890ff;
            }
            & > span:last-of-type {
              margin-top: 12px;
              font-size: 16px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #999999;
            }
          }
          & > div:last-of-type {
            margin-left: 5px;
          }
        }
        .operate {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-top: 16px;
          .operateIcon {
            display: flex;
            span {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 6px;
              width: 65px;
              height: 26px;
              background: #ffffff;
              border: 1px solid #999999;
              font-size: 14px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #999999;

              border-radius: 4px;

              cursor: pointer;
              i {
                margin-right: 3px;
                font-size: 18px;
                color: #a2b0bf;
                cursor: pointer;
              }
              &:hover {
                color: #1890ff;
                border-color: #2797ff;
                i {
                  color: #1890ff;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.caseNameTooltip {
  font-size: 18px;
}
</style>
