<template>
  <div class="home-container">
    <div class="home_top">
      <div class="title">
        <span>SP开放性人机对话系统</span>
        <span>SP open human-machine dialogue system</span>
      </div>
      <!-- <img src="@/assets/images/home_top.png" alt="" /> -->
    </div>
    <div class="bigBox">
      <section>
        <div v-if="isExist('system:system')" class="box" @click="jump(1)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>系统管理</span>
        </div>
        <div v-if="isExist('case:case')" class="box" @click="jump(2)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例库</span>
        </div>
        <div v-if="isExist('caseRectify:caseRectify')" class="box" @click="jump(3)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>问答纠错库</span>
        </div>
        <div v-if="isExist('caseExam:caseExam')" class="box" @click="jump(4)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>考核</span>
        </div>
        <div v-if="isExist('volume:volume')" class="box" @click="jump(5)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>理论考核</span>
        </div>
        <div v-if="isExist('question:questionlist')" class="box" @click="jump(6)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>题库</span>
        </div>
        <div v-if="isExist('casePractise:casePractise')" class="box" @click="jump(7)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例训练记录</span>
        </div>
        <div v-if="isExist('examGrade:examGrade')" class="box" @click="jump(8)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例考核成绩</span>
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {}
  },
  created() {},
  computed: {
    ...mapGetters(['permission_routes', 'roles'])
  },
  methods: {
    isExist(role) {
      return this.roles.includes(role)
    },
    jump(type) {
      // type: 1系统管理
      window.localStorage.setItem('sp_admin_modelType', type)
      this.$router.push(this.getFirstRoute(type))
    },
    getFirstRoute(type) {
      const route = this.permission_routes.filter((item) => {
        if (item.meta && item.meta.moduleType === type) {
          return item
        }
      })
      return route[0].path
    }
  }
}
</script>
<style scoped lang="scss">
.home-container {
  width: 100%;
  height: 100%;
  background: url('~@/assets/home/<USER>') no-repeat;
  overflow: hidden;
  .home_top {
    position: relative;
    width: 100%;
    height: 276px;
    img {
      width: 100%;
      height: 100%;
      object-fit: scale-down;
    }
    .title {
      position: absolute;
      left: 40px;
      top: 32px;
      display: flex;
      flex-direction: column;

      font-size: 26px;
      font-family:
        Microsoft YaHei,
        Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      & > span:last-of-type {
        margin-top: 4px;
        font-size: 15px;
      }
    }
  }
  .bigBox {
    position: relative;
    z-index: 1;
    margin: 0 auto;
    margin-top: -127px;
    padding: 0 52px;
    width: 1650px;
    height: 734px;

    section {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 60px;
      div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 278px;
        height: 171px;
        margin-right: 48px;
        margin-bottom: 48px;
        background: #3f77d4;
        border-radius: 6px 6px 6px 6px;
        cursor: pointer;
        &:nth-of-type(4n) {
          margin-right: 0;
        }
        & > span {
          margin-top: 23px;
          font-size: 20px;
          font-family:
            Microsoft YaHei,
            Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
