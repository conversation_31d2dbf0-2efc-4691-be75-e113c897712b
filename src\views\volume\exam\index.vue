<template>
  <div class="exam-container">
    <div class="major-select">
      <div class="major-select__title">选择专业(多选)</div>
      <div class="major-select__list">
        <div class="major-select__item" v-for="item in majors" :key="item.majorId" :class="{ active: majorIds.includes(item.majorId) }" @click="selectItem(item)">
          {{ item.majorName }}
        </div>
      </div>
    </div>
    <div class="exam-manage">
      <div class="exam-manage__search">
        <div class="search-field">
          <span class="search-field__label">试卷名称：</span>
          <el-input class="search-field__input" placeholder="请输入试卷名称" v-model="paperName" clearable />
        </div>
        <div class="search-field">
          <el-button @click="currentChange(1)">查询</el-button>
          <el-button class="reset-button" @click="research">重置</el-button>
          <el-button type="primary" @click="openAddInfo" style="margin-left: 5px">添加试卷</el-button>
          <el-button type="success" @click="openScore" icon="el-icon-setting">默认分值</el-button>
        </div>
      </div>
      <div class="exam-manage__table">
        <el-table :data="exams" row-key="paperId" cell-class-name="tableCellClassName" header-cell-class-name="tableHeaderClassName" @selection-change="selectionChange">
          <!-- <el-table-column type="selection" width="60" align="center"> </el-table-column> -->
          <el-table-column prop="paperName" label="试卷名称" width="width" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="majorName" label="所属专业" align="center" width="130px"> </el-table-column>
          <el-table-column prop="questionCount" label="试题数量" align="center" width="100"> </el-table-column>
          <el-table-column prop="totalScore" label="试卷分数" align="center" width="100"> </el-table-column>
          <el-table-column prop="passScore" label="及格分数" align="center" width="100"> </el-table-column>
          <el-table-column prop="duration" label="试卷时长(分钟)" align="center" width="150"> </el-table-column>
          <el-table-column label="状态" align="center" width="80px">
            <template slot-scope="scope">
              <el-tag type="primary" v-if="scope.row.disabled == '0'">未发布</el-tag>
              <el-tag type="success" v-if="scope.row.disabled == '1'">已发布</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" width="220"> </el-table-column>
          <el-table-column prop="createUserName" label="创建人" align="center" width="130"> </el-table-column>
          <el-table-column label="操作" align="center" width="310">
            <template slot-scope="scope">
              <div class="button-group" v-if="scope.row.disabled == '0'">
                <el-button type="primary" icon="el-icon-upload2" @click="openPublish(scope.row)" size="small">发布试卷</el-button>
                <el-button type="primary" icon="el-icon-edit" @click="openEditInfo(scope.row)" size="small">编辑</el-button>
                <el-button type="primary" icon="el-icon-delete" @click="openDelete(scope.row)" size="small">删除</el-button>
              </div>
              <div class="button-group" v-else>
                <el-button type="primary" icon="el-icon-document" @click="openViewInfo(scope.row)" size="small">查看</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="exam-manage__pagination">
        <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-size="pageSize" layout="total,  prev, pager, next" :total="total"> </el-pagination>
      </div>
    </div>
    <el-dialog title="发布试卷" :custom-class="'publish_dialog'" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="publishDiolog">
      <el-form :model="publishForm" :rules="rules" ref="publishForm" :inline="true">
        <el-form-item class="selectClbum" label="选择班级" prop="clbumIdsArr" label-width="130px">
          <el-button @click="changeClbum(item)" :type="publishForm.clbumIds.includes(item.id) ? 'primary' : ''" v-for="item in clbums" :key="item.id">{{ item.clbumName }}</el-button>
        </el-form-item>
        <el-form-item label="考试时间" prop="startTime" label-width="130px">
          <el-date-picker v-model="publishForm.examStartEnd" @change="examTimeChange" type="datetimerange" range-separator="至" start-placeholder="考试开始日期" end-placeholder="考试结束日期" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item>
        <el-form-item class="selectDate" label="成绩发布" prop="scorePublishTime" label-width="130px">
          <el-date-picker v-model="publishForm.scorePublishTime" @change="publishTimeChange" :picker-options="publishOptions" type="datetime" placeholder="选择成绩发布时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="批阅时间" prop="correctStartTime" label-width="110px">
          <el-date-picker v-model="publishForm.correctStartEnd" @change="correctTimeChange" :picker-options="correctOptions" :disabled="!publishForm.endTime" type="datetimerange" range-separator="至" start-placeholder="批阅开始日期" end-placeholder="批阅结束日期" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item>
   
        <el-form-item label="申诉截止" prop="complainEndTime" label-width="110px">
          <el-date-picker v-model="publishForm.complainEndTime" @change="complainEndTimeChange" :disabled="!publishForm.scorePublishTime" :picker-options="orrigendumOptions" type="datetime" placeholder="选择申诉截止时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetPublish">取 消</el-button>
        <el-button type="primary" @click="addPublish" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 设置默认分值 -->
    <el-dialog title="默认分值" custom-class="score_dialog" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="scoreDiolog">
      <el-form :model="addform" :rules="scoreFormRules" ref="addform">
        <el-form-item label="单选题分数:" label-width="160px" prop="SINGLE">
          <div class="numberInput-box">
            <el-input v-model="addform.SINGLE" type="number" placeholder="请输入分值"></el-input>
            <div class="number-controls">
              <span class="control-btn increment" :class="{ disabled: addform.SINGLE >= 100 }" @click="addform.SINGLE++">
                <i class="el-icon-arrow-up"></i>
              </span>
              <span class="control-btn decrement" :class="{ disabled: addform.SINGLE <= 1 }" @click="addform.SINGLE--">
                <i class="el-icon-arrow-down"></i>
              </span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="多选题分数:" label-width="160px" prop="MULTIPLE">
          <div class="numberInput-box">
            <el-input v-model="addform.MULTIPLE" type="number" placeholder="请输入分值"></el-input>
            <div class="number-controls">
              <span class="control-btn increment" :class="{ disabled: addform.MULTIPLE >= 100 }" @click="addform.MULTIPLE++">
                <i class="el-icon-arrow-up"></i>
              </span>
              <span class="control-btn decrement" :class="{ disabled: addform.MULTIPLE <= 1 }" @click="addform.MULTIPLE--">
                <i class="el-icon-arrow-down"></i>
              </span>
            </div>
          </div>
        </el-form-item>
        <!-- <el-form-item label="判断题" :label-width="'80px'" prop="JUDGE">
          <el-col :span="22">
            <el-input v-model="addform.JUDGE" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="填空题" :label-width="'80px'" prop="COMPLETION">
          <el-col :span="22">
            <el-input v-model="addform.COMPLETION" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="简答题" :label-width="'80px'" prop="SHORT">
          <el-col :span="22">
            <el-input v-model="addform.SHORT" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="配伍题" :label-width="'80px'" prop="COMPATIBILITY">
          <el-col :span="22">
            <el-input v-model="addform.COMPATIBILITY" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="组合题" :label-width="'80px'" prop="COMPREHENSIVE">
          <el-col :span="22">
            <el-input v-model="addform.COMPREHENSIVE" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scoreDiolog = false">取 消</el-button>
        <el-button @click="scoreChange" type="primary" :disabled="dataloading">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectSimulateParamList, updateSimulateParam, saveSimulateParam } from '@/api/param.js'
import { examinePaperList, saveExaminePaper, updateExaminePaper, removeExaminePaper, selectExaminePaperDetailById, releaseExaminePaper } from '@/api/paper.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入内容'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请输入数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    return {
      userinfo: {},
      majorIds: [],
      paperName: '',
      teacherName: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      exams: [],
      multipleSelect: [],
      clbums: [],
      majors: [],
      publishDiolog: false,
      scoreDiolog: false,
      dataloading: false,
      publishForm: {
        paperId: '',
        clbumIds: [],
        clbumIdsArr: '',
        examStartEnd: [],
        startTime: '',
        endTime: '',
        correctStartEnd: [],
        correctStartTime: '',
        correctEndTime: '',
        scorePublishTime: '',
        complainEndTime: ''
      },
      rules: {
        clbumIdsArr: [
          {
            required: true,
            message: '请选择发布班级'
          }
        ],
        startTime: [
          {
            required: true,
            message: '请选择考试时间'
          }
        ],
        correctStartTime: [
          {
            required: true,
            message: '请选择批改时间'
          }
        ],
        scorePublishTime: [
          {
            required: true,
            message: '请选择成绩发布时间'
          }
        ],
        complainEndTime: [
          {
            required: true,
            message: '请选择申诉截止时间'
          }
        ]
      },
      isEdited: false,
      addform: {
        SINGLE: '',
        MULTIPLE: '',
        JUDGE: '',
        COMPLETION: '',
        SHORT: '',
        COMPATIBILITY: '',
        COMPREHENSIVE: ''
      },
      scoreIds: [],
      scoreFormRules: {
        SINGLE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        MULTIPLE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        JUDGE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        COMPLETION: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        SHORT: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        COMPATIBILITY: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        COMPREHENSIVE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    correctOptions() {
      var that = this
      return {
        disabledDate(time) {
          let licenseStart = new Date(that.publishForm.endTime)
          licenseStart.setDate(licenseStart.getDate() + 1)
          return time.getTime() < licenseStart.getTime()
        }
      }
    },
    publishOptions() {
      var that = this
      return {
        disabledDate(time) {
          let licenseStart = new Date(that.publishForm.correctEndTime)
          licenseStart.setDate(licenseStart.getDate() + 1)
          return time.getTime() < licenseStart.getTime()
        }
      }
    },
    orrigendumOptions() {
      var that = this
      return {
        disabledDate(time) {
          let licenseStart = new Date(that.publishForm.scorePublishTime)
          licenseStart.setDate(licenseStart.getDate() + 1)
          return time.getTime() < licenseStart.getTime()
        }
      }
    }
  },
  methods: {
    selectItem(item) {
      if (this.majorIds.includes(item.majorId)) {
        this.majorIds = this.majorIds.filter((i) => {
          return i != item.majorId
        })
        this.currentChange(1)
      } else {
        this.majorIds.push(item.majorId)
        this.currentChange(1)
      }
    },
    research() {
      this.paperName = ''
      this.pageNum = 1
      this.majorIds = []
      this.getGroups()
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var majorIds = this.majorIds

      if (!majorIds || majorIds.length <= 0) {
        majorIds = this.majors.map((item) => {
          return item.majorId
        })
      }

      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        paperName: this.paperName ? this.paperName : null,
        majorIds: majorIds,
        teacherName: this.teacherName,
        schoolId: this.userinfo.schoolId
      }
      examinePaperList(data).then(async (res) => {
        this.exams = res.data.list
        this.total = res.data.total
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.clbums = res.data.clbums
        this.majors = res.data.majors
        this.getGroups()
      })
    },
    openScore() {
      this.getScoreList()
      this.scoreDiolog = true
    },
    scoreChange() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      var that = this
      this.$refs.addform.validate((valid) => {
        if (valid) {
          var addform = this.addform
          var paramReqs = []
          for (let i in addform) {
            var data = {
              code: i,
              value: addform[i],
              paramName: addform[i + 'name'],
              paramId: that.isEdited ? addform[i + 'id'] : null,
              schoolId: that.userinfo.schoolId
            }
            if (addform[i + 'name']) {
              paramReqs.push(data)
            }
          }
          if (this.isEdited) {
            updateSimulateParam(paramReqs).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.scoreDiolog = false
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          } else {
            saveSimulateParam({
              paramName: '默认分值_' + that.userinfo.name,
              code: 'score_rule',
              value: 'score_rule',
              parentId: '0',
              schoolId: that.userinfo.schoolId,
              paramReqs: paramReqs
            }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.scoreDiolog = false
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          }
        }
      })
    },
    getScoreList() {
      this.isEdited = false
      var teacherId = this.userinfo.teacherId
      selectSimulateParamList({
        teacherId: teacherId,
        schoolId: this.userinfo.schoolId,
        code: 'score_rule'
      }).then((res) => {
        if (res.data) {
          res.data.map((item) => {
            this.addform[item.code] = item.value
            this.addform[item.code + 'id'] = item.paramId
            this.addform[item.code + 'name'] = item.paramName
            if (item.teacherId == teacherId) {
              this.isEdited = true
            }
          })
        }
      })
    },
    openPublish(item) {
      this.publishForm.paperId = item.paperId
      this.publishDiolog = true
    },
    changeClbum(item) {
      if (this.publishForm.clbumIds.includes(item.id)) {
        var index = this.publishForm.clbumIds.indexOf(item.id)
        this.publishForm.clbumIds.splice(index, 1)
      } else {
        this.publishForm.clbumIds.push(item.id)
      }
      if (this.publishForm.clbumIds.length > 0) {
        this.publishForm.clbumIdsArr = this.publishForm.clbumIds.join(',')
      } else {
        this.publishForm.clbumIdsArr = ''
      }
    },
    examTimeChange() {
      if (this.publishForm.examStartEnd && this.publishForm.examStartEnd.length > 1) {
        this.publishForm.startTime = this.publishForm.examStartEnd[0]
        this.publishForm.endTime = this.publishForm.examStartEnd[1]
      } else {
        this.publishForm.startTime = ''
        this.publishForm.endTime = ''
      }
      this.publishForm.correctStartEnd = []
      this.correctTimeChange()
    },
    correctTimeChange() {
      if (this.publishForm.correctStartEnd && this.publishForm.correctStartEnd.length > 1) {
        this.publishForm.correctStartTime = this.publishForm.correctStartEnd[0]
        this.publishForm.correctEndTime = this.publishForm.correctStartEnd[1]
      } else {
        this.publishForm.correctStartTime = ''
        this.publishForm.correctEndTime = ''
      }
      this.publishForm.scorePublishTime = ''
      this.publishTimeChange()
    },
    publishTimeChange() {
      if (this.publishForm.correctEndTime && this.publishForm.scorePublishTime) {
        let time = new Date(this.publishForm.scorePublishTime)
        let licenseStart = new Date(this.publishForm.correctEndTime)
        licenseStart.setDate(licenseStart.getDate() + 1)
        if (time.getTime() < licenseStart.getTime()) {
          this.publishForm.scorePublishTime = ''
        }
      }
      this.publishForm.complainEndTime = ''
    },
    complainEndTimeChange() {
      if (this.publishForm.scorePublishTime && this.publishForm.complainEndTime) {
        let time = new Date(this.publishForm.complainEndTime)
        let licenseStart = new Date(this.publishForm.scorePublishTime)
        licenseStart.setDate(licenseStart.getDate() + 1)
        if (time.getTime() < licenseStart.getTime()) {
          this.publishForm.complainEndTime = ''
        }
      }
    },
    addPublish() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.publishForm.validate((valid) => {
        if (valid) {
          var nowData = new Date()
          var endTime = new Date(this.publishForm.endTime)
          if (endTime < nowData) {
            this.$message({
              type: 'error',
              message: '考试结束时间在当前时间之前，无法发布！'
            })
            return
          }
          var data = Object.assign({}, this.publishForm)
          releaseExaminePaper(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getGroups()
              this.resetPublish()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    resetPublish() {
      this.publishDiolog = false
      this.publishForm.clbumIds = []
      this.publishForm.examStartEnd = []
      this.publishForm.correctStartEnd = []
      this.$refs.publishForm.resetFields()
      this.$refs.publishForm.clearValidate()
    },
    openViewInfo(item) {
      this.$router.replace({
        name: 'examinfo',
        query: {
          paperId: item.paperId
        }
      })
    },
    openAddInfo() {
      this.$router.replace({
        name: 'examadd'
      })
    },
    openEditInfo(item) {
      this.$router.replace({
        name: 'examadd',
        query: {
          paperId: item.paperId
        }
      })
    },
    openDelete(item) {
      this.$confirm('此操作将永久删除此试卷, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'generalConfirm'
      })
        .then(() => {
          removeExaminePaper({
            paperId: item.paperId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getGroups()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.exam-container {
  width: 100%;
  height: 100%;
  display: flex;
  .major-select {
    width: 300px;
    height: 100%;
    border-right: 1px solid #e4e4e4;
    &__title {
      padding: 20px 0 30px 38px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #303030;
    }
    &__item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 60px;
      padding-left: 38px;
      cursor: pointer;
      &:hover {
        background: #f6f8fa;
      }
    }
    &__item.active {
      background: #f6f8fa;
      color: #274e6a;
      font-weight: 600;
    }
  }
  .exam-manage {
    position: relative;
    flex: 1;
    height: 100%;
    padding-left: 20px;
    padding-top: 30px;
    padding-right: 20px;
    overflow: auto;
    &__search {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      .search-field {
        display: flex;
        align-items: center;
        margin-right: 30px;
        &__label {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #666666;
        }
        &__input {
          width: 300px;
          height: 45px;
          ::v-deep {
            .el-input__inner {
              width: 100%;
              height: 100%;
              border-radius: 74px;
              background: #eef0f2;
              border: none;
              color: #333333;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              &::placeholder {
                color: #cccccc;
              }
            }
            .el-input__suffix {
              top: 3px;
              right: 10px;
              .el-input__suffix-inner {
                .el-icon-circle-close {
                  font-size: 20px;
                }
              }
            }
          }
        }
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 45px;
          padding: 0 18px;
          margin-right: 20px;
          background: #65849a;
          border-radius: 63px 63px 63px 63px;
          border: none;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          &:first-of-type {
            margin-right: 10px;
          }
        }
        .reset-button {
          background: #fff;
          border: 1px solid #65849a;
          color: #65849a;
        }
      }
    }
    &__table {
      .el-table {
        &::before {
          display: none;
        }
        ::v-deep {
          .tableCellClassName {
            height: 55px;
            background: #f6f8fa;
            border-bottom: 4px solid #fff;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            &:first-of-type {
              border-radius: 8px 0 0 8px;
            }
            &:last-of-type {
              border-radius: 0 8px 8px 0;
            }

            .el-checkbox {
              &__input.is-checked {
                .el-checkbox__inner {
                  background-color: #274e6a;
                  border-color: #274e6a;
                }
              }
              &__inner {
                width: 28px;
                height: 28px;
                border-radius: 4px 4px 4px 4px;
                &::after {
                  width: 8px;
                  height: 14px;
                  left: 8px;
                  top: 2px;
                  border-width: 2px;
                }
              }
            }
          }
          .tableHeaderClassName {
            height: 55px;
            border-bottom: 4px solid #fff;
            background: #f6f8fa;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #999999;
            &:first-of-type {
              border-radius: 8px 0 0 8px;
            }
            &:nth-of-type(10) {
              border-radius: 0 8px 8px 0;
            }
            .el-checkbox {
              &__input.is-checked,
              &__input.is-indeterminate {
                .el-checkbox__inner {
                  background-color: #274e6a;
                  border-color: #274e6a;
                }
              }
              &__inner {
                width: 28px;
                height: 28px;
                border-radius: 4px 4px 4px 4px;
                &::after {
                  width: 8px;
                  height: 14px;
                  left: 8px;
                  top: 2px;
                  border-width: 2px;
                }
                &::before {
                  width: 14px;
                  top: 12px;
                  left: 50%;
                  transform: translateX(-50%) scale(1);
                }
              }
            }
          }

          .button-group {
            display: flex;
            align-items: center;
            justify-content: space-around;
          }
          .el-tag {
            position: relative;
            left: -5px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 66px;
            height: 38px;
            padding: 0;
            border-radius: 8px 8px 8px 8px;

            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            &.el-tag--success {
              border-color: #c2dfc6;
              background: #e2efe7;
              color: #33a141;
            }
            &.el-tag--danger {
              border-color: #ffd3d3;
              background: #f3e7e9;
              color: #dd4b4b;
            }
          }
          .el-button {
            display: flex;
            align-items: center;
            justify-content: center;

            // min-width: 78px;
            // height: 38px;
            padding: 10px;
            margin: 0;
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #e2e2e2;

            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            i {
              font-size: 16px;
              margin-right: 4px;
            }
            span {
              margin-left: 0;
            }

            &--primary {
              color: #3381b9;
            }
          }
        }
      }
    }
    &__pagination {
      position: absolute;
      left: 50%;
      bottom: 30px;
      transform: translateX(-50%);
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep {
  .score_dialog {
    width: 608px;
    height: 385px;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    .el-dialog__header {
      padding-top: 30px;
      .el-dialog__title {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
      }
      .el-dialog__headerbtn {
        top: 30px;
        .el-icon-close {
          font-size: 24px;
          font-weight: 600;
          color: #666666;
        }
      }
    }
    .el-dialog__body {
      padding-top: 60px;
      padding-left: 59px;
      .el-form {
        .el-form-item {
          margin-bottom: 30px;
          &__label {
            position: relative;
            top: 5px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #555555;
          }
          &__content {
            .numberInput-box {
              position: relative;
              width: 294px;
              height: 50px;
              .el-input {
                width: 100%;
                height: 100%;
                &__inner {
                  width: 100%;
                  height: 100%;
                  padding-right: 50px;
                  background: #e9e9e9;
                  border: none;
                  border-radius: 10px 10px 10px 10px;
                  font-size: 20px;
                  /* 隐藏默认的上下箭头 */
                  &::-webkit-outer-spin-button,
                  &::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                  }
                }
              }
              .number-controls {
                position: absolute;
                right: 0;
                top: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 46px;
                height: 100%;
                border: 1px solid #ebebeb;
                border-radius: 0 10px 10px 0;
                overflow: hidden;

                .control-btn {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 100%;
                  height: 25px;
                  background: #ffffff;
                  cursor: pointer;
                  &:last-of-type {
                    border-top: 1px solid #ebebeb;
                  }
                  &.disabled {
                    cursor: not-allowed;
                    opacity: 0.5;
                  }
                }
              }
            }
          }
        }
      }
    }
    .el-dialog__footer {
      padding: 0;
      .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 92px;
        height: 50px;
        border-radius: 63px 63px 63px 63px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        &--primary {
          background: #274e6a;
          border-color: #274e6a;
          color: #ffffff;
        }
        &--default {
          background: #fff;
          color: #274e6a;
          border: 1px solid #274e6a;
        }
      }
    }
  }
  .publish_dialog {
    width: 720px;
    height: 500px;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    .el-dialog__header {
      padding-top: 30px;
      .el-dialog__title {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
      }
      .el-dialog__headerbtn {
        top: 30px;
        .el-icon-close {
          font-size: 24px;
          font-weight: 600;
          color: #666666;
        }
      }
    }

    .el-dialog__body {
      padding-top: 30px;
      .el-form {
        .el-form-item {
          display: flex;
          margin-bottom: 30px;
          &__label {
            position: relative;
            top: 5px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #555555;
          }
          &__content {
            flex: 1;

            .el-date-editor {
              width: 500px;
              height: 45px;
              padding-right: 0;
              background: #eef0f2;
              border-radius: 74px 74px 74px 74px;
              border: none;
              .el-range__icon {
                font-size: 20px;
                margin-left: 10px;
                margin-top: 5px;
              }
              .el-range-separator {
                margin-top: 12px;
              }
              .el-range-input {
                background: transparent;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                &::placeholder {
                  color: #cccccc;
                }
              }
              .el-range__close-icon {
                margin-right: 0px;
                margin-top: 5px;
                font-size: 20px;
              }
            }
          }
          &.selectClbum {
            .el-form-item__content {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
            }
          }
          &.selectDate {
            .el-input__inner {
              width: 100%;
              height: 100%;
              padding-left: 57px;
              border-radius: 74px;
              background: #eef0f2;
              border: none;
              color: #333333;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 16px;
              &::placeholder {
                color: #cccccc;
              }
            }
            .el-input__prefix {
              .el-icon-time {
                font-size: 20px;
                margin-left: 15px;
                margin-top: 2px;
              }
            }
            .el-input__suffix {
              top: 3px;
              right: 10px;
              .el-input__suffix-inner {
                .el-icon-circle-close {
                  font-size: 20px;
                }
              }
            }
          }
          .el-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            padding: 10px;
            margin: 0;
            margin-right: 10px;
            margin-bottom: 10px;
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #65849a;

            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #65849a;
            i {
              font-size: 16px;
              margin-right: 4px;
            }
            span {
              margin-left: 0;
            }
            &:nth-of-type(5) {
              margin-right: 0;
            }
            &--primary {
              color: #fff;
              background: #65849a;
            }
          }
        }
      }
    }
    .el-dialog__footer {
      padding: 0;
      .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 92px;
        height: 50px;
        border-radius: 63px 63px 63px 63px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        &--primary {
          background: #274e6a;
          border-color: #274e6a;
          color: #ffffff;
        }
        &--default {
          background: #fff;
          color: #274e6a;
          border: 1px solid #274e6a;
        }
      }
    }
  }
}
</style>
