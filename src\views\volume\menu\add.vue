<template>
  <div class="clearfix">
    <div class="topform_cont clearfix">
      <el-form :model="topform" :rules="rules" ref="topform" class="clearfix">
        <div class="topicinfo_top">
          <el-row>
            <el-col :span="5">
              <el-form-item label="练习名称：" :label-width="labelwidth" prop="name">
                <el-input v-model="topform.name" placeholder="请输入练习名称" maxlength="60" style="width: 207px"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="menuinfo_botttom">
          <el-col :span="10">
            <el-form-item label="试题添加：" :label-width="labelwidth" prop="questionExerciseReqList">
              <el-button v-for="item in addtypes" @click="changeType(item)" :type="addtype == item.value ? 'primary' : ''">
                {{ item.name }}
              </el-button>
              <el-button @click="regettopics" type="primary"> 题库选择 </el-button>
              <el-tooltip class="item" effect="dark" content="您在自定义添加界面与复制粘贴界面添加的试题，将会自动存入非正式题库中" placement="bottom">
                <div class="tip_btn"><i class="el-icon-question"></i></div>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </div>
      </el-form>
      <div class="menuinfo">
        <el-form :model="form" :rules="rules" ref="addForm">
          <el-row>
            <el-col :span="24" v-if="addtype == '1'">
              <el-form-item label="难度：" :label-width="labelwidth" prop="difficulty">
                <el-radio-group v-model="form.difficulty">
                  <el-radio :label="item.value" v-for="item in difficultys">{{ item.name }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="题型：" :label-width="labelwidth" prop="questionType">
                <el-radio-group v-model="form.questionType" @change="topicChange">
                  <el-radio :label="item.value" v-for="item in questionTypes">{{ item.name }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <div v-if="questionType">
                <questioninput ref="questioninput" :letters="letters" :form="form" :questionType="questionType"></questioninput>
              </div>
              <div style="text-align: center">
                <el-button @click="jumpQuestion(-1)" :disabled="!beforeBtn" type="primary" v-if="viewIndex >= 0">上一题</el-button>
                <el-button @click="beforeCheck" type="primary" v-if="form.questionType">保存</el-button>
                <el-button @click="jumpQuestion(1)" :disabled="!afterBtn" type="primary" v-if="viewIndex >= 0">下一题</el-button>
              </div>
            </el-col>
            <el-col :span="24" v-if="addtype == '2'">
              <paste @beforePasteCheck="beforePasteCheck"></paste>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="menulist_cont">
      <div class="menulist_conttop">
        <el-button type="primary" @click="checkAll" :disabled="addLoading">提交练习</el-button>
      </div>
      <div class="menuinfo_list">
        <div class="menuinfo_top">
          练习题
          <span>题目总数：{{ topform.questionExerciseReqList.length }}</span>
        </div>
        <div class="menuinfo_info" v-for="questionType in questionTypes" v-if="getArray(questionType.value).length > 0">
          <div class="menuinfo_infoname">{{ questionType.name }}（共{{ getArray(questionType.value).length }}题）</div>
          <div class="menuinfo_infolist clearfix" v-show="getArray(questionType.value).length > 0">
            <div @click.stop="openList(item, index)" v-for="(item, index) in getArray(questionType.value)" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
              <div class="question_delete" @click.stop="deleteList(item, index)"><i class="el-icon-close"></i></div>
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择试题" width="65%" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="tableshow">
      <div style="height: 34px; font-size: 16px; font-weight: bold">当前选择题数：{{ selections.length }}</div>
      <div style="margin-bottom: 10px">
        <!--<span class="search_label">所属专业：</span>
				<el-select v-model="questionMajorId" placeholder="请选择所属专业" @change='handleCurrentChange(1)' clearable>
					<el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId">
					</el-option>
				</el-select>-->
        <span class="search_label">题干名称：</span>
        <el-input placeholder="请输入题干名称" width="30px" v-model="questionSele" @change="handleCurrentChange(1)" style="width: 200px; margin-left: 5px" clearable />
        <span class="search_label">题目类型：</span>
        <el-select v-model="questionSeleType" placeholder="请选择题目类型" @change="handleCurrentChange(1)" clearable>
          <el-option v-for="item in questionTypes" :key="item.value" :label="item.name" :value="item.value"> </el-option>
        </el-select>
        <span class="search_label">创建时间：</span>
        <el-date-picker v-model="questionStartEnd" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"> </el-date-picker>
        <el-button type="primary" @click="handleCurrentChange(1)" style="margin-left: 5px">搜索</el-button>
      </div>
      <el-table :data="topics" border row-key="questionId" @selection-change="selectionChange" ref="topics">
        <el-table-column type="selection" width="55" align="center"> </el-table-column>
        <el-table-column prop="question" label="题干">
          <template slot-scope="scope">
            <span v-if="scope.row.questionType != 'COMPATIBILITY'">
              <span class="editor_box" v-html="scope.row.question"></span>
            </span>
            <div v-else>
              <div class="list_question" v-for="(item, index) in getQuestion(scope.row)">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="questionType" label="题型" align="center">
          <template slot-scope="scope">
            <span v-for="item in questionTypes" v-if="item.value == scope.row.questionType">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" align="center">
          <template slot-scope="scope">
            <span v-for="item in difficultys" v-if="item.value == scope.row.difficulty">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="majorName" label="所属专业" align="center"> </el-table-column>
        <el-table-column prop="questionUse" label="所属题库" align="center">
          <template slot-scope="scope">
            <span v-for="item in questionUses" v-if="item.value == scope.row.questionUse">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"> </el-table-column>
        <el-table-column prop="createUserName" label="创建人" align="center"> </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300, 2000]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="addLoading" @click="calcelSelection">取 消</el-button>
        <el-button :disabled="addLoading" type="primary" @click="addSelection()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { questionList, selectQuestionDetail } from '@/api/question.js'
import { questionExerciseList, saveQuestionExercise, updateQuestionExercise } from '@/api/menu.js'
import { selectTeacherById } from '@/api/teacher.js'
import questioninput from '@/views/question/input/index'
import paste from '@/views/question/input/paste'
import { questionTypes } from '@/filters'

export default {
  components: {
    questioninput,
    paste
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    var checkArray = (rule, value, callback) => {
      if (!value || value.length <= 0) {
        return callback(new Error('请添加试题'))
      } else {
        return callback()
      }
    }
    return {
      menuId: this.$route.query.menuId,
      majorId: this.$route.query.majorId,
      parentId: this.$route.query.parentId,
      name: this.$route.query.name,
      labelwidth: '110px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: null,
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      SINGLE: [],
      MULTIPLE: [],
      JUDGE: [],
      COMPLETION: [],
      SHORT: [],
      COMPATIBILITY: [],
      COMPREHENSIVE: [],
      viewIndex: -1,
      viewType: -1,
      beforeBtn: true,
      afterBtn: true,
      topform: {
        name: '',
        sumScore: 0,
        questionExerciseReqList: [],
        deleteIds: []
      },
      socorRegular: [],
      form: {
        majorId: '',
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        scoreType: 1,
        score: 0,
        sortState: '0'
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入练习名称'
          }
        ],
        score: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入分数'
          }
        ],
        questionExerciseReqList: [
          {
            validator: checkArray
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      //试题选择
      questionMajorId: '',
      questionSeleType: '',
      questionStartEnd: '',
      questionSele: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      topics: [],
      selections: [],
      selectionIndex: 0,
      tableshow: false,
      addLoading: false
    }
  },
  mounted() {
    this.getUserInfo()
  },
  watch: {
    'form.list'(val) {
      console.log(val)
    }
  },
  methods: {
    checkAll() {
      this.$refs.topform.validate((valid) => {
        if (valid) {
          this.addLoading = true
          var menuReq = {
            majorId: this.majorId,
            parentId: this.parentId,
            schoolId: this.userinfo.schoolId,
            menuSign: 1,
            name: this.topform.name,
            deleteIds: this.topform.deleteIds
          }
          var questionExerciseReqList = this.topform.questionExerciseReqList
          if (this.menuId) {
            menuReq.menuId = this.menuId
            updateQuestionExercise({
              menuUpdateReq: menuReq,
              questionExerciseReqList
            }).then((res) => {
              this.addLoading = false
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.closeCheck()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          } else {
            saveQuestionExercise({
              menuReq,
              questionExerciseReqList
            }).then((res) => {
              this.addLoading = false
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.closeCheck()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          }
        }
      })
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    regettopics() {
      this.questionMajorId = this.majorId
      this.questionSeleType = ''
      this.questionStartEnd = []
      this.questionSele = ''
      this.pageNum = 1
      this.selections = []
      this.topics = []
      this.gettopics()
      this.tableshow = true
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.gettopics()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.gettopics()
    },
    gettopics() {
      var questionIds = []
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value].map((item) => {
          if (item.questionId) {
            questionIds.push(item.questionId)
          }
        })
      })
      var data = {
        majorId: this.questionMajorId ? this.questionMajorId : null,
        question: this.questionSele ? this.questionSele : null,
        questionType: this.questionSeleType ? this.questionSeleType : null,
        startTime: this.questionStartEnd && this.questionStartEnd.length == 2 ? this.questionStartEnd[0] : null,
        endTime: this.questionStartEnd && this.questionStartEnd.length == 2 ? this.questionStartEnd[1] : null,
        questionIds: questionIds,
        disabled: 1,
        questionUse: 1,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    selectionChange(val) {
      this.selections = val
    },
    calcelSelection() {
      this.selections = []
      this.tableshow = false
      this.$refs.topics.clearSelection()
    },
    addSelection() {
      var selections = this.selections
      if (selections && selections.length > 0) {
        this.addLoading = true
        this.selectionIndex = 0
        this.getAndAdd()
      } else {
        this.$message({
          type: 'error',
          message: '请选择试题'
        })
      }
    },
    getAndAdd() {
      var selections = this.selections
      var selectionIndex = this.selectionIndex
      var socorRegular = this.socorRegular
      selectQuestionDetail({
        questionId: selections[selectionIndex].questionId
      }).then((res) => {
        var item = Object.assign({}, selections[selectionIndex], res.data)
        var inSetArr = this[item.questionType]
        var flagList = inSetArr.filter((it) => {
          return it.questionId == item.questionId
        })
        if (flagList.length == 0) {
          this[item.questionType].push(item)
        }
        this.nextAdd()
      })
    },
    nextAdd() {
      if (this.selectionIndex < this.selections.length - 1) {
        this.selectionIndex++
        this.getAndAdd()
      } else {
        this.selections = []
        this.selectionIndex = 0
        this.$refs.topics.clearSelection()
        this.tableshow = false
        this.addLoading = false
        this.sumSelection()
      }
    },
    sumSelection() {
      var sumScore = 0
      var questionExerciseReqList = []
      var questionTypes = this.questionTypes
      questionTypes.map((item) => {
        questionExerciseReqList = questionExerciseReqList.concat(this[item.value])
      })
      questionExerciseReqList.map((item, index) => {
        var score = item.score
        score = Number(score)
        if (!isNaN(score)) {
          sumScore += score
        }
      })
      this.topform.questionExerciseReqList = questionExerciseReqList
      this.topform.sumScore = sumScore
    },
    deleteList(item, index) {
      this[item.questionType].splice(index, 1)
      if (index == this.viewIndex) {
        this.clearCheck()
      }
      if (item.questionExerciseId) {
        this.topform.deleteIds.push(item.questionExerciseId)
      }
      this.sumSelection()
    },
    openList(item, index) {
      this.clearCheck()
      this.addtype = '1'
      var socorRegular = this.socorRegular
      this.questionType = null
      this.viewIndex = index
      this.viewType = item.questionType
      var questionArr = ['', '', '', '']
      var optionsArr = ['', '', '', '']
      var answers = []
      if (item.questionType == 'COMPATIBILITY') {
        try {
          var questionObject = JSON.parse(item.question)
          questionArr = Object.values(questionObject)
        } catch (err) {}
      }
      if (item.options) {
        try {
          var optionObject = JSON.parse(item.options)
          optionsArr = Object.values(optionObject)
        } catch (err) {}
      }
      var newitem = JSON.parse(JSON.stringify(item))
      this.form = Object.assign({}, this.form, newitem, {
        questionArr,
        optionsArr,
        answers: item.answer ? item.answer.split('') : [],
        answer: item.answer ? item.answer : '',
        score: 0,
        scoreType: 1
      })
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    pushList(item) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      if (viewType == -1 || (viewType != -1 && item.questionType != viewType)) {
        this[item.questionType].push(item)
        if (viewType != -1) {
          this[viewType].splice(viewIndex, 1)
        }
      } else {
        this[item.questionType][viewIndex] = item
      }
      this.sumSelection()
      this.clearCheck()
    },
    getArray(questionType) {
      return this[questionType]
    },
    jumpQuestion(type) {
      this.beforeBtn = true
      this.afterBtn = true
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      var questionTypes = this.questionTypes
      var typeIndex = ''
      questionTypes.map((item, index) => {
        if (item.value == viewType) {
          typeIndex = index
        }
      })
      if (type < 0) {
        if (viewIndex > 0) {
          viewIndex--
          this.openList(this[viewType][viewIndex], viewIndex)
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = newInfo.theArray.length - 1
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.beforeBtn = false
          }
        }
      }
      if (type > 0) {
        if (viewIndex < this[viewType].length - 1) {
          viewIndex++
          this.openList(this[viewType][viewIndex], viewIndex)
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = 0
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.afterBtn = false
          }
        }
      }
    },
    getBefore(typeIndex, type) {
      var questionTypes = this.questionTypes
      if (type < 0) {
        if (typeIndex <= 0) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex--
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
      if (type > 0) {
        if (typeIndex >= questionTypes.length - 1) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex++
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
    },
    beforeCheck() {
      this.theCheck()
        .then((data) => {
          data.isEdited = true
          this.pushList(data)
        })
        .catch((err) => {})
    },
    theCheck() {
      return new Promise((resolve, reject) => {
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            this.$refs.questioninput
              .beforeCheck()
              .then((resdata) => {
                var data = Object.assign({}, this.form, resdata)
                resolve(data)
              })
              .catch((err) => {
                reject()
              })
          } else {
            reject()
          }
        })
      })
    },
    beforePasteCheck(item) {
      var data = Object.assign({}, item, {
        majorId: this.majorId
      })
      this.pushList(data)
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.majorId,
          questionUse: 1,
          difficulty: '',
          score: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: ''
        }
      )
      this.viewIndex = -1
      this.viewType = -1
      this.questionType = null
      this.$refs.addForm.resetFields()
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    topicChange() {
      var socorRegular = this.socorRegular
      this.questionType = null
      var form = {
        majorId: this.majorId,
        questionUse: this.form.questionUse,
        difficulty: this.form.difficulty,
        questionType: this.form.questionType,
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        sortState: '0'
      }
      this.$set(this, 'form', form)
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    changeType(item) {
      if (this.addtype != item.value) {
        this.addtype = item.value
      } else {
        if (item.value == '1') {
          this.clearCheck()
        }
      }
    },
    getInfo() {
      questionExerciseList({
        menuId: this.menuId
      }).then((res) => {
        var questionExerciseReqList = res.data
        questionExerciseReqList.map((item) => {
          item.list = item.questionExerciseAnswers
          this.pushList(item)
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (this.menuId) {
          this.topform.name = this.name
          this.getInfo()
        }
      })
    },
    closeCheck() {
      var view = this.$route
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>
