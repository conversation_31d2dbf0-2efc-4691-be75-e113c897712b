<template>
  <div class="clearfix questionPaper-details">
    <div class="questionPaper-header">
      <div class="header_back" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="header-title">{{ topform.paperName }}</div>
    </div>
    <div class="questionPaper-body">
      <el-form :model="topform" :rules="rules" ref="topform" class="clearfix">
        <div class="topicinfo_top">
          <el-row>
            <el-col :span="6">
              <el-form-item label="所属专业：" :label-width="labelwidth">
                {{ topform.majorName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="试卷时长：" :label-width="labelwidth"> {{ topform.duration }}分钟 </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="及格分数：" :label-width="labelwidth">
                {{ topform.passScore }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="试卷分数：" :label-width="labelwidth">
                {{ topform.totalScore }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div class="question-view">
        <div class="question_info">
          <div class="menuinfo">
            <template v-if="questionType">
              <div class="topic_des" :class="form.questionType">
                {{ showQuestionType(form.questionType) }}
              </div>
              <div>
                <questionview ref="questionview" :splited="true" :letters="letters" :form="form" :difficultys="difficultys" v-if="questionType"></questionview>
              </div>
            </template>
          </div>
          <div class="button-group">
            <el-button @click="jumpQuestion(-1)" :disabled="!beforeBtn" type="primary" v-if="viewIndex >= 0">上一题</el-button>
            <el-button @click="jumpQuestion(1)" :disabled="!afterBtn" type="primary" v-if="viewIndex >= 0">下一题</el-button>
          </div>
        </div>

        <!-- <div class="menulist_conttop">
            <el-button type="primary" @click="closeCheck" :disabled="addLoading">确定</el-button>
          </div> -->
        <div class="menuinfo_list">
          <div class="menuinfo_top">
            <span class="menuinfo-title">试题列表</span>
            <span class="menuinfo-total">小题数量：{{ topform.paperQuestionDetailDtoList.length }}</span>
          </div>
          <div class="menuinfo_info" v-for="questionType in questionTypes" :key="questionType.value" v-show="getArray(questionType.value).length > 0">
            <div class="menuinfo_infoname">{{ questionType.name }}（共{{ getArray(questionType.value).length }}题, 共 {{ questionTypeTotalScore(getArray(questionType.value)) }}分）</div>
            <div class="menuinfo_infolist clearfix" v-show="getArray(questionType.value).length > 0">
              <!-- <div v-for="(item, index) in getArray(questionType.value)" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
                  {{ item.indexId }}
                </div> -->

              <div v-for="(item, index) in getArray(questionType.value)" :key="index" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
                <span class="question-type" :class="questionType.value">{{ questionType.name }}</span>
                <el-tooltip effect="dark" :content="stripHtmlTags(item.question)" placement="top-start">
                  <span class="question-content"> {{ stripHtmlTags(item.question) }}</span>
                </el-tooltip>
                <div class="question-operate">
                  <span class="question-operate__details" @click.stop="openList(item, index)">
                    <i class="el-icon-document"></i>
                    详情</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { examinePaperList, saveExaminePaper, updateExaminePaper, removeExaminePaper, selectExaminePaperDetailById, releaseExaminePaper } from '@/api/paper.js'
import { selectTeacherById } from '@/api/teacher.js'
import questionview from '@/views/question/view/index'
import { questionTypes } from '@/filters'
import { stripHtmlTags } from '@/utils'

export default {
  components: {
    questionview
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    var checkArray = (rule, value, callback) => {
      if (!value || value.length <= 0) {
        return callback(new Error('请添加试题'))
      } else {
        return callback()
      }
    }
    return {
      paperId: this.$route.query.paperId,
      labelwidth: '150px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: null,
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      SINGLE: [],
      MULTIPLE: [],
      JUDGE: [],
      COMPLETION: [],
      SHORT: [],
      COMPATIBILITY: [],
      COMPREHENSIVE: [],
      viewIndex: -1,
      viewType: -1,
      beforeBtn: true,
      afterBtn: true,
      topform: {
        paperName: '',
        totalScore: 0,
        paperQuestionDetailDtoList: [],
        duration: '',
        passScore: '',
        majorId: '',
        paperQuestionIds: []
      },
      form: {
        score: '',
        majorId: '',
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: []
      },
      rules: {
        paperName: [
          {
            required: true,
            message: '请输入试卷名称'
          }
        ],
        score: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入分数'
          }
        ],
        paperQuestionDetailDtoList: [
          {
            validator: checkArray
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      //试题选择
      questionMajorId: '',
      questionSeleType: '',
      questionSele: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      selections: [],
      selectionIndex: 0,
      tableshow: false,
      addLoading: false
    }
  },
  mounted() {
    this.getUserInfo()
  },
  computed: {
    questionTypeTotalScore() {
      return (data) => {
        let totalScore = 0
        data.forEach((question) => {
          totalScore += question.list.reduce((pre, next) => {
            return pre + next.score
          }, 0)
        })
        return totalScore
      }
    },
    showQuestionType() {
      return (val) => {
        return questionTypes.filter((item) => item.value == val)[0].name
      }
    }
  },
  methods: {
    stripHtmlTags,
    goBack() {
      this.$router.push('/volume')
    },
    sumSelection() {
      var sumScore = 0
      var paperQuestionDetailDtoList = []
      var questionTypes = this.questionTypes
      questionTypes.map((item) => {
        paperQuestionDetailDtoList = paperQuestionDetailDtoList.concat(this[item.value])
      })
      paperQuestionDetailDtoList.map((item, index) => {
        var score = item.score
        score = Number(score)
        if (!isNaN(score)) {
          sumScore += score
        }
      })
      this.topform.paperQuestionDetailDtoList = paperQuestionDetailDtoList
      this.topform.sumScore = sumScore
    },
    openList(item, index) {
      this.questionType = null
      this.viewIndex = index
      this.viewType = item.questionType

      this.beforeBtn = true
      this.afterBtn = true
      var questionArr = ['', '', '', '']
      var optionsArr = ['', '', '', '']
      var answers = []
      if (item.questionType == 'COMPATIBILITY') {
        this.splited = true
        try {
          var questionObject = JSON.parse(item.question)
          questionArr = Object.values(questionObject)
        } catch (err) {}
      } else {
        this.splited = false
      }
      if (item.options) {
        try {
          var optionObject = JSON.parse(item.options)
          optionsArr = Object.values(optionObject)
        } catch (err) {}
      }
      this.form = Object.assign({}, item, {
        questionArr,
        optionsArr,
        answers: item.answer ? item.answer.split('') : []
      })
      this.$nextTick(() => {
        this.questionType = this.form.questionType

        var typeIndex = ''
        this.questionTypes.map((item, index) => {
          if (item.value == this.viewType) {
            typeIndex = index
          }
        })
        if (this.viewIndex == 0) {
          var newInfo = this.getBefore(typeIndex, -1)
          if (!newInfo || !newInfo.hasitem) {
            this.beforeBtn = false
          }
        } else if (this.viewIndex == this[this.viewType].length - 1) {
          var newInfo = this.getBefore(typeIndex, 1)
          if (!newInfo || !newInfo.hasitem) {
            this.afterBtn = false
          }
        }
      })
    },
    pushList(item) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      if (viewType == -1 || (viewType != -1 && item.questionType != viewType)) {
        this[item.questionType].push(item)
        if (viewType != -1) {
          this[viewType].splice(viewIndex, 1)
        }
      } else {
        this[item.questionType][viewIndex] = item
      }
      this.sumSelection()
      this.clearCheck()
    },
    getArray(questionType) {
      return this[questionType]
    },
    jumpQuestion(type) {
      this.beforeBtn = true
      this.afterBtn = true
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      var questionTypes = this.questionTypes
      var typeIndex = ''
      questionTypes.map((item, index) => {
        if (item.value == viewType) {
          typeIndex = index
        }
      })
      if (type < 0) {
        if (viewIndex > 0) {
          viewIndex--
          this.openList(this[viewType][viewIndex], viewIndex)

          if (viewIndex == 0) {
            var newInfo = this.getBefore(typeIndex, type)
            if (!newInfo || !newInfo.hasitem) {
              this.beforeBtn = false
            }
          }
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = newInfo.theArray.length - 1
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.beforeBtn = false
          }
        }
      }
      if (type > 0) {
        if (viewIndex < this[viewType].length - 1) {
          viewIndex++
          this.openList(this[viewType][viewIndex], viewIndex)
          if (viewIndex == this[viewType].length - 1) {
            var newInfo = this.getBefore(typeIndex, type)
            if (!newInfo || !newInfo.hasitem) {
              this.afterBtn = false
            }
          }
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = 0
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.afterBtn = false
          }
        }
      }
    },
    getBefore(typeIndex, type) {
      var questionTypes = this.questionTypes
      if (type < 0) {
        if (typeIndex <= 0) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex--
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
      if (type > 0) {
        if (typeIndex >= questionTypes.length - 1) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex++
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.majorId,
          questionUse: 1,
          difficulty: '',
          score: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: ''
        }
      )
      this.viewIndex = -1
      this.viewType = -1
      this.questionType = null
      // this.$refs.addForm.resetFields()
      // this.$refs.addForm.clearValidate()
    },
    getInfo() {
      selectExaminePaperDetailById({
        paperId: this.paperId
      }).then((res) => {
        this.topform = res.data
        var paperQuestionDetailDtoList = res.data.paperQuestionDetailDtoList
        paperQuestionDetailDtoList.map((item, index) => {
          var indexId = this[item.questionType].length + 1
          if (item.questionType != 'COMPATIBILITY' && item.questionType != 'COMPREHENSIVE') {
            item.list = item.paperQuestionAnswerList
            item.indexId = indexId
            this.pushList(item)
          } else {
            if (item.paperQuestionAnswerList && item.paperQuestionAnswerList.length > 0) {
              item.paperQuestionAnswerList.map((it, ide) => {
                var newItem = Object.assign({}, item)
                newItem.list = [it]
                newItem.indexId = indexId + '-' + (ide + 1)
                newItem.splited = true
                this.pushList(newItem)
              })
            }
          }
        })
        this.$nextTick(() => {
          //打开第一个
          if (paperQuestionDetailDtoList && paperQuestionDetailDtoList.length > 0) {
            var openFirst = false
            this.questionTypes.map((item) => {
              if (this[item.value] && this[item.value].length > 0) {
                if (!openFirst) {
                  this.openList(this[item.value][0], 0)
                  openFirst = true
                }
              }
            })
          }
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (this.paperId) {
          this.getInfo()
        }
      })
    },
    closeCheck() {
      this.$router.push('/volume')
      // var view = this.$route
      // this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
      //   if (this.isActive(view)) {
      //     this.toLastView(visitedViews, view)
      //   }
      // })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>

<style lang="scss">
.questionPaper-details {
  width: 100%;
  height: 100%;
  padding: 0 50px 20px;
  background: linear-gradient(180deg, #d0dae4 0%, #f4f4f4 100%);
  .questionPaper-header {
    position: relative;
    padding-top: 34px;
    text-align: center;
    .header_back {
      position: absolute;
      left: 50px;
      top: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .header-title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      line-height: 35px;
    }
    .save-button {
      position: absolute;
      right: 0;
      top: 36px;
      width: 200px;
      height: 50px;
      background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
      border-radius: 63px 63px 63px 63px;
      border: none;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #ffffff;
    }
  }
  .questionPaper-body {
    position: relative;
    width: 100%;
    margin-top: 30px;
    height: 800px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px 30px 30px 30px;
    overflow: auto;
    &::-webkit-scrollbar {
      background: transparent;
    }

    .topicinfo_top {
      padding: 25px 0;
      padding-left: 170px;
      border-bottom: 1px solid #e4e4e4;
      .el-form-item {
        margin-bottom: 0;
        &__label {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 26px;
          color: #333;
        }
        &__content {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 26px;
          color: #274e6a;
        }
      }
    }
    .question-view {
      display: flex;
      // align-items: center;
      height: calc(100% - 87px);
      padding: 20px;
      .question_info {
        width: 964px;
        height: 100%;
        position: relative;
        .menuinfo {
          position: relative;
          width: 100%;
          height: 620px;
          padding: 20px 25px;
          background: #ffffff;
          border-radius: 20px 20px 20px 20px;
          border: 1px solid #dddee3;
          overflow: auto;
          & > .topic_des {
            position: absolute;
            left: 100px;
            top: 20px;

            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 26px;
            border-radius: 4px 4px 4px 4px;

            font-family: PingFang SC;
            font-weight: 400;
            font-size: 12px;
            &.SINGLE {
              background: #3d8aff;
              color: #fff;
            }
            &.MULTIPLE {
              background: #2eb2ff;
              color: #fff;
            }
          }
        }
        .button-group {
          position: absolute;
          left: 50%;
          bottom: -10px;
          transform: translateX(-50%);
          display: flex;
          align-items: center;
          .el-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 93px;
            height: 50px;
            background: #274e6a;
            border-color: #274e6a;
            border-radius: 63px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #ffffff;
            &:last-of-type {
              margin-left: 20px;
            }
            &--info {
              color: #274e6a;
              background: #fff;
            }
            &.is-disabled {
              opacity: 0.6;
            }
          }
        }
      }
      .menuinfo_list {
        flex: 1;
        height: 620px;

        margin-left: 20px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #dddee3;
        transition: all 0.3s ease;
        overflow: auto;
        .menuinfo_top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 60px;
          padding: 0 30px;
          background: #fafafa;
          border-bottom: 1px solid #dddee3;
          .menuinfo-title {
            position: relative;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #000000;
            &::before {
              content: '';
              position: absolute;
              left: -10px;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 20px;
              background: #274e6a;
            }
          }
          .menuinfo-total {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #000000;
            margin-left: 35px;
          }
        }
        .menuinfo_info {
          padding: 20px;
          .menuinfo_infoname {
            margin-bottom: 20px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 20px;
            color: #333333;
          }
          .menuinfo_infolist {
            .question_answer {
              position: relative;
              display: flex;
              align-items: center;

              width: 100%;
              height: 60px;
              padding: 0 20px;
              margin-bottom: 16px;

              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #dddee3;
              &.doing {
                background: rgba($color: #3d8aff, $alpha: 0.1);
              }
              .question-type {
                width: 60px;
                height: 26px;
                line-height: 26px;
                border-radius: 4px 4px 4px 4px;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #ffffff;
                text-align: center;
                &.SINGLE {
                  background: #3d8aff;
                }
                &.MULTIPLE {
                  background: #2eb2ff;
                }
                &.JUDGE {
                  background: #009900;
                }
              }
              .question-content {
                margin-left: 20px;
                max-width: 65%;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 18px;
                color: #000000;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .question-operate {
                position: absolute;
                right: 20px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 16px;
                &__details {
                  color: #3d8aff;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
}

.el-tooltip__popper {
  font-size: 15px;
  line-height: 26px;
}
</style>
