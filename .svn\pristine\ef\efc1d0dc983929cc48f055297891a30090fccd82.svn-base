<template>
  <div class="clearfix">
    <div class="topform_cont clearfix">
      <el-form :model="topform" :rules="rules" ref="topform" class="clearfix">
        <div class="topicinfo_top">
          <el-row>
            <el-col :span="4">
              <el-form-item label="所属专业：" :label-width="labelwidth">
                {{ topform.majorName }}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="试卷名称：" :label-width="labelwidth">
                <div class="text-ellipsis">
                  <el-tooltip class="item" effect="dark" :content="topform.paperName" placement="top">
                    <span>{{ topform.paperName }}</span>
                  </el-tooltip>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="试卷时长：" :label-width="labelwidth"> {{ topform.duration }}分钟 </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="及格分数：" :label-width="labelwidth">
                {{ topform.passScore }}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="试卷分数：" :label-width="labelwidth">
                {{ topform.totalScore }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div class="menuinfo">
        <el-form :model="form" :rules="rules" ref="addForm">
          <el-row v-if="questionType">
            <el-col :span="24">
              <el-form-item label="" :label-width="labelwidth">
                <div class="topic_des">
                  <el-tag v-if="form.questionType == item.value" v-for="item in questionTypes">{{ item.name }}</el-tag>
                </div>
                <div v-if="questionType">
                  <questionview ref="questionview" :splited="true" :letters="letters" :form="form" :difficultys="difficultys" v-if="questionType"></questionview>
                </div>
              </el-form-item>
              <div style="text-align: center; margin: 20px 0">
                <el-button @click="jumpQuestion(-1)" :disabled="!beforeBtn" type="primary" v-if="viewIndex >= 0">上一题</el-button>
                <el-button @click="jumpQuestion(1)" :disabled="!afterBtn" type="primary" v-if="viewIndex >= 0">下一题</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="menulist_cont">
      <div class="menulist_conttop">
        <el-button type="primary" @click="closeCheck" :disabled="addLoading">确定</el-button>
      </div>
      <div class="menuinfo_list">
        <div class="menuinfo_top">
          试题列表
          <span>小题数量：{{ topform.paperQuestionDetailDtoList.length }}</span>
        </div>
        <div class="menuinfo_info" v-for="questionType in questionTypes" v-if="getArray(questionType.value).length > 0">
          <div class="menuinfo_infoname">{{ questionType.name }}（共{{ getArray(questionType.value).length }}题）</div>
          <div class="menuinfo_infolist clearfix" v-show="getArray(questionType.value).length > 0">
            <div @click="openList(item, index)" v-for="(item, index) in getArray(questionType.value)" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
              {{ item.indexId }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { examinePaperList, saveExaminePaper, updateExaminePaper, removeExaminePaper, selectExaminePaperDetailById, releaseExaminePaper } from '@/api/paper.js'
import { selectTeacherById } from '@/api/teacher.js'
import questionview from '@/views/question/view/index'
import { questionTypes } from '@/filters'

export default {
  components: {
    questionview
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    var checkArray = (rule, value, callback) => {
      if (!value || value.length <= 0) {
        return callback(new Error('请添加试题'))
      } else {
        return callback()
      }
    }
    return {
      paperId: this.$route.query.paperId,
      labelwidth: '110px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: null,
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      SINGLE: [],
      MULTIPLE: [],
      JUDGE: [],
      COMPLETION: [],
      SHORT: [],
      COMPATIBILITY: [],
      COMPREHENSIVE: [],
      viewIndex: -1,
      viewType: -1,
      beforeBtn: true,
      afterBtn: true,
      topform: {
        paperName: '',
        totalScore: 0,
        paperQuestionDetailDtoList: [],
        duration: '',
        passScore: '',
        majorId: '',
        paperQuestionIds: []
      },
      form: {
        score: '',
        majorId: '',
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: []
      },
      rules: {
        paperName: [
          {
            required: true,
            message: '请输入试卷名称'
          }
        ],
        score: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入分数'
          }
        ],
        paperQuestionDetailDtoList: [
          {
            validator: checkArray
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      //试题选择
      questionMajorId: '',
      questionSeleType: '',
      questionSele: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      selections: [],
      selectionIndex: 0,
      tableshow: false,
      addLoading: false
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    sumSelection() {
      var sumScore = 0
      var paperQuestionDetailDtoList = []
      var questionTypes = this.questionTypes
      questionTypes.map((item) => {
        paperQuestionDetailDtoList = paperQuestionDetailDtoList.concat(this[item.value])
      })
      paperQuestionDetailDtoList.map((item, index) => {
        var score = item.score
        score = Number(score)
        if (!isNaN(score)) {
          sumScore += score
        }
      })
      this.topform.paperQuestionDetailDtoList = paperQuestionDetailDtoList
      this.topform.sumScore = sumScore
    },
    openList(item, index) {
      this.questionType = null
      this.viewIndex = index
      this.viewType = item.questionType
      var questionArr = ['', '', '', '']
      var optionsArr = ['', '', '', '']
      var answers = []
      if (item.questionType == 'COMPATIBILITY') {
        this.splited = true
        try {
          var questionObject = JSON.parse(item.question)
          questionArr = Object.values(questionObject)
        } catch (err) {}
      } else {
        this.splited = false
      }
      if (item.options) {
        try {
          var optionObject = JSON.parse(item.options)
          optionsArr = Object.values(optionObject)
        } catch (err) {}
      }
      this.form = Object.assign({}, item, {
        questionArr,
        optionsArr,
        answers: item.answer ? item.answer.split('') : []
      })
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    pushList(item) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      if (viewType == -1 || (viewType != -1 && item.questionType != viewType)) {
        this[item.questionType].push(item)
        if (viewType != -1) {
          this[viewType].splice(viewIndex, 1)
        }
      } else {
        this[item.questionType][viewIndex] = item
      }
      this.sumSelection()
      this.clearCheck()
    },
    getArray(questionType) {
      return this[questionType]
    },
    jumpQuestion(type) {
      this.beforeBtn = true
      this.afterBtn = true
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      var questionTypes = this.questionTypes
      var typeIndex = ''
      questionTypes.map((item, index) => {
        if (item.value == viewType) {
          typeIndex = index
        }
      })
      if (type < 0) {
        if (viewIndex > 0) {
          viewIndex--
          this.openList(this[viewType][viewIndex], viewIndex)
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = newInfo.theArray.length - 1
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.beforeBtn = false
          }
        }
      }
      if (type > 0) {
        if (viewIndex < this[viewType].length - 1) {
          viewIndex++
          this.openList(this[viewType][viewIndex], viewIndex)
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = 0
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.afterBtn = false
          }
        }
      }
    },
    getBefore(typeIndex, type) {
      var questionTypes = this.questionTypes
      if (type < 0) {
        if (typeIndex <= 0) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex--
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
      if (type > 0) {
        if (typeIndex >= questionTypes.length - 1) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex++
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.majorId,
          questionUse: 1,
          difficulty: '',
          score: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: ''
        }
      )
      this.viewIndex = -1
      this.viewType = -1
      this.questionType = null
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
    },
    getInfo() {
      selectExaminePaperDetailById({
        paperId: this.paperId
      }).then((res) => {
        this.topform = res.data
        var paperQuestionDetailDtoList = res.data.paperQuestionDetailDtoList
        paperQuestionDetailDtoList.map((item, index) => {
          var indexId = this[item.questionType].length + 1
          if (item.questionType != 'COMPATIBILITY' && item.questionType != 'COMPREHENSIVE') {
            item.list = item.paperQuestionAnswerList
            item.indexId = indexId
            this.pushList(item)
          } else {
            if (item.paperQuestionAnswerList && item.paperQuestionAnswerList.length > 0) {
              item.paperQuestionAnswerList.map((it, ide) => {
                var newItem = Object.assign({}, item)
                newItem.list = [it]
                newItem.indexId = indexId + '-' + (ide + 1)
                newItem.splited = true
                this.pushList(newItem)
              })
            }
          }
        })
        this.$nextTick(() => {
          //打开第一个
          if (paperQuestionDetailDtoList && paperQuestionDetailDtoList.length > 0) {
            var openFirst = false
            this.questionTypes.map((item) => {
              if (this[item.value] && this[item.value].length > 0) {
                if (!openFirst) {
                  this.openList(this[item.value][0], 0)
                  openFirst = true
                }
              }
            })
          }
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (this.paperId) {
          this.getInfo()
        }
      })
    },
    closeCheck() {
      var view = this.$route
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>
