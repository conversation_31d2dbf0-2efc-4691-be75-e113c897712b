import request from '@/utils/request'
//设备管理

/**设备列表 */
export function caseDeviceList(params) {
  return request({
    url: '/caseDevice/list',
    method: 'get',
    params
  })
}
/**设备详情 */
export function caseDeviceDetais(params) {
  return request({
    url: '/caseDevice/detail',
    method: 'get',
    params
  })
}

/** 添加设备 */
export function caseDeviceAdd(data) {
  return request({
    url: '/caseDevice/add',
    method: 'post',
    data
  })
}

/** 修改设备 */
export function caseDeviceUpdate(data) {
  return request({
    url: '/caseDevice/update',
    method: 'post',
    data
  })
}

/** 删除设备 */
export function caseDeviceRemove(params) {
  return request({
    url: '/caseDevice/remove',
    method: 'DELETE',
    params
  })
}

/** 绑定电脑地址判重 */
export function caseDeviceRepeatMac(params) {
  return request({
    url: '/caseDevice/repeatMac',
    method: 'GET',
    params
  })
}
