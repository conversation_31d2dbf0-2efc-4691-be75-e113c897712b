import request from '@/utils/request'

//查询专业列表
export function majorList(data) {
  return request({
    url: '/system/major/majorList',
    method: 'get',
    params: data
  })
}
//添加专业
export function majorSave(data) {
  return request({
    url: '/system/major/majorSave',
    method: 'post',
    data
  })
}
//修改专业信息
export function majorUpdate(data) {
  return request({
    url: '/system/major/majorUpdate',
    method: 'post',
    data
  })
}
//删除专业
export function majorRemove(data) {
  return request({
    url: '/system/major/majorRemove',
    method: 'delete',
    params: data
  })
}

//修改 启用/禁用状态
export function updateState(data) {
  return request({
    url: '/system/major/updateState',
    method: 'post',
    params: data
  })
}
