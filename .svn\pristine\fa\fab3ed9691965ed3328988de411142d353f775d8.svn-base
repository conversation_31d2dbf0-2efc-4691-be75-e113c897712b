<template>
  <div>
    <!-- 选择病例 -->
    <el-dialog title="选择病例" :visible="selectCaseDialog" width="900px" custom-class="selectCase" center @open="getAllList" @close="close" :close-on-click-modal="false">
      <el-form ref="form" :model="queryInfo" label-width="80px" inline>
        <el-form-item label="病例名称:" label-width="80px">
          <el-input v-model="queryInfo.name" clearable placeholder="请输入病例名称" @clear="getCaseList" @keydown.native.enter="getCaseList"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="small" @click="getCaseList">查询</el-button>
          <el-button type="primary" size="small" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="selectCaseBox">
        <div v-for="(item, index) in list" :key="item.caseId" :class="[{ isChecked: checkedCaseIds.includes(item.caseId) }, 'selectCaseItem']" @click="checked(item, index)">
          <span v-if="checkedCaseIds.includes(item.caseId)" class="checkedIcon">
            <i class="el-icon-check"></i>
          </span>
          <div class="caseItem_top_left">
            <casePhoto :sex="item.sex" :age="item.age" />
          </div>
          <div class="caseItem_top_right">
            <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
              <div class="caseName">{{ item.name }}</div>
            </el-tooltip>
            <div class="patientInfo">
              <span> {{ item.realName }} </span>
              <svg-icon :icon-class="item.sex === 'M' ? 'nan' : 'nv'"></svg-icon>
              <span> {{ item.age }} 岁</span>
            </div>
            <div class="score">
              <span>病例总分</span>
              <span> {{ item.allScore }}分</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <el-pagination style="margin-top: 25px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next, jumper" :total="total" @size-change="getCaseList" @current-change="getCaseList"> </el-pagination>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confimSelect">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseList } from '@/api/case'
import casePhoto from '@/components/casePhoto'

export default {
  name: '',
  components: {
    casePhoto
  },
  props: {
    selectCaseDialog: {
      type: Boolean,
      require: true
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      // 选择病例
      queryInfo: {
        name: null,
        sort: 1,
        caseType: 2,
        isEnable: 1,
        orderby: 1,
        pageNum: 1,
        pageSize: 4
      },
      allList: [],
      list: [],
      total: 0,
      checkedCaseIds: []
    }
  },
  created() {},
  methods: {
    async getAllList() {
      const { data } = await caseList({ ...this.queryInfo, pageSize: 2000 })
      this.allList = data.list
    },
    async getCaseList() {
      const { data } = await caseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      // 给每一个病例增加权重
      this.list.forEach((item) => {
        this.$set(item, 'weight', null)
      })
    },
    reset() {
      this.queryInfo = {
        name: null,
        sort: 1,
        caseType: 2,
        isEnable: 1,
        orderby: 1,
        pageNum: 1,
        pageSize: 4
      }
      this.getCaseList()
    },
    checked(item) {
      if (this.checkedCaseIds.includes(item.caseId)) {
        this.checkedCaseIds = this.checkedCaseIds.filter((li) => li !== item.caseId)
      } else {
        this.checkedCaseIds.push(item.caseId)
      }
    },
    confimSelect() {
      // 多选逻辑
      const list = this.allList.filter((item) => {
        return this.checkedCaseIds.includes(item.caseId)
      })
      this.close()
      this.$emit('success', list)
    },
    close() {
      this.$emit('update:selectCaseDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog__title {
    font-size: 22px;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: bold;
    color: #000000;
  }
  .el-dialog__headerbtn {
    .el-dialog__close {
      font-size: 26px;
      width: 26px;
      height: 26px;
      color: #999999;
    }
  }
  .el-form {
    .el-form-item {
      margin-bottom: 0;
    }
    .el-form-item__label {
      font-size: 16px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      color: #000000;
    }
    .el-input__inner {
      width: 220px;
      height: 40px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #dcdfe6;
    }
  }
  .el-dialog__body {
    padding-left: 40px;
  }
  .el-dialog__footer {
    padding-top: 30px;
  }
}
.selectCaseBox {
  display: flex;
  flex-wrap: wrap;

  .selectCaseItem {
    display: flex;
    width: 400px;
    height: 140px;
    margin-top: 20px;
    margin-right: 20px;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid rgba(153, 153, 153, 0.4);
    cursor: pointer;
    overflow: hidden;
    &:nth-of-type(2n) {
      margin-right: 0;
    }
    .caseItem_top_left {
      width: 126px;
      height: 140px;
      border-radius: 10px 10px 10px 10px;
      ::v-deep {
        img {
          width: 100%;
          height: 100%;
          object-fit: scale-down;
        }
      }
    }
    .caseItem_top_right {
      flex: 1;
      padding-top: 17px;
      overflow: hidden;
      .caseName {
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #000000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .patientInfo {
        margin-top: 4px;
        & > span:first-of-type {
          font-size: 16px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #666666;
        }
      }
      .score {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 247px;
        height: 61px;
        padding: 0 39px;
        margin-top: 4px;
        background: #f0f0f0;
        border-radius: 8px 8px 8px 8px;
        & > span:first-of-type {
          font-size: 18px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #333333;
        }
        & > span:last-of-type {
          font-size: 18px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          color: #1890ff;
        }
      }
    }
  }
  // 选中的数据
  .isChecked {
    position: relative;
    border: 1px solid #1890ff;
    .checkedIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 0px;
      top: 0;
      width: 25px;
      height: 25px;
      background: #1890ff;
      border-radius: 0px 8px 0px 10px;
      .el-icon-check {
        font-size: 15px;
        font-weight: bold;
        color: #fff;
        text-align: center;
      }
    }
  }
}
</style>
