import request from '@/utils/request'

//学校分页列表
export function schoolList(data) {
  return request({
    url: '/system/school/schoolList',
    method: 'get',
    params: data
  })
}
//添加学校
export function schoolSave(data) {
  return request({
    url: '/system/school/schoolSave',
    method: 'post',
    data
  })
}
//修改学校信息
export function schoolUpdate(data) {
  return request({
    url: '/system/school/schoolUpdate',
    method: 'post',
    data
  })
}
//删除学校
export function schoolRemove(data) {
  return request({
    url: '/system/school/schoolRemove',
    method: 'delete',
    params: data
  })
}
