.div-top {
  color: rgb(32, 29, 29);
  border-bottom: 1px solid rgba(32, 30, 30, 0.432);
  height: 70px;
  font-size: 20px;
  padding-top: 30px;
  padding-left: 50px;
  margin-bottom: 20px;
}

.avataruploads {
  height: 150px;
  width: 150px;
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  line-height: 150px;
  text-align: center;
  font-size: 22px;
  position: relative;
}

.avataruploads:hover {
  border: 1px solid #5cb6ff;
  color: #5cb6ff;
}

.avatarupcover {
  position: absolute;
  left: 0;
  top: 0;
  height: 150px;
  width: 150px;
  z-index: 1;
  cursor: pointer;
  line-height: 150px;
  text-align: center;
  font-size: 22px;
}

.avatarupcover:hover {
  color: #5cb6ff;
}

.avataruploads:hover .avatarupcover-change {
  display: block;
}

.avataruploads img {
  height: 150px;
  width: 150px;
}

.imagePreview:hover {
  cursor: pointer;
}

.eltreeBox {
  width: 100%;
  height: auto;
  max-height: 400px;
  overflow-y: auto;
}

.search_label {
  margin: 0 0 0 20px;
  display: inline-block;
}

.user_lable {
  width: 100%;
  height: 40px;
  text-align: center;
  background: rgba(49, 188, 122, 0.9);
  color: #ffffff;
  line-height: 40px;
}

.user_right {
  width: calc(100% - 30px);
  height: calc(100vh - 125px);
  min-height: 400px;
  border: 1px solid #cccccc;
  margin: 15px;
  overflow-y: auto;
}

.user_alert {
  width: 100%;
  height: 40px;
  text-align: center;
  background: #ff4d51;
  color: #ffffff;
  line-height: 40px;
}

.topic_question {
  min-height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  box-sizing: border-box;
  padding: 0 120px 0 0;
  color: #000000;
}

.topic_selection {
  width: 100%;
  height: auto;
  line-height: 30px;
  font-size: 14px;
}

.topic_selectionit {
  box-sizing: border-box;
  padding: 0 0 0 30px;
  position: relative;
}

.topic_question p,
.topic_question div,
.topic_answer p,
.topic_answer div,
.topic_selectionit p,
.topic_selectionit div {
  display: inline-block !important;
  margin: 0;
}

.topic_selectionitall {
  padding: 0 0 0 0;
}

.topic_answermr {
  margin: 15px 0 0 0;
}

.topic_answer {
  height: auto;
  line-height: 26px;
  font-size: 14px;
  padding: 0 0 0 10px;
}
.small_input .ql-editor {
  height: auto;
  min-height: 100px;
  max-height: 200px;
}

.small_input .el-form-item {
  margin: 0 0 20px 0;
  position: relative;
}

.small_input .el-tabs__item {
  height: 36px !important;
  line-height: 36px !important;
}

.mainColor {
  color: #1482f0;
}

.right_name {
  height: 40px;
  float: right;
  line-height: 40px;
  margin: 0 15px 0 0;
}

.menuinfo {
  box-sizing: border-box;
  padding: 20px;
  width: 100%;
  float: left;
}

.menulist_cont {
  width: 400px;
  float: right;
}

.menuinfo_top {
  box-sizing: border-box;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #e4e4e4;
  font-weight: bold;
  span {
    float: right;
  }
}

.menuinfo_botttom {
  box-sizing: border-box;
  padding: 20px 20px 0 20px;
  width: 100%;
  float: left;
}

.menulist_conttop {
  height: 79px;
  box-sizing: border-box;
  padding: 20px 20px 0 0;
  text-align: right;
  border-bottom: 1px solid #e4e4e4;
}

.simulation_cont {
  box-sizing: border-box;
  padding: 15px;
}

.simulation_all {
  font-size: 19px;
  font-weight: bold;
}

.simulation_info {
  margin: 15px 0;
}

.top_info {
  line-height: 45px;
  padding: 20px;
  box-sizing: border-box;
  border-bottom: 1px solid #cccccc;
}

.info_label {
  margin: 0 0 0 35px;
  display: inline-block;
}
.tip_btn {
  height: 36px;
  width: 36px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  line-height: 36px;
  font-size: 36px;
  cursor: pointer;
  display: inline-block;
  color: #e6a23c;
  overflow: hidden;
}

.editor_box > p {
  margin: 0;
  //  display: inline-block;
}

.editor_box img {
  max-width: 100%;
}

.list_question span p,
.list_question span div {
  display: inline-block !important;
  margin: 0;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.correct_complain .el-dialog__body {
  padding: 10px 20px;
}

.universal-layout {
  width: 100%;
  height: 100%;
  padding: 0 50px 20px;
  background: linear-gradient(180deg, #d0dae4 0%, #f4f4f4 100%);
  .universal-layout__header {
    position: relative;
    padding-top: 34px;
    text-align: center;
    .header-back {
      position: absolute;
      left: 50px;
      top: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .header-title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      line-height: 35px;
    }
  }
  .universal-layout__body {
    position: relative;
    width: 100%;
    margin-top: 30px;
    height: 800px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px 30px 30px 30px;
    overflow: auto;
    &::-webkit-scrollbar {
      background: transparent;
    }

    .search-field {
      display: flex;
      align-items: center;
      justify-content: center;
      &--select {
        .el-input {
          height: 100%;
        }
      }
      &__label {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #666666;
      }
      &__input {
        width: 300px;
        height: 45px;
        .el-input__inner {
          width: 100%;
          height: 100%;
          border-radius: 74px;
          background: #eef0f2;
          border: none;
          color: #333333;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          &::placeholder {
            color: #cccccc;
          }
        }
        .el-icon-circle-close {
          margin-right: 5px;
          margin-top: 2px;
          font-size: 20px;
        }
      }

      &--action {
        display: flex;
        align-items: center;
        margin-left: 20px;
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 45px;
          padding: 0 18px;
          margin-right: 20px;
          background: #65849a;
          border-radius: 63px 63px 63px 63px;
          border: none;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          &:first-of-type {
            margin-right: 10px;
          }
          &--primary {
            background: #65849a;
            border-color: #65849a;
            color: #fff;
          }
          &.is-plain {
            background: #fff;
            border: 1px solid #65849a;
            color: #65849a;
          }
          &--info {
            background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
            border-color: transparent;
            color: #fff;
          }
        }
      }
    }
  }
  .universal-layout__table {
    .el-table {
      &::before {
        display: none;
      }
      .tableCellClassName {
        height: 55px;
        padding-top: 0;
        padding-bottom: 0;
        background: #f6f8fa;
        border-bottom: 4px solid #fff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        &:first-of-type {
          border-radius: 8px 0 0 8px;
        }
        &:last-of-type {
          border-radius: 0 8px 8px 0;
        }
        & > .cell {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-checkbox {
          &__input.is-checked {
            .el-checkbox__inner {
              background-color: #274e6a;
              border-color: #274e6a;
            }
          }
          &__inner {
            width: 28px;
            height: 28px;
            border-radius: 4px 4px 4px 4px;
            &::after {
              width: 8px;
              height: 14px;
              left: 8px;
              top: 2px;
              border-width: 2px;
            }
          }
        }
      }
      .tableHeaderClassName {
        height: 55px;
        border-bottom: 4px solid #fff;
        background: #f6f8fa;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #999999;
        &:first-of-type {
          border-radius: 8px 0 0 8px;
        }
        &:last-of-type {
          border-radius: 0 8px 8px 0;
        }

        .el-checkbox {
          &__input.is-checked,
          &__input.is-indeterminate {
            .el-checkbox__inner {
              background-color: #274e6a;
              border-color: #274e6a;
            }
          }
          &__inner {
            width: 28px;
            height: 28px;
            border-radius: 4px 4px 4px 4px;
            &::after {
              width: 8px;
              height: 14px;
              left: 8px;
              top: 2px;
              border-width: 2px;
            }
            &::before {
              width: 14px;
              top: 12px;
              left: 50%;
              transform: translateX(-50%) scale(1);
            }
          }
        }
      }

      .button-group {
        display: flex;
        align-items: center;
        justify-content: space-around;
        .button-group__text {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #666666;
        }
      }
      .el-tag {
        position: relative;
        left: -5px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 66px;
        height: 38px;
        padding: 0;
        border-radius: 8px 8px 8px 8px;

        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        &.el-tag--success {
          border-color: #c2dfc6;
          background: #e2efe7;
          color: #33a141;
        }
        &.el-tag--danger {
          border-color: #ffd3d3;
          background: #f3e7e9;
          color: #dd4b4b;
        }
      }
      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;

        // min-width: 78px;
        // height: 38px;
        padding: 10px;
        margin: 0;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e2e2e2;

        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        i {
          font-size: 16px;
          margin-right: 4px;
        }
        span {
          margin-left: 0;
        }

        &--primary {
          color: #3381b9;
        }
      }
    }
  }
  .universal-layout__pagination {
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #65849a;
      color: #eef0f2;
    }
    .el-pagination.is-background .el-pager li {
      min-width: 40px;
      height: 40px;
      line-height: 40px;
      border-radius: 10px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      background-color: #eef0f2;
      color: #65849a;
    }
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .btn-next {
      min-width: 40px;
      height: 40px;
      line-height: 40px;
      border-radius: 10px;
      background-color: #eef0f2;
      color: #65849a;
      font-size: 16px;
    }
    .el-pagination.is-background .btn-prev:disabled {
      color: rgba($color: #65849a, $alpha: 0.3);
    }
    .el-pagination__total {
      height: 40px;
      line-height: 40px;
      color: #fff;
      font-size: 15px;
    }
  }
}
