<template>
  <div>
    <div class="topic_des">
      <span :class="difficultyClass">{{ difficulty }}</span>
    </div>
    <div class="topic_question editor_box" v-html="form.question"></div>
    <div class="topic_selection" v-for="(item, formindex) in form.list" v-if="formindex == 0">
      <div class="topic_selectionit" :class="{ correctAnswer: correctAnswer(item.answer).includes(index + 1) }" v-for="(optionItem, index) in item.optionsArr" :key="index">
        <span class="work-selectionlabel">{{ letters[index] }}、</span>
        <span class="editor_box" v-html="optionItem"></span>
      </div>
      <div class="topic_answermr topic_answer">正确答案：<span class="editor_box" v-html="item.answer"></span></div>
      <div class="topic_answermr topic_answer">解析：<span class="editor_box" v-html="item.analysis"></span></div>
    </div>
  </div>
</template>

<script>
import { letterToNumber } from '@/filters'
export default {
  props: {
    form: {
      type: Object,
      required: true
    },
    letters: {
      type: Array,
      required: true
    },
    difficultys: {
      type: Array,
      required: true
    }
  },
  data() {
    return {}
  },
  created() {
    this.jsonObject()
  },
  computed: {
    difficulty() {
      return this.difficultys.find((item) => item.value == this.form.difficulty).name
    },
    difficultyClass() {
      const value = this.difficultys.find((item) => item.value == this.form.difficulty).value
      return value === 'SIMPLE' ? 'difficulty--simple' : value === 'MEDIUM' ? 'difficulty--medium' : 'difficulty--difficulty'
    }
  },
  methods: {
    correctAnswer(letter) {
      console.log(letter)

      return letterToNumber(letter, true)
    },
    jsonObject() {
      var form = this.form
      if (form.list && form.list.length > 0) {
        form.list.map((item) => {
          if (item.options) {
            var optionObject = JSON.parse(item.options)
            item.optionObject = optionObject
            item.optionsArr = Object.values(optionObject)
          } else {
            item.optionObject = {}
            item.optionsArr = []
          }
        })
      } else {
        form.list = []
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.topic_des {
  span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 26px;
    background: #e6f7eb;
    border-radius: 4px 4px 4px 4px;

    font-family: PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #0b9d3a;
  }
  .difficulty--simple {
    background: #e6f7eb;
    color: #0b9d3a;
  }
  .difficulty--medium {
    background: #fff4e1;
    color: #ff7411;
  }
  .difficulty--difficulty {
    background: #ffe9e9;
    color: #ff1104;
  }
}
.topic_question {
  margin-top: 20px;
  margin-bottom: 20px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #333333;
}
.topic_selectionit {
  display: flex;
  align-items: center;
  height: 44px;
  margin-bottom: 20px;
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;

  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}
.topic_selectionit.correctAnswer {
  background: #ecf9f0;
  color: #1aa94a;
}
.topic_answermr.topic_answer {
  display: flex;
  align-items: center;
  margin-top: 33px;
  .topic_lable {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 84px;
    height: 32px;
    margin-right: 12px;
    background: #eaeaea;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
  }
  .editor_box {
    flex: 1;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #666666;
  }
}
.topic_answermr.topic_analysis {
  align-items: flex-start;
  .editor_box {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }
}
</style>
