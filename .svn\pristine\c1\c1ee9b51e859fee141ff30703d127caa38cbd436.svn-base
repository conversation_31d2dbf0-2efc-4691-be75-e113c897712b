import requestAI from '@/utils/requestAi'
import request from '@/utils/request'
/** 获取应用绑定的会话角色和模型 */
export function chatRoleModel(appId, headers) {
  return requestAI({
    url: '/api/chat/chatRoleModel',
    method: 'GET',
    params: {
      appId
    },
    headers
  })
}
/** 获取应用绑定的会话角色和模型 */
export function chatRoleInfo(chatRoleId, headers) {
  return requestAI({
    url: '/api/chat/chatRoleInfo',
    method: 'GET',
    params: {
      chatRoleId
    },
    headers
  })
}

/** 获取应用配置 */
export function appConf(appId, headers) {
  return requestAI({
    url: '/api/chat/appConf',
    method: 'GET',
    params: {
      appId
    },
    headers
  })
}

/** 流式对话 */
export function generateStream(data, headers) {
  return requestAI({
    url: '/api/chat/generateStream',
    method: 'post',
    headers,
    data
  })
}

/** 音色 */
export function chatTts(headers) {
  return requestAI({
    url: '/api/chat/tts',
    method: 'get',
    headers
  })
}

/** 调用大模型
 * @param {string} type 类型 1 生成病例 2 生成对话
 * @param {string} content 输入的问题
 * @param {number} isStream 是否使用流输出 1 是 2 否
 * @param {string} model 模型
 * @param {string} chatModel 模式:OLLAMA-本地;DASH_SCOPE-阿里云百炼
 */
export function aiCall(data) {
  return request({
    url: '/ai/call',
    method: 'post',
    data
  })
}
