<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="addDialog" width="960px" top="60px" @close="close" custom-class="examDialog" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="120px" :rules="rules" inline>
        <el-form-item label="考试名称:" prop="name">
          <el-input v-model="form.name" maxlength="50" placeholder="请输入考试名称"></el-input>
        </el-form-item>
        <el-form-item label="考试开始时间:" prop="startTime">
          <el-date-picker v-model="startTime" type="datetime" placeholder="选择考试开始时间" format="yyyy-MM-dd HH:mm" :picker-options="startTimePickerOptions" @change="startTimePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="考试结束时间:" prop="endTime">
          <el-date-picker v-model="endTime" type="datetime" placeholder="选择考试结束时间" format="yyyy-MM-dd HH:mm" :picker-options="endTimePickerOptions" @change="endTimePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="考试限时:" prop="time"> <el-input-number v-model="form.time" :min="1" :max="timeLimit" label="考试限时"></el-input-number> 分钟 </el-form-item>
        <el-form-item label="及格分:" prop="passScore"> <el-input-number v-model="form.passScore" :min="1" :max="100" label="及格分"></el-input-number> </el-form-item>
        <el-form-item label="考核病例:" prop="caseIds" class="selectContent">
          <el-button type="primary" size="small" style="margin-bottom: 15px" @click="selectCase">选择病例</el-button>
          <div class="caseBox">
            <div v-for="item in selectList" :key="item.caseId" class="caseItem">
              <div class="caseItem_top">
                <div class="caseItem_top_left">
                  <div class="score">
                    总分: <span>{{ item.allScore }}</span> 分
                  </div>
                  <casePhoto :height="'186px'" :width="'136px'" :sex="item.sex" :age="item.age" :type="'case'" />
                </div>
                <div class="caseItem_top_right">
                  <div>
                    <span>
                      {{ item.name }}
                      <svg-icon :icon-class="item.sex === 'M' ? 'nan' : 'nv'"></svg-icon>
                    </span>
                    <span> {{ item.age }} 岁</span>
                    <span>{{ item.form | caseForm }}</span>
                  </div>
                  <div>
                    <span> {{ item.mainDemands }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="班级学生:" prop="studentIds" class="selectContent">
          <el-button type="primary" size="small" style="margin-bottom: 15px" @click="selectStudent">选择班级学生</el-button>
          <el-card v-if="checkedStudentList.length" class="checkedStudents">
            <div slot="header">选择的班级学生</div>
            <div v-for="item in checkedStudentList" :key="item.id">
              <div class="checkedClass">{{ item.className }}</div>
              <ul class="students">
                <li v-for="li in item.children" :key="li.id">
                  <el-tag type="success" closable @close="tagClose(li, item)">{{ li.name }}</el-tag>
                </li>
              </ul>
            </div>
          </el-card>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 选择病例 -->
    <SelectCase ref="SelectCase" :selectCaseDialog.sync="selectCaseDialog" :selectList="selectList" @success="selectCaseOver" />
    <!-- 选择班级学生 -->
    <SelectStudent ref="SelectStudent" :selectStudentDialog.sync="selectStudentDialog" @success="selectStudentOver" />
  </div>
</template>
<script>
import { caseExamAdd, caseExamUpdate, caseExamTimeJudge } from '@/api/caseExam'
import casePhoto from '@/components/casePhoto'
import { formatDate } from '@/filters'
import SelectCase from '@/views/caseExam/components/SelectCase'
import SelectStudent from '@/views/caseExam/components/SelectStudent'
export default {
  name: 'AddExamDialog',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    casePhoto,
    SelectCase,
    SelectStudent
  },

  data() {
    return {
      form: {
        name: null,
        clbumIds: null,
        startTime: null,
        endTime: null,
        time: null, // 考试限时
        state: 1,
        passScore: null,
        caseIds: [],
        studentIds: []
      },
      startTime: null,
      endTime: null,
      startTimePickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      endTimePickerOptions: {
        disabledDate: (time) => {
          const date = Date.parse(formatDate(time, 'yyyy-MM-dd hh:mm:ss'))
          const startTime = Date.parse(this.form.startTime)
          return date < startTime - 8.64e7 || time.getTime() < Date.now() - 8.64e7
        }
      },
      selectCaseDialog: false,
      selectStudentDialog: false,
      rules: {
        name: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
        startTime: [{ required: true, message: '请选择考试开始时间', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择考试结束时间', trigger: 'change' }],
        time: [{ required: true, message: '请输入考试限时', trigger: 'blur' }],
        publishTime: [{ required: true, message: '请选择成绩公布时间', trigger: 'change' }],
        passScore: [{ required: true, message: '请输入及格分数', trigger: 'blur' }],
        caseIds: [{ type: 'array', required: true, message: '病例不能为空', trigger: 'change' }],
        studentIds: [{ type: 'array', required: true, message: '学生不能为空', trigger: 'change' }]
      },
      selectList: [],
      checkedStudentList: [],
      isCopy: false
    }
  },
  computed: {
    dialogTitle() {
      let label = null
      if (this.form.examId) {
        label = '编辑考核'
      } else {
        label = this.isCopy ? '复制考核' : '添加考核'
      }
      return label
    },
    timeLimit() {
      if (this.form.startTime && this.form.endTime) {
        // 使用示例
        const startTime = new Date(this.form.startTime).getTime()
        const endTime = new Date(this.form.endTime).getTime()
        const totalTime = endTime - startTime
        return Math.floor(totalTime / 60000)
      } else {
        return 10000
      }
    }
  },
  created() {},
  methods: {
    startTimePickerChange(val) {
      if (val) {
        this.form.startTime = formatDate(val, 'yyyy-MM-dd hh:mm:ss')
      } else {
        this.form.startTime = null
      }
    },
    endTimePickerChange(val) {
      if (val) {
        const startTime = Date.parse(this.form.startTime)
        const endTime = Date.parse(val)
        if (startTime >= endTime) {
          this.endTime = null
          return this.$message.warning('考试结束时间不能小于考试开始时间')
        } else {
          this.form.endTime = formatDate(val, 'yyyy-MM-dd hh:mm:ss')
        }
      } else {
        this.form.endTime = null
      }
    },
    /** 选择病例*/
    selectCase() {
      this.$refs['SelectCase'].getCaseList()
      this.selectCaseDialog = true
    },
    selectCaseOver(list) {
      this.selectList = list
      this.form.caseIds = this.selectList.map((item) => item.caseId)
      this.$refs['form'].validateField('caseIds')
    },
    /** 选择班级学生 */
    selectStudent() {
      this.$refs['SelectStudent'].getClassAll()
      this.selectStudentDialog = true
    },
    selectStudentOver({ checkedList, checkIds }) {
      this.checkedStudentList = [...checkedList]
      this.form.studentIds = [...checkIds]
      this.$nextTick(() => {
        this.$refs['form'].validateField('studentIds')
      })
    },
    tagClose(li, data) {
      this.$refs['SelectStudent'].tagClose(li, data)
    },

    showData(data, type) {
      this.isCopy = type === 'copy' ? true : false
      this.form = {
        examId: type === 'copy' ? null : data.examId,
        name: data.name,
        clbumIds: data.clbumIds,
        startTime: data.startTime,
        endTime: data.endTime,
        time: data.time, // 考试限时
        state: data.state,
        passScore: data.passScore,
        caseIds: data.cases.map((item) => item.case_id),
        studentIds: data.students.map((item) => item.student_id)
      }
      this.startTime = this.form.startTime
      this.endTime = this.form.endTime
      // 回显病例
      data.cases.forEach((item) => {
        this.selectList.push({
          caseId: item.case_id,
          allScore: item.all_score,
          sex: item.sex,
          name: item.name,
          age: item.age,
          form: item.form,
          mainDemands: item.main_demands
        })
      })

      this.$refs['SelectStudent'].showData(data)
    },
    // 关闭
    close() {
      this.$emit('update:addDialog', false)
      this.form = {
        name: null,
        clbumIds: null,
        startTime: null,
        endTime: null,
        time: null, // 考试限时
        state: 1,
        passScore: null,
        caseIds: [],
        studentIds: []
      }
      this.startTime = null
      this.endTime = null
      this.selectList = []
      this.checkedStudentList = []
      this.$refs['SelectStudent'].checkStudents = []
      this.$refs['SelectStudent'].checkIds = []
      this.$refs['form'].resetFields()
    },

    confirm() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          // 判断当前时间下是否有考试
          const timeInfo = {
            startTime: this.form.startTime,
            endTime: this.form.endTime,
            examId: this.form.examId ? this.form.examId : null,
            type: this.form.examId ? 2 : 1
          }
          const startTime = new Date(timeInfo.startTime).getTime()
          const endTime = new Date(timeInfo.endTime).getTime()
          if (startTime > endTime) {
            return this.$message.warning('考试开始时间不能大于结束时间')
          }
          await caseExamTimeJudge(timeInfo)
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.form.clbumIds = this.checkedStudentList.map((item) => item.classId).join(',')
          if (this.form.examId) {
            caseExamUpdate(this.form)
              .then(() => {
                loading.close()
                this.$message.success('修改考核成功！')
                this.$emit('success')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          } else {
            caseExamAdd(this.form)
              .then(() => {
                loading.close()
                this.$message.success(`${this.isCopy ? '复制' : '添加'}考核成功！`)
                this.$emit('success')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .selectCase {
    .el-dialog__body {
      padding-bottom: 0;
    }
  }
}
::v-deep {
  .examDialog {
    .el-dialog__body {
      height: 700px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 3px;
      }

      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #d2d2d2;
      }

      // 外面的背景
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
    }
    .el-input-number,
    .el-date-editor,
    .el-input__inner {
      width: 300px;
    }
    .selectContent {
      width: 100%;
      .el-form-item__content {
        width: calc(100% - 120px);
      }
    }
    .caseBox {
      justify-content: flex-start;
      .caseItem {
        width: 385px;
        margin-right: 15px;
        &:nth-of-type(2n) {
          margin-right: 0;
        }
      }
    }
    .el-card__body {
      padding-top: 0;
    }
    .checkedStudents {
      .checkedClass {
        padding-left: 10px;
        margin-top: 15px;
        font-size: 18px;
        font-weight: bold;
      }
      .students {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        padding-left: 10px;
        margin: 0;
        margin-top: 10px;
        list-style: none;
        li {
          margin-right: 10px;
        }
      }
    }
  }
}
::v-deep {
  .caseBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .caseItem {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 450px;
      margin-bottom: 23px;
      height: 188px;
      line-height: initial;
      border-radius: 8px;
      border: 1px solid #eee;
      background: #fff;
      cursor: pointer;
      // overflow: hidden;
      box-sizing: border-box;
      .el-icon-success {
        position: absolute;
        right: -10px;
        top: -10px;
        font-size: 35px;
        color: #409eff;
      }
      .caseItem_top {
        position: relative;
        display: flex;
        justify-content: space-between;
        .caseItem_top_left {
          width: 136px;
          .score {
            position: absolute;
            padding: 4px 9px;
            height: 24px;
            background: rgba($color: #ff9f1e, $alpha: 0.6);
            text-align: center;
            border-radius: 8px 0 8px 0;
            font-size: 12px;
            font-family:
              Microsoft YaHei-Bold,
              Microsoft YaHei;
            color: #ffffff;
            span {
              font-weight: bold;
            }
          }
        }
        .caseItem_top_right {
          flex: 1;
          position: relative;
          padding-top: 12px;
          padding-right: 8px;
          padding-left: 13px;
          & > div:first-of-type {
            & > span {
              font-size: 16px;
              font-weight: bold;
              color: #1a1a1a;
              &:nth-of-type(2) {
                margin: 0 18px;
              }
            }
          }
          & > div:nth-of-type(2) {
            margin-top: 14px;
            padding: 8px 12px;
            min-height: 110px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 400;
            color: #737373;
            line-height: 24px;
            background: rgba($color: #f1f1f1, $alpha: 0.5);
            span {
              display: inline-block;
              width: 100%;
              // 两行显示，超出隐藏
              display: -webkit-box;
              overflow: hidden;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: (3);
            }
          }
        }
      }
    }
  }
  // 选中的数据
  .isChecked {
    box-shadow: 0 0 5px 2px #409eff;
  }
}
</style>
