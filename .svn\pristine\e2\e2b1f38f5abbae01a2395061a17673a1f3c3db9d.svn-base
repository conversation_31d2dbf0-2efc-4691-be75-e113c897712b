<template>
  <div class="app-container">
    <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
    <div class="title">病史采集</div>
    <el-descriptions v-if="caseInfo" :column="6" border>
      <el-descriptions-item label="病例名称">{{ caseInfo.name }}</el-descriptions-item>
      <el-descriptions-item label="病例类型">
        <el-tag type="success">{{ caseInfo.caseType === 1 ? '学习病例' : '考核病例' }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="病人信息">
        <el-tag style="margin-right: 10px">姓名:{{ caseInfo.realName }}</el-tag>
        <el-tag style="margin-right: 10px">年龄:{{ caseInfo.age }}</el-tag>
        <el-tag>性别:{{ caseInfo.sex === 'F' ? '女' : '男' }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="病例简介">{{ caseInfo.mainDemands ? caseInfo.mainDemands : '暂无' }}</el-descriptions-item>
      <el-descriptions-item label="当前分值">
        <span style="color: #0076f6; font-size: 20px">{{ caseInfo.allScore ? caseInfo.allScore : 0 }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="问题数量">
        <span style="color: #0076f6; font-size: 20px">{{ caseInfo.questionCount ? caseInfo.questionCount : 0 }}</span>
      </el-descriptions-item>
    </el-descriptions>
    <!-- 查询 -->
    <el-form ref="form" :model="queryInfo" label-width="80px" style="margin-top: 15px" inline>
      <el-form-item label="关键字:">
        <el-input v-model="queryInfo.keyword" maxlength="40" size="small" placeholder="请输入关键字" clearable @clear="getDetails" @keydown.native.enter="getDetails"></el-input>
      </el-form-item>
      <el-form-item label="问诊属性:">
        <el-radio-group v-model="queryInfo.level" @change="getDetails">
          <el-radio :label="1">重要</el-radio>
          <el-radio :label="2">常规</el-radio>
          <el-radio :label="3">无效</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="问诊类型:">
        <el-select v-model="queryInfo.typeId" placeholder="请选择问诊类型" @focus="getTypeList" @change="getDetails">
          <el-option v-for="item in typeList" :key="item.typeId" :value="item.typeId" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getDetails">查询</el-button>
        <el-button type="primary" size="small" @click="reset">重置</el-button>
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="addDialog = true" size="small">添加问题</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" @click="removes">批量删除</el-button>
        <el-button type="primary" icon="el-icon-delete" size="small" @click="questionViewsDialog = true">问诊试图</el-button>
      </el-form-item>
    </el-form>
    <!-- 列表 -->
    <el-table :data="list" style="width: 100%" border @selection-change="selectionChange">
      <el-table-column align="center" type="selection"> </el-table-column>
      <el-table-column align="center" label="序号" type="index"> </el-table-column>
      <el-table-column align="center" prop="typeName" label="问诊类型名称" width="width"> </el-table-column>
      <el-table-column align="center" prop="level" label="问题属性" width="width">
        <template v-slot="{ row }">
          <span>{{ row.level | caseLevel }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="problem" label="问题" width="width"> </el-table-column>
      <el-table-column align="center" prop="keyword" label="关键字" width="width"> </el-table-column>
      <el-table-column align="center" prop="answer" label="回答" width="width"> </el-table-column>
      <el-table-column align="center" prop="score" label="分数" width="width"> </el-table-column>
      <el-table-column align="center" label="操作" width="width">
        <template v-slot="{ row }">
          <el-button size="small" type="primary" @click="edit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="width: 100%; margin: 15px; text-align: center">
      <el-pagination background @current-change="getDetails" @size-change="getDetails" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
    </div>
    <!-- 新增问诊问题 -->
    <AddQuestionDialog ref="AddQuestionDialog" :addDialog.sync="addDialog" :questionNum="questionNum" :questionTotalScore="caseInfo.allScore" @success="getDetails" />
    <!-- 问诊试图 -->
    <QuestionViews :show-dialog.sync="questionViewsDialog" />
  </div>
</template>
<script>
import { caseQuestionTypeList } from '@/api/caseQuestionType'
import { caseDetail, caseQuestionList, caseRemoveQuestionBatch } from '@/api/case'
import addQuestionDialog from './addQuestion.vue'
import QuestionViews from '@/views/case/historyCollect/QuestionViews'
export default {
  name: '',
  components: {
    AddQuestionDialog: addQuestionDialog,
    QuestionViews
  },
  data() {
    return {
      queryInfo: {
        typeId: null,
        level: null,
        keyword: null,
        pageNum: 1,
        pageSize: 10
      },
      typeList: [],
      caseInfo: {},
      list: [],
      checkedList: [], // 被勾选的问题
      total: 0,
      addDialog: false,
      questionNum: 0,
      questionViewsDialog: false
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    goBack() {
      this.$router.push('/case')
    },
    async getDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.caseInfo = data
      const { data: caseList } = await caseQuestionList({ caseId: this.$route.params.id, ...this.queryInfo })
      this.list = caseList.list
      this.total = caseList.total
      this.questionNum = caseList.total
    },
    async getTypeList() {
      const { data } = await caseQuestionTypeList()
      this.typeList = data.list
    },
    reset() {
      this.queryInfo = {
        typeId: null,
        level: null,
        keyword: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getDetails()
    },
    selectionChange(val) {
      this.checkedList = val
    },
    edit(row) {
      this.$refs['AddQuestionDialog'].showData(row)
      this.addDialog = true
    },
    del(row) {
      this.$confirm('确定要删除该问诊问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          console.log(row)
          await caseRemoveQuestionBatch({ ids: [row.questionId], caseId: this.$route.params.id })
          this.getDetails()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    removes() {
      if (!this.checkedList.length) return this.$message.warning('未选中问诊问题')
      this.$confirm('确定要删除被选中的问诊问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const ids = this.checkedList.map((item) => item.questionId)
          await caseRemoveQuestionBatch({ ids, caseId: this.$route.params.id })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style scoped lang="scss">
.goBack {
  position: absolute;
  top: 10px;
}
.title {
  font-size: 22px;
  text-align: center;
  margin-bottom: 15px;
}
</style>
