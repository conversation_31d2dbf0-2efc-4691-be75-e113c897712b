<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
        <div class="title">考核管理</div>
      </div>
      <el-form ref="form" :model="queryInfo" label-width="80px" inline>
        <el-form-item label="考试名称:">
          <el-input v-model="queryInfo.name" size="small" maxlength="40" placeholder="请输入考试名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>
        <el-form-item label="考试时间:">
          <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange" clearable> </el-date-picker>
        </el-form-item>
        <el-form-item label="状态:">
          <el-radio-group v-model="queryInfo.state" @change="getList">
            <el-radio :label="1">未开考</el-radio>
            <el-radio :label="2">考试中</el-radio>
            <el-radio :label="3">已结束</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="small" @click="getList">查询</el-button>
          <el-button type="primary" size="small" @click="reset">重置</el-button>
          <el-button type="primary" size="small" icon="el-icon-circle-plus-outline" @click="addDialog = true">添加考核</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column prop="name" label="考试名称" width="width" align="center"> </el-table-column>
        <el-table-column label="考试时间" width="180" align="center">
          <template v-slot="{ row }">
            <div>{{ row.startTime }}</div>
            <div>~</div>
            <div>{{ row.endTime }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="time" label="考试限时" width="width" align="center">
          <template v-slot="{ row }">
            <div>{{ row.time }}分钟</div>
          </template>
        </el-table-column>
        <el-table-column prop="cou" label="考试人数" width="width" align="center">
          <template v-slot="{ row }">
            <div>
              <span style="color: #409eff">{{ row.alreadyNumber }}</span> / <span>{{ row.allNumber }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="caseName" label="考试病例" width="width" align="center"> </el-table-column>
        <el-table-column prop="allScore" label="总分" width="80" align="center"> </el-table-column>
        <el-table-column prop="passScore" label="及格分" width="80" align="center"> </el-table-column>
        <el-table-column prop="state" label="状态" width="100" align="center">
          <template v-slot="{ row }">
            <el-tag :type="rowStateStyle(row)">{{ row.state | examState }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="创建人" width="width" align="center"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center"> </el-table-column>
        <el-table-column prop="name" label="操作" width="170" align="center">
          <template v-slot="{ row }">
            <el-button v-if="row.state === 1" type="warning" size="small" @click="edit(row)">编辑</el-button>
            <el-button v-if="row.state === 1" type="danger" size="small" @click="del(row)">删除</el-button>
            <el-button v-if="row.state === 2 || row.state === 3" type="primary" size="small" @click="details(row)">详情</el-button>
            <el-button v-if="row.state === 3" type="info " size="small" @click="openCopyDialog(row)">复制</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </el-card>
    <!-- 新增/编辑考核 -->
    <AddExam ref="AddExam" :addDialog.sync="addDialog" @success="getList" />
    <!-- 考核详情 -->
    <ExamDetails ref="ExamDetails" :detailsDialog.sync="detailsDialog" />
  </div>
</template>
<script>
import { caseExamList, caseExamDetail, caseExamRemove } from '@/api/caseExam'
import { formatDate } from '@/filters'
import AddExam from './add'
import ExamDetails from '@/views/caseExam/components/ExamDetails'
export default {
  name: 'CaseExam',
  components: {
    AddExam,
    ExamDetails
  },
  data() {
    return {
      queryInfo: {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 7
      },
      time: null,
      list: [],
      total: 0,
      addDialog: false,
      detailsDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseExamList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 7
      }
      this.getList()
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? 'success' : ' '
      return type
    },
    async edit(row) {
      const { data } = await caseExamDetail({ id: row.examId })
      this.$refs['AddExam'].showData(data)
      this.addDialog = true
    },
    del(row) {
      this.$confirm('确定要删除该考核吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseExamRemove({ id: row.examId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async details(row) {
      const { data } = await caseExamDetail({ id: row.examId })
      this.$refs['ExamDetails'].getInfo(data)
      this.detailsDialog = true
    },
    async openCopyDialog(row) {
      const { data } = await caseExamDetail({ id: row.examId })
      this.$refs['AddExam'].showData(data, 'copy')
      this.addDialog = true
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  .el-card {
    height: 100%;
    width: 100%;
    .goBack {
      position: absolute;
    }
    .title {
      font-size: 25px;
      text-align: center;
    }
  }
}
</style>
