<template>
  <div class="app-container">
    <div class="page-header">
      <div class="back-btn" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="page-title">病例考核成绩</div>
    </div>

    <div class="content-wrapper">
      <div class="search-form">
        <el-form ref="form" class="search-form" :model="queryInfo" label-width="110px" inline>
          <el-form-item label="考试名称:">
            <el-input v-model="queryInfo.name" size="small" maxlength="40" placeholder="请输入考试名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
          </el-form-item>
          <el-form-item label="考试时间:">
            <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange" clearable> </el-date-picker>
          </el-form-item>
          <el-form-item label="状态:" label-width="80px">
            <el-radio-group v-model="queryInfo.state" @change="getList">
              <el-radio :label="1">未开考</el-radio>
              <el-radio :label="2">考试中</el-radio>
              <el-radio :label="3">已结束</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="btn-group">
            <el-button type="primary" size="small" class="search-btn" @click="getList">查询</el-button>
            <el-button size="small" class="reset-btn" @click="reset">重置</el-button>
            <el-button type="primary" size="small" icon="el-icon-download" class="export-btn" @click="download">Excel导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table-container">
        <el-table :data="list" style="width: 100%" class="exam-table" header-cell-class-name="el_table_header_cell_class" cell-class-name="tableCellClassName">
          <el-table-column prop="name" label="考试名称" width="width" show-overflow-tooltip align="center"> </el-table-column>
          <el-table-column label="考试时间" width="410" align="center">
            <template v-slot="{ row }">
              <div>{{ row.startTime }}~{{ row.endTime }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="time" label="考试限时" width="100" align="center">
            <template v-slot="{ row }">
              <div>{{ row.time }}分钟</div>
            </template>
          </el-table-column>
          <el-table-column prop="caseName" label="考试病例" width="320" show-overflow-tooltip align="center"> </el-table-column>
          <el-table-column label="考试形式" width="100" align="center">
            <template v-slot="{ row }">
              <div>
                <span>{{ row.type | examType }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="cou" label="考试人数" width="100" align="center">
            <template v-slot="{ row }">
              <div>
                <span style="color: #3381b9">{{ row.alreadyNumber }}</span
                >/<span>{{ row.allNumber }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="allScore" label="总分" width="85" align="center"> </el-table-column>
          <el-table-column prop="passScore" label="及格分" width="85" align="center"> </el-table-column>
          <el-table-column prop="passNumber" label="及格人数" width="100" align="center"> </el-table-column>
          <el-table-column prop="state" label="状态" width="100" align="center">
            <template v-slot="{ row }">
              <el-tag :type="rowStateStyle(row)" class="status-tag">{{ row.state | examState }}</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="createUserName" label="创建人" width="width" align="center"> </el-table-column> -->
          <!-- <el-table-column prop="createTime" label="创建时间" width="180" align="center"> </el-table-column> -->
          <el-table-column prop="name" label="操作" width="120" align="center">
            <template v-slot="{ row }">
              <el-button v-if="row.state === 3" class="detail-btn" type="primary" size="small" @click="details(row)">考生成绩</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-wrapper">
          <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total, prev, pager, next" :total="total"> </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { caseExamList, caseExamListExport } from '@/api/caseExam'
import { formatDate } from '@/filters'
export default {
  name: 'CaseExam',
  data() {
    return {
      queryInfo: {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 8
      },
      time: null,
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseExamList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 8
      }
      this.getList()
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? 'success' : ' '
      return type
    },
    async download() {
      const info = {
        name: this.queryInfo.name,
        startTime: this.queryInfo.startTime,
        endTime: this.queryInfo.endTime,
        state: this.queryInfo.state
      }
      const { data } = await caseExamListExport(info)
      const headers = {
        考试名称: 'name',
        考试开始时间: 'startTime',
        考试结束时间: 'endTime',
        考试限时: 'time',
        考试病例: 'caseName',
        考试形式: 'type',
        考试人数: 'alreadyNumber',
        总人数: 'allNumber',
        总分: 'allScore',
        及格分: 'passScore',
        状态: 'state'
        // 创建人: 'realName',
        // 创建时间: 'createTime'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        // var tHeader = ['考试名称', '考试开始时间', '考试结束时间', '考试限时', '成绩公布时间', '考试人数', '考试病例', '总分', '及格分', '状态', '创建人', '创建时间']
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '病例考核成绩表' // 非必填
        })
      })
    },
    // 处理导出数据格式
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (key === '状态') {
            item[headers[key]] = item[headers[key]] === 1 ? '未开考' : item[headers[key]] === 2 ? '考试中' : '已结束'
          } else if (key === '考试形式') {
            item[headers[key]] = item[headers[key]] === 1 ? '随机全考' : item[headers[key]] === 2 ? '随机抽考一个' : '考一个'
          }
          return item[headers[key]]
        })
      })
    },
    details(row) {
      this.$router.push(`examGrade/${row.examId}`)
      console.log(row)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  padding: 0 50px;
  padding-top: 10px;
  background: #e3e8ed;
  overflow: hidden;
  .page-header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 18px;
    margin-bottom: 22px;

    .back-btn {
      position: absolute;
      left: 0;
      top: -10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;

      img {
        width: 38px;
        height: 38px;
      }

      &:hover {
        background: #e7f0ff;
      }
    }

    .page-title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      text-align: center;
    }
  }
  .content-wrapper {
    position: relative;
    width: 100%;
    height: 810px;
    padding: 30px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px 30px 30px 30px;
  }
}
</style>

<style lang="scss" scoped>
.content-wrapper {
  ::v-deep {
    .search-form {
      .el-form {
        display: flex;
        .el-form-item {
          display: flex;
          align-items: center;

          .el-form-item__label {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #666666;
          }
          .el-form-item__content {
            .el-input {
              .el-input__inner {
                width: 300px;
                height: 45px;
                background: #eef0f2;
                border: none;
                border-radius: 74px 74px 74px 74px;
                font-size: 20px;
                color: #333333;
                &::placeholder {
                  color: #cccccc;
                }
              }
              .el-input__suffix {
                .el-input__suffix-inner {
                  .el-icon-circle-close {
                    margin-right: 5px;
                    margin-top: 2px;
                    font-size: 20px;
                  }
                }
              }
            }

            .el-radio-group {
              display: flex;
              .el-radio {
                display: flex;
                align-items: center;
                margin-right: 15px;
                .el-radio__inner {
                  width: 18px;
                  height: 18px;
                  background: #fff;
                  border-color: #b1b1b1;
                }
                .el-radio__label {
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  font-size: 20px;
                  color: #b1b1b1;
                }
              }

              .el-radio__input.is-checked .el-radio__inner {
                &::after {
                  background: #274e6a;
                  width: 12px;
                  height: 12px;
                }
              }
              .el-radio__input.is-checked + .el-radio__label {
                color: #274e6a;
              }
            }
            .el-date-editor {
              width: 420px;
              height: 45px;
              background: #eef0f2;
              border-radius: 74px 74px 74px 74px;
              border: none;
              .el-icon-date {
                font-size: 20px;
                margin-left: 20px;
                margin-top: 5px;
              }
              .el-range-separator {
                margin-top: 12px;
              }
              .el-range-input {
                background: transparent;
                font-size: 20px;
              }
              .el-range__close-icon {
                margin-right: 5px;
                margin-top: 5px;
                font-size: 20px;
              }
            }
          }
          .search-btn {
            padding: 0;
            width: 70px;
            height: 45px;
            background: #65849a;
            border-radius: 63px 63px 63px 63px;
            border: none;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            text-align: center;
          }
          .reset-btn {
            padding: 0;
            width: 70px;
            height: 45px;
            background: #fff;
            border-radius: 63px 63px 63px 63px;
            border: 1px solid #65849a;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #65849a;
            text-align: center;
          }
          .export-btn {
            padding: 0;
            width: 120px;
            height: 45px;
            background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
            border-radius: 63px 63px 63px 63px;
            border: none;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            text-align: center;
          }
        }
      }
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }

    .el-table {
      border: none;
      &::before {
        display: none;
      }
      .el-table__row {
        border-radius: 8px 8px 8px 8px;
        .el-table__cell {
          height: 55px;
          background: #f6f8fa;
          border-top: 2px solid #fff;
          .cell {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            text-align: center;
          }
        }
      }
      .tableCellClassName {
        &:first-of-type {
          border-radius: 8px 0 0 8px;
        }
        &:nth-of-type(11) {
          border-radius: 0 8px 8px 0;
        }
      }
      .el_table_header_cell_class {
        height: 55px;
        background: #f6f8fa;
        text-align: center;
        &:first-of-type {
          border-radius: 8px 0 0 8px;
        }
        &:nth-of-type(11) {
          border-radius: 0 8px 8px 0;
        }
        & > .cell {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #999999;
        }
      }

      .status-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 84px;
        height: 38px;
        background: rgba(101, 132, 154, 0.05);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e2e2e2;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #3381b9;
      }
      .el-tag--info {
        background: #fef6ea;
        border-color: #eadfcf;
        color: #f9a42d;
      }
      .el-tag--success {
        background: #eaf5ec;
        border-color: #c2dfc6;
        color: #33a141;
      }

      .detail-btn {
        padding: 0;
        width: 88px;
        height: 38px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e2e2e2;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #3381b9;
      }
    }

    .pagination-wrapper {
      position: absolute;
      left: 50%;
      bottom: 15px;
      transform: translateX(-50%);
    }
  }
}
</style>
