import request from '@/utils/request'
// 病例库管理

/** 病例列表 */
export function caseList(data) {
  return request({
    url: '/case/list',
    method: 'post',
    data
  })
}
/** 添加病例 */
export function caseAdd(data) {
  return request({
    url: '/case/add',
    method: 'POST',
    data
  })
}
/** 修改病例 */
export function caseUpdate(data) {
  return request({
    url: '/case/update',
    method: 'POST',
    data
  })
}

/** 删除病例 */
export function caseRemove(params) {
  return request({
    url: '/case/remove',
    method: 'DELETE',
    params
  })
}
/** 病例详情 */
export function caseDetail(params) {
  return request({
    url: '/case/detail',
    method: 'get',
    params
  })
}
/** 查询所有病例信息 */
export function caseAllList(params) {
  return request({
    url: '/case/allList',
    method: 'get',
    params
  })
}

/** 修改问诊问题 */
export function caseUpdateQuestion(data) {
  return request({
    url: '/case/updateQuestion',
    method: 'POST',
    data
  })
}

/** 添加问诊问题 */
export function caseAddQuestion(data) {
  return request({
    url: '/case/addQuestion',
    method: 'POST',
    data
  })
}
/** 批量删除问诊问题 */
export function caseRemoveQuestionBatch(data) {
  return request({
    url: '/case/removeQuestionBatch',
    method: 'POST',
    data
  })
}
/** 问诊问题列表 */
export function caseQuestionList(params) {
  return request({
    url: '/case/questionList',
    method: 'get',
    params
  })
}

/** 复制病例 */
export function caseCopy(data) {
  return request({
    url: '/case/copy',
    method: 'POST',
    data
  })
}
/** 添加病例使用次数 */
export function caseAddCount(params) {
  return request({
    url: '/case/addCount',
    method: 'get',
    params
  })
}

/** 根据关键字匹配问诊问题 */
export function questionListByKeyword(params) {
  return request({
    url: '/case/questionListByKeyword',
    method: 'get',
    params
  })
}

/** 问诊问题树 */
export function caseQuestionTree(params) {
  return request({
    url: '/case/questionTree',
    method: 'get',
    params
  })
}

/** 根据病例查询所有问诊问题 */
export function caseAllQuestionByCaseId(params) {
  return request({
    url: '/case/allQuestionByCaseId',
    method: 'get',
    params
  })
}

// ------------病例权限
/** 添加 */
export function casePowerAdd(data) {
  return request({
    url: '/casePower/add',
    method: 'post',
    data
  })
}

/** 根据学生id查询病例信息 */
export function casePower_selectByStudentId(params) {
  return request({
    url: '/casePower/selectByStudentId',
    method: 'get',
    params
  })
}

/** 获取ai模型的配置 */
export function getSecret(data) {
  return request({
    url: '/ai/getSecret',
    method: 'post',
    data
  })
}
/** 批量添加问诊问题 */
export function addQuestionBatch(reqs) {
  return request({
    url: '/case/addQuestionBatch',
    method: 'post',
    data: {
      reqs
    }
  })
}

// 语音生成

/** 判断是否开启语音生成，若开启说明学生端不使用实时语音 */
export function caseJudgeVoiceOpen() {
  return request({
    url: '/case/judgeVoiceOpen',
    method: 'get'
  })
}
/** 应用音色到病例 */
export function caseUpdateRefId(params) {
  return request({
    url: '/case/updateRefId',
    method: 'get',
    params
  })
}

/** 病例批量语音生成 */
export function caseBatchCreateVoice(params) {
  return request({
    url: '/case/batchCreateVoice',
    method: 'get',
    params
  })
}

/** 单个语音生成 */
export function caseCreateVoice(params) {
  return request({
    url: '/case/createVoice',
    method: 'get',
    params
  })
}
