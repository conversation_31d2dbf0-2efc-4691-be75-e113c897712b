<template>
  <div class="app-container">
    <el-card>
      <template slot="header" v-if="caseInfo">
        <div class="header_left">
          <casePhoto :height="'197px'" :width="'151px'" :sex="caseInfo.sex" :age="caseInfo.age" :type="'statistics'" />
        </div>
        <div class="header_center">
          <div class="name">病例名称: {{ caseInfo.name }}</div>
          <div>
            <span
              >姓名: <span class="data">{{ caseInfo.realName }}</span></span
            >
            <i>|</i>
            <span
              >性别: <span class="data">{{ caseInfo.sex === 'F' ? '女' : '男' }}</span></span
            >
            <i>|</i>
            <span
              >年龄: <span class="data">{{ caseInfo.age }}岁</span></span
            >
            <i>|</i>
            <span
              >是否已婚: <span class="data">{{ caseInfo.isMarry ? '是' : '否' }}</span></span
            >
          </div>
          <div style="padding-left: 10px">
            <div style="margin-bottom: 22px">
              <span>病例简介:</span> <span class="data">{{ caseInfo.mainDemands }}</span>
            </div>
            <div>
              <span>备注:</span><span class="data">{{ caseInfo.remark }}</span>
            </div>
          </div>
        </div>
        <div class="header_right">
          <div>
            <span>总分</span>
            <span>{{ caseInfo.allScore }}</span>
          </div>
          <div>
            <span>问题数量</span>
            <span>{{ caseInfo.questionCount ? caseInfo.questionCount : 0 }}</span>
          </div>
          <div>
            <span>操作总次数</span>
            <span>{{ caseInfo.count ? caseInfo.count : 0 }}</span>
          </div>
          <div>
            <span>操作总用时</span>
            <span>{{ parseInt(caseInfo.allTime / 60) }}分钟</span>
          </div>
          <div>
            <span>平均正确率</span>
            <span>{{ parseInt(caseInfo.avgRate) }}%</span>
          </div>
        </div>
        <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
      </template>
      <div class="content">
        <el-row>
          <el-form ref="form" :model="queryInfo" label-width="80px" inline>
            <el-form-item label="学号:" label-width="115px">
              <el-input v-model="queryInfo.loginName" size="small" placeholder="学号" @keydown.native.enter="getStudentList" clearable @clear="getStudentList"></el-input>
            </el-form-item>
            <el-form-item label="所在班级:">
              <el-select v-model="queryInfo.clbumId" placeholder="请选择所在班级" size="small" @change="getStudentList">
                <el-option v-for="item in classList" :key="item.id" :label="item.clbumName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间:">
              <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" size="small" @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="success " @click="getStudentList">查询</el-button>
              <el-button size="small" type="primary" plain @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-table :data="list" style="width: 100%" stripe :header-cell-style="{ background: '#ECF2F9', color: '#1a1a1a' }">
          <el-table-column prop="studentName" label="学生姓名" width="width" align="center"> </el-table-column>
          <el-table-column prop="prop" label="性别" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.sex === 'F' ? '女' : '男' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="loginName" label="学号" width="width" align="center"> </el-table-column>
          <el-table-column prop="clbumName" label="所在班级" width="width" align="center"> </el-table-column>
          <el-table-column prop="allScore" label="得分" width="width" align="center"> </el-table-column>
          <el-table-column prop="times" label="用时(分钟)" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ parseInt(row.times / 60) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="训练时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="prop" label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="details(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination style="text-align: center; margin-top: 15px" background :current-page.sync="queryInfo.pageNum" :page-sizes="[10, 15, 20, 30]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="getStudentList" @current-change="getStudentList"> </el-pagination>
      </div>
    </el-card>
  </div>
</template>
<script>
import { caseDetail } from '@/api/case'
import { casePractiseStudentList } from '@/api/casePractise'
import { clbumTree } from '@/api/clbum'
import { selectTeacherById } from '@/api/teacher'
import { formatDate } from '@/filters'
import casePhoto from '@/components/casePhoto'

export default {
  name: '',
  components: {
    casePhoto
  },
  data() {
    return {
      queryInfo: {
        clbumId: null,
        loginName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      time: null,
      classList: [],
      caseInfo: null,
      list: [],
      total: 0
    }
  },
  created() {
    this.getCaseDetails()
    this.getStudentList()
    this.getClassList()
  },
  methods: {
    goBack() {
      this.$router.push('/casePractise')
    },

    async getCaseDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.caseInfo = data
    },
    async getStudentList() {
      const { data } = await casePractiseStudentList({ ...this.queryInfo, caseId: this.$route.params.id })
      this.list = data.list
      this.total = data.total
    },
    getClassList() {
      selectTeacherById().then(async (res) => {
        const { data } = await clbumTree({ schoolId: res.data.schoolId })
        this.classList = data
      })
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getStudentList()
    },
    reset() {
      this.queryInfo = {
        clbumId: null,
        loginName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      }
      this.getStudentList()
    },
    details(item) {
      this.$router.push(`/casePractise/details/record/${item.practiseId}/${this.caseInfo.caseId}`)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  padding-top: 10px;
  padding-bottom: 0;
  .goBack {
    position: absolute;
    right: 20px;
  }
}
::v-deep {
  position: relative;
  .el-card__header {
    display: flex;
    .header_left {
      width: 151px;
      height: 197px;
      margin-right: 24px;
      background: #eee;
      border-radius: 4px;
    }
    .header_center {
      .name {
        font-size: 16px;
        font-family:
          Microsoft YaHei-Bold,
          Microsoft YaHei;
        font-weight: bold;
        color: #1a1a1a;
      }
      & > div:nth-of-type(2) {
        margin: 14px 0;
        font-size: 15px;
        i {
          font-style: normal;
          color: #dcdcdc;
          margin: 0 16px;
        }
        span {
          font-family:
            Microsoft YaHei-Regular,
            Microsoft YaHei;
          font-weight: 400;
          color: #737373;
          .data {
            color: #1a1a1a;
          }
        }
      }
      & > div:last-of-type {
        display: flex;
        flex-direction: column;
        min-width: 900px;
        max-width: 1042px;
        min-height: 146px;
        padding: 15px 0;
        padding-right: 5px;
        background: #f8f8f8;
        div {
          display: flex;
          font-size: 14px;
          font-family:
            Microsoft YaHei-Regular,
            Microsoft YaHei;
          font-weight: 400;
          color: #737373;
          & > span:first-of-type {
            display: inline-block;
            line-height: 24px;
            width: 35px;
          }
          .data {
            flex: 1;
            line-height: 24px;
            color: #1a1a1a;
          }
        }
      }
    }
    .header_right {
      display: flex;
      align-items: center;
      margin-left: 100px;
      div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 60px;
        & > span:first-of-type {
          font-size: 14px;
          font-family:
            Microsoft YaHei-Bold,
            Microsoft YaHei;
          font-weight: bold;
          color: #1a1a1a;
        }
        & > span:last-of-type {
          margin-top: 16px;
          font-size: 18px;
          font-family:
            Microsoft YaHei-Regular,
            Microsoft YaHei;
          font-weight: bold;
          color: #409eff;
        }
      }
      & > div {
        padding: 0 26px;
        margin-top: 60px;
        border-right: 1px dashed #dcdcdc;
      }
    }
  }
}
.content {
  ::v-deep {
    .el-form-item__label {
      font-size: 14px;
      font-family:
        Microsoft YaHei-Regular,
        Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-table {
      border: 1px solid #eae9e9;
      &::before {
        display: none;
      }
      .el-button--primary {
        color: #409eff;
        background: #e7f3ff;
        border-color: #e7f3ff;
      }
    }
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }
    .el-table .cell {
      color: #1a1a1a;
    }
  }
}
</style>
