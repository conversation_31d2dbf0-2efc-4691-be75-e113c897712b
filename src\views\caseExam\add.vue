<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="addDialog" center="center" top="48px" @close="close" custom-class="examDialog" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="180px" :rules="rules" inline>
        <el-form-item label="考试名称:" prop="name">
          <el-input class="name__input" v-model="form.name" maxlength="50" placeholder="请输入考试名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="考试开始时间:" prop="startTime">
          <el-date-picker class="startTime__input" v-model="startTime" type="datetime" placeholder="选择考试开始时间" format="yyyy-MM-dd HH:mm" :picker-options="startTimePickerOptions" @change="startTimePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="考试结束时间:" prop="endTime">
          <el-date-picker class="endTime__input" v-model="endTime" type="datetime" placeholder="选择考试结束时间" format="yyyy-MM-dd HH:mm" :picker-options="endTimePickerOptions" @change="endTimePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="考试限时:" prop="time"> <el-input-number class="time__input" v-model="form.time" :min="1" :max="timeLimit" label="考试限时"></el-input-number> </el-form-item>
        <el-form-item label="及格分:" prop="passScore"> <el-input-number class="passScore__input" v-model="form.passScore" :min="1" :max="100" label="及格分"></el-input-number> </el-form-item>
        <el-form-item label="考核病例:" prop="caseIds" class="select-case">
          <el-button type="primary" size="small" class="select-case__button" @click="selectCase">选择病例</el-button>
          <div v-if="selectList.length" class="caseBox">
            <div v-for="item in selectList" :key="item.caseId" class="caseItem">
              <div class="case-item_header">
                <span>分数权重</span>
                <el-select popper-class="noXScroll" v-model="item.weight" placeholder="选择权重" :disabled="selectList.length <= 1 ? true : false || form.type === 2" @change="weightChnage($event, item.caseId)">
                  <el-option v-for="item in weightOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
                <div class="question" @click="lookCaseDetails(item)">病例详情 <i class="el-icon-arrow-right"></i></div>
              </div>
              <div class="case-item_body">
                <div class="caseItem_top_left">
                  <casePhoto :sex="item.sex" :age="item.age" />
                </div>
                <div class="caseItem_top_right">
                  <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
                    <div class="caseName">{{ item.name }}</div>
                  </el-tooltip>
                  <div class="patientInfo">
                    <span :class="{ man: item.sex === 'M' }" class="woman">（{{ item.sex === 'M' ? '男' : '女' }}）</span>
                    <span class="sex"> {{ item.age }} 岁</span>
                  </div>
                  <div class="score">
                    <span>病例总分</span>
                    <span> {{ item.allScore }}分</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="考核设置:" prop="type">
          <el-radio-group v-model="form.type" :disabled="selectList.length <= 1" @change="typeChange">
            <el-radio :label="1">随机全考<span>所选病例，随机全部考，顺序随机</span> </el-radio>
            <el-radio :label="2">随机考一个 <span>所选病例，随机考一个</span></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="班级学生:" prop="studentIds" class="select-student">
          <el-button type="primary" class="select-student__button" size="small" @click="selectStudent">选择班级学生</el-button>
          <el-card v-if="checkedStudentList.length" class="checkedStudents">
            <div slot="header">选择的班级学生</div>
            <div v-for="item in checkedStudentList" :key="item.id">
              <div class="checkedClass">{{ item.className }}</div>
              <ul class="students">
                <li v-for="li in item.children" :key="li.id">
                  <el-tag @close="tagClose(li, item)">{{ li.name }}</el-tag>
                </li>
              </ul>
            </div>
          </el-card>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button class="cancel__button" @click="close">取 消</el-button>
        <el-button class="confirm__button" type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 选择病例 -->
    <SelectCase ref="SelectCase" :selectCaseDialog.sync="selectCaseDialog" :selectList="selectList" @success="selectCaseOver" />
    <!-- 选择班级学生 -->
    <SelectStudent ref="SelectStudent" :selectStudentDialog.sync="selectStudentDialog" @success="selectStudentOver" />
    <!-- 病例详情 -->
    <LookCaseDetails ref="LookCaseDetails" :showDialog.sync="lookCaseDetailsDialog" />
  </div>
</template>
<script>
import { caseExamAdd, caseExamUpdate, caseExamTimeJudge } from '@/api/caseExam'
import casePhoto from '@/components/casePhoto'
import { formatDate } from '@/filters'
import SelectCase from '@/views/caseExam/components/SelectCase'
import SelectStudent from '@/views/caseExam/components/SelectStudent'
import LookCaseDetails from '@/views/caseExam/components/LookCaseDetails'
export default {
  name: 'AddExamDialog',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    casePhoto,
    SelectCase,
    SelectStudent,
    LookCaseDetails
  },

  data() {
    return {
      form: {
        name: null,
        clbumIds: null,
        startTime: null,
        endTime: null,
        time: null, // 考试限时
        state: 1,
        passScore: null,
        caseIds: [],
        studentIds: [],
        type: null, // 类型 1 随机全考 2 随机抽考一个 3 考一个
        allScore: 0
      },
      startTime: null,
      endTime: null,
      startTimePickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      endTimePickerOptions: {
        disabledDate: (time) => {
          const date = Date.parse(formatDate(time, 'yyyy-MM-dd hh:mm:ss'))
          const startTime = Date.parse(this.form.startTime)
          return date < startTime - 8.64e7 || time.getTime() < Date.now() - 8.64e7
        }
      },
      selectCaseDialog: false,
      selectStudentDialog: false,
      rules: {
        name: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
        startTime: [{ required: true, message: '请选择考试开始时间', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择考试结束时间', trigger: 'change' }],
        time: [{ required: true, message: '请输入考试限时', trigger: 'blur' }],
        publishTime: [{ required: true, message: '请选择成绩公布时间', trigger: 'change' }],
        passScore: [{ required: true, message: '请输入及格分数', trigger: 'blur' }],
        caseIds: [{ type: 'array', required: true, message: '病例不能为空', trigger: 'change' }],
        studentIds: [{ type: 'array', required: true, message: '学生不能为空', trigger: 'change' }],
        type: [{ required: true, message: '请选择考核设置', trigger: 'change' }]
      },
      selectList: [],
      checkedStudentList: [],
      isCopy: false,
      weightOptions: [
        { value: 10, label: '10%' },
        { value: 20, label: '20%' },
        { value: 30, label: '30%' },
        { value: 40, label: '40%' },
        { value: 50, label: '50%' },
        { value: 60, label: '60%' },
        { value: 70, label: '70%' },
        { value: 80, label: '80%' },
        { value: 90, label: '90%' },
        { value: 100, label: '100%' }
      ],
      lookCaseDetailsDialog: false
    }
  },
  computed: {
    dialogTitle() {
      let label = null
      if (this.form.examId) {
        label = '编辑考核'
      } else {
        label = this.isCopy ? '复制考核' : '添加考核'
      }
      return label
    },
    timeLimit() {
      if (this.form.startTime && this.form.endTime) {
        // 使用示例
        const startTime = new Date(this.form.startTime).getTime()
        const endTime = new Date(this.form.endTime).getTime()
        const totalTime = endTime - startTime
        const maxTime = Math.floor(totalTime / 60000)
        return maxTime > 240 ? 240 : maxTime
      } else {
        return 240
      }
    }
  },
  created() {},
  methods: {
    startTimePickerChange(val) {
      if (val) {
        this.form.startTime = formatDate(val, 'yyyy-MM-dd hh:mm:ss')
      } else {
        this.form.startTime = null
      }
    },
    endTimePickerChange(val) {
      if (val) {
        const startTime = Date.parse(this.form.startTime)
        const endTime = Date.parse(val)
        if (startTime >= endTime) {
          this.endTime = null
          return this.$message.warning('考试结束时间不能小于考试开始时间')
        } else {
          this.form.endTime = formatDate(val, 'yyyy-MM-dd hh:mm:ss')
        }
      } else {
        this.form.endTime = null
      }
    },
    /** 选择病例*/
    selectCase() {
      this.$refs['SelectCase'].getCaseList()
      this.selectCaseDialog = true
    },
    selectCaseOver(list) {
      this.selectList = list
      this.form.caseIds = this.selectList.map((item) => {
        return {
          caseId: item.caseId,
          weight: item.weight
        }
      })
      // 单选病例
      if (this.form.caseIds.length === 1) {
        this.form.type = 3
        this.selectList[0].weight = 100
      } else {
        this.form.type = null
      }
      this.$refs['form'].validateField('caseIds')
    },
    // 选中权重
    weightChnage(val, caseId) {
      const allWeight = this.form.caseIds.map((item) => (item.weight ? item.weight : 0)).reduce((accumulator, currentValue) => accumulator + currentValue, 0)
      if (allWeight + val > 100) {
        this.$message.warning('权重值不能超过100')
        this.selectList.forEach((item) => {
          if (item.caseId === caseId) {
            item.weight = null
          }
        })
        this.form.caseIds.forEach((item) => {
          if (item.caseId === caseId) {
            item.weight = 0
          }
        })
        return
      } else {
        this.form.caseIds.forEach((item) => {
          if (item.caseId === caseId) {
            item.weight = val
          }
        })
      }
    },
    // 查看病例详情
    lookCaseDetails(item) {
      this.lookCaseDetailsDialog = true
      this.$refs['LookCaseDetails'].getCaseDetails(item)
    },
    /** 选择班级学生 */
    selectStudent() {
      this.selectStudentDialog = true
      this.$refs['SelectStudent'].getClassAll()
    },
    selectStudentOver({ checkedList, checkIds }) {
      this.checkedStudentList = [...checkedList]
      this.form.studentIds = [...checkIds]
      this.$nextTick(() => {
        this.$refs['form'].validateField('studentIds')
      })
    },
    tagClose(li, data) {
      this.$refs['SelectStudent'].TagClose(li, data)
    },

    typeChange(val) {
      if (val === 2) {
        this.selectList.forEach((item) => {
          item.weight = null
        })
      }
    },
    showData(data, type) {
      this.isCopy = type === 'copy' ? true : false
      this.form = {
        examId: type === 'copy' ? null : data.examId,
        name: data.name,
        clbumIds: data.clbumIds,
        startTime: data.startTime,
        endTime: data.endTime,
        time: data.time, // 考试限时
        state: data.state,
        passScore: data.passScore,
        caseIds: data.cases.map((item) => {
          return {
            caseId: item.case_id,
            weight: data.type === 3 ? 100 : data.type === 2 ? null : item.weight
          }
        }),
        studentIds: data.students.map((item) => item.student_id),
        type: data.type,
        allScore: parseFloat(data.allScore)
      }
      this.startTime = this.form.startTime
      this.endTime = this.form.endTime
      // 回显病例
      data.cases.forEach((item) => {
        this.selectList.push({
          caseId: item.case_id,
          weight: data.type === 3 ? 100 : data.type === 2 ? null : item.weight,
          allScore: item.all_score,
          sex: item.sex,
          name: item.name,
          age: item.age,
          form: item.form,
          mainDemands: item.main_demands
        })
      })
      this.$refs['SelectCase'].getCaseList()
      this.$refs['SelectStudent'].showData(data)
    },
    // 关闭
    close() {
      this.$emit('update:addDialog', false)
      this.form = {
        name: null,
        clbumIds: null,
        startTime: null,
        endTime: null,
        time: null, // 考试限时
        state: 1,
        passScore: null,
        caseIds: [],
        studentIds: [],
        type: null, // 类型 1 随机全考 2 随机抽考一个 3 考一个
        allScore: 0
      }
      this.startTime = null
      this.endTime = null
      this.selectList = []
      this.checkedStudentList = []
      this.$refs['SelectStudent'].checkStudents = []
      this.$refs['SelectStudent'].checkIds = []
      this.$refs['form'].resetFields()
    },

    confirm() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          // 判断当前时间下是否有考试
          const timeInfo = {
            startTime: this.form.startTime,
            endTime: this.form.endTime,
            examId: this.form.examId ? this.form.examId : null,
            type: this.form.examId ? 2 : 1
          }
          const startTime = new Date(timeInfo.startTime).getTime()
          const endTime = new Date(timeInfo.endTime).getTime()
          if (startTime > endTime) {
            return this.$message.warning('考试开始时间不能大于结束时间')
          }
          await caseExamTimeJudge(timeInfo)

          // 判断选中的病例权重是否全部选择,多选病例才有验证
          if (this.form.caseIds.length > 1 && this.form.type === 1) {
            const isWeightNull = this.form.caseIds.some((item) => !item.weight)
            if (isWeightNull) {
              return this.$message.error('病例的权重值不能为空')
            }
            // 判断所有病例的权重是否等于100
            const totalWeight = this.form.caseIds.reduce((accumulator, currentValue) => {
              return accumulator + currentValue.weight
            }, 0)
            if (totalWeight < 100) {
              return this.$message.error('病例权重值加起来必须等于100')
            }
          }
          // 根据不同的考核模式设置不同的总分数
          if (this.form.type === 2) {
            /** 判断选中病例总分是否一致 */
            const caseAllScore = this.selectList[0].allScore
            const different = this.selectList.some((item) => item.allScore != caseAllScore)
            if (different) {
              return this.$message.error('当前考核设置模式下需所有病例分数一致！')
            } else {
              this.form.allScore = this.selectList[0].allScore

              /** 设置所有选中的病例权重为100 */
              this.form.caseIds.forEach((item) => {
                item.weight = 100
              })
            }
          } else if (this.form.type === 1) {
            // 根据权重值设置总分数
            const allscore = await this.setAllScore()
            this.form.allScore = parseFloat(allscore.toFixed(2))
          } else {
            this.form.allScore = this.selectList[0].allScore
          }

          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.form.clbumIds = this.checkedStudentList.map((item) => item.classId).join(',')
          if (this.form.examId) {
            caseExamUpdate(this.form)
              .then(() => {
                loading.close()
                this.$message.success('修改考核成功！')
                this.$emit('success')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          } else {
            caseExamAdd(this.form)
              .then(() => {
                loading.close()
                this.$message.success(`${this.isCopy ? '复制' : '添加'}考核成功！`)
                this.$emit('success')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    },
    setAllScore() {
      return new Promise((resolve, reject) => {
        const caseAllScore = this.selectList.reduce((accumulator, currentValue) => {
          return accumulator + parseFloat(currentValue.allScore) * (currentValue.weight / 100)
        }, 0)
        resolve(caseAllScore)
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .selectCase {
    .el-dialog__body {
      padding-bottom: 0;
    }
  }
}
::v-deep {
  .examDialog {
    width: 1264px;
    height: 800px;
    background: #ffffff;
    border-radius: 30px 30px 30px 30px;
    .el-dialog__header {
      .el-dialog__title {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
        line-height: 26px;
        text-align: center;
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          font-size: 24px;
          font-weight: 600;
          color: #666666;
        }
      }
    }
    .el-dialog__body {
      max-height: 680px;
      height: 670px;
      padding: 47px 40px 0 37px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 6px;
        background: transparent;
      }

      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #81909a;
        border-radius: 40px;
      }

      .el-form {
        display: flex;
        flex-wrap: wrap;

        .el-form-item {
          display: flex;
          align-items: center;
          margin-bottom: 30px;
          margin-right: 40px;
          .el-form-item__label {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #333333;
          }
          .el-form-item__content {
            flex: 1;
            line-height: inherit;
          }
          .name__input,
          .startTime__input,
          .endTime__input {
            width: 340px;
            height: 50px;
            .el-input__inner {
              width: 100%;
              height: 100%;
              background: #f5f5f5;
              border-radius: 10px 10px 10px 10px;
              border: none;
              font-size: 20px;
              color: #333333;
              &::placeholder {
                color: #999999;
              }
            }
            .el-input__suffix {
              .el-input__suffix-inner {
                .el-icon-circle-close {
                  margin-right: 5px;
                  margin-top: 2px;
                  font-size: 20px;
                }
              }
            }
            .el-input__prefix {
              .el-icon-time {
                margin-top: 3px;
                font-size: 20px;
              }
            }
          }
          .time__input,
          .passScore__input {
            width: 338px;
            height: 50px;
            background: #dadde2;
            border-radius: 11px;
            overflow: hidden;
            .el-input-number__decrease,
            .el-input-number__increase {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 53px;
              height: 100%;
              background: #274e6a;
              i {
                font-size: 20px;
                color: rgba($color: #fff, $alpha: 0.9);
              }
            }
            .el-input-number__decrease {
              border-radius: 10px 0px 0px 10px;
            }
            .el-input-number__increase {
              border-radius: 0px 10px 10px 0px;
            }
            .el-input {
              height: 100%;
              .el-input__inner {
                height: 100%;
                background: #dadde2;
                border: none;
                color: #274e6a;
                font-size: 30px;
              }
            }
          }
          .el-radio-group {
            display: flex;
            .el-radio {
              display: flex;
              align-items: center;
              margin-right: 15px;
              .el-radio__inner {
                width: 18px;
                height: 18px;
                background: #fff;
                border-color: #b1b1b1;
              }
              .el-radio__label {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 20px;
                color: #b1b1b1;
              }
            }

            .el-radio__input.is-checked .el-radio__inner {
              &::after {
                background: #274e6a;
                width: 12px;
                height: 12px;
              }
            }
            .el-radio__input.is-checked + .el-radio__label {
              color: #274e6a;
            }
          }
        }
      }

      .select-case,
      .select-student {
        .select-case__button,
        .select-student__button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 130px;
          height: 50px;
          padding: 0;
          background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
          border: none;
          border-radius: 10px 10px 10px 10px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
        }
        .select-student__button {
          width: 180px;
        }
      }
      // .selectContent {
      //   width: 100%;
      //   .el-form-item__content {
      //     width: calc(100% - 180px);
      //   }
      // }
    }
    .el-dialog__footer {
      padding: 0;
      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }
      .cancel__button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 93px;
        height: 50px;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #274e6a;
      }
      .confirm__button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 93px;
        height: 50px;
        background: #274e6a;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #fff;
      }
    }

    .el-card__body {
      padding-top: 0;
    }
    .checkedStudents {
      width: 981px;
      margin-top: 18px;
      background: #ffffff;
      box-shadow: 0px 3px 7px 0px rgba(204, 198, 198, 0.25);
      border-radius: 14px 14px 14px 14px;
      border: 1px solid #edeef2;
      .el-card__header {
        div {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #333333;
        }
      }
      .checkedClass {
        margin-top: 23px;
        margin-bottom: 13px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #274e6a;
      }
      .students {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        margin: 0;
        list-style: none;
        li {
          margin-right: 20px;
          margin-bottom: 12px;
          .el-tag {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            padding: 0 16px;
            background: #f3f5f9;
            border-radius: 8px 8px 8px 8px;
            border: none;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #07121e;
            .el-icon-close {
              top: 2px;
              width: 20px;
              height: 20px;
              margin-left: 8px;
              font-size: 20px;
              color: #274e6a;
              &:hover {
                cursor: pointer;
                background: transparent;
              }
            }
          }
        }
      }
    }
  }
  // 选择后病例样式
  .caseBox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    .caseItem {
      width: 470px;
      height: 256px;
      margin-right: 20px;
      margin-bottom: 20px;
      padding: 20px 22px 20px 25px;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(177, 177, 177, 0.15);
      border-radius: 20px 20px 20px 20px;
      border: 1px solid #e8e8e8;
      &:nth-of-type(2n) {
        margin-right: 0;
      }
      .case-item_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > span {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #000000;
        }
        .el-select {
          .el-input__inner {
            width: 232px;
            height: 45px;
            background: #eef0f2;
            border-radius: 10px;
            border: none;
            color: #333333;
            font-size: 20px;
          }
        }
        .question {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #65849a;
          cursor: pointer;
        }
      }

      .case-item_body {
        display: flex;
        margin-top: 25px;
        .caseItem_top_left {
          width: 115px;
          height: 140px;
          box-shadow: inset 8px 10px 50px 0px rgba(255, 255, 255, 0.3);
          border-radius: 10px 10px 10px 10px;
          ::v-deep {
            img {
              width: 100%;
              height: 100%;
              object-fit: scale-down;
            }
          }
        }
        .caseItem_top_right {
          flex: 1;
          padding: 10px;
          line-height: 24px;
          overflow: hidden;
          .caseName {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #07121e;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .patientInfo {
            margin: 17px 0 15px;
            .woman {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 20px;
              color: #f494b7;
            }
            .man {
              color: #3381b9;
            }
            .sex {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 20px;
              color: #666666;
            }
          }
          .score {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 292px;
            height: 50px;
            background: #eef0f2;
            border-radius: 10px 10px 10px 10px;
            & > span:first-of-type {
              font-size: 20px;
              font-family: PingFang SC;
              font-weight: 500;
              color: #07121e;
            }
            & > span:last-of-type {
              margin-left: 25px;
              font-size: 20px;
              font-family: PingFang SC;
              font-weight: bold;
              color: #3381b9;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.caseNameTooltip {
  font-size: 18px;
}
.noXScroll {
  .el-select-dropdown__wrap {
    overflow-x: hidden;
  }
}
</style>
