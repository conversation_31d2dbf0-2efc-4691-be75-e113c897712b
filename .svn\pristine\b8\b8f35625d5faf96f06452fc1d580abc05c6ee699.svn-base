<template>
  <div class="app-container">
    <div class="caseBody">
      <el-row>
        <div class="back" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          <span>病例库</span>
        </div>
      </el-row>
      <el-form ref="form" :model="queryInfo" label-width="80px" inline>
        <el-form-item label="病例名称:">
          <el-input v-model="queryInfo.name" size="small" maxlength="40" placeholder="请输入病例名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>
        <el-form-item label="病例类型:">
          <el-radio-group v-model="queryInfo.caseType" @change="getList">
            <el-radio :label="1">学习病例</el-radio>
            <el-radio :label="2">考核病例</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="getList">查询</el-button>
          <el-button plain size="small" @click="reset">重置</el-button>
        </el-form-item>
        <el-form-item style="float: right">
          <el-button type="primary" size="small" @click="casePermissionDialog = true">
            <svg-icon icon-class="lock"></svg-icon>

            病例权限
          </el-button>
          <el-button type="primary" size="small" icon="el-icon-circle-plus" @click="addDialog = true">添加病例</el-button>
        </el-form-item>
      </el-form>
      <div class="caseContent">
        <ul>
          <li v-for="item in list" :key="item.caseId">
            <el-row type="flex" align="middle" justify="space-between">
              <div class="name">
                <span :style="{ background: item.caseType === 2 ? '#ff9f1e' : '#1bc75a' }">{{ item.caseType === 1 ? '学' : '考' }}</span>
                <span>{{ item.name }}</span>
              </div>
              <div class="switch">
                <el-switch v-model="item.isEnable" :active-value="1" :inactive-value="0" active-color="#3f9cfd" inactive-color="#d5dae0" @change="switchChange($event, item)"> </el-switch>
              </div>
            </el-row>
            <div class="caseInfo">
              <div>
                <img v-if="item.sex === 'M'" src="@/assets/case/caseM.png" alt="" />
                <img v-else src="@/assets/case/caseF.png" alt="" />
                <div>
                  <div>{{ item.realName }}</div>
                  <div>
                    <span> {{ item.sex === 'F' ? '女' : '男' }}</span>
                    <span>{{ item.age }}岁</span>
                  </div>
                </div>
              </div>
              <div class="statistics">
                <span>{{ item.questionCount }}</span>
                <span>问题数量</span>
              </div>
              <div class="statistics">
                <span>{{ item.allScore }}</span>
                <span>总分</span>
              </div>
            </div>
            <div class="operate">
              <el-button type="primary" size="small" @click="historyCollect(item)">病史采集</el-button>
              <div class="operateIcon">
                <el-tooltip class="item" effect="dark" content="编辑" placement="top">
                  <i class="el-icon-edit" @click="edit(item)"></i>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="复制" placement="top">
                  <i class="el-icon-copy-document" @click="copy(item)"></i>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top">
                  <i class="el-icon-delete" @click="del(item)"></i>
                </el-tooltip>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>

    <!-- 新增病例 -->
    <AddCase ref="AddCase" :addDialog.sync="addDialog" @success="getList" />
    <!-- 病例权限 -->
    <CasePermission :casePermissionDialog.sync="casePermissionDialog" />
  </div>
</template>
<script>
import { caseList, caseCopy, caseRemove, caseUpdate } from '@/api/case'

import AddCase from './add'
import CasePermission from '@/views/case/casePermission'
export default {
  name: 'Case',
  components: {
    AddCase,
    CasePermission
  },
  data() {
    return {
      queryInfo: {
        name: null,
        caseType: null,
        pageNum: 1,
        pageSize: 8
      },
      list: [],
      total: 0,
      addDialog: false,
      casePermissionDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null,
        caseType: null,
        pageNum: 1,
        pageSize: 8
      }
      this.getList()
    },
    historyCollect(item) {
      this.$router.push(`/case/historyCollect/${item.caseId}`)
    },
    copy(item) {
      this.$confirm('确定要复制该病例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseCopy({ ...item })
          this.$message({
            type: 'success',
            message: '复制成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消复制'
          })
        })
    },
    del(item) {
      this.$confirm('确定要删除该病例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseRemove({ id: item.caseId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    edit(item) {
      this.$refs['AddCase'].form = { ...item }
      this.addDialog = true
    },
    switchChange(val, item) {
      const typeLable = val ? '开启' : '禁用'
      this.$confirm(`确定要${typeLable}该病例吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          caseUpdate({ caseId: item.caseId, isEnable: val }).then(() => {
            this.$message.success(val ? '开启成功!' : '禁用成功!')
            this.getList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: `已取消${typeLable}`
          })
          this.getList()
        })
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  background: #e8eaee;
  .caseBody {
    width: calc(100% - 210px);
    margin: 0 auto;
    .back {
      display: flex;
      align-items: center;
      cursor: pointer;
      i {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        background: #409eff;
        border-radius: 50%;
        text-align: center;
        color: #fff;
      }
      span {
        margin-left: 12px;
        font-size: 18px;
        font-weight: 400;
        color: #1a1a1a;
      }
    }
  }
}
::v-deep {
  .el-form {
    background: #fff;
    padding-top: 15px;
    margin-top: 16px;
    border-radius: 4px;
    .el-form-item__label {
      font-size: 14px;
      font-family:
        Microsoft YaHei,
        Microsoft YaHei;
      font-weight: 400;
      color: #333333;
    }
    .el-form-item {
      margin-bottom: 15px;
      .el-radio__inner {
        width: 18px;
        height: 18px;
      }
      .el-radio__input.is-checked .el-radio__inner {
        background: #fff;
        &::after {
          background: #1890ff;
          width: 8px;
          height: 8px;
        }
      }
    }
  }
}
.caseContent {
  display: flex;
  margin: 0 auto;
  padding-left: 20px;
  padding-top: 45px;
  ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    li {
      position: relative;
      width: 380px;
      height: 202px;
      padding: 18px;
      background: #ffffff;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #dbdbdb;
      margin-right: 24px;
      margin-bottom: 24px;
      &:nth-of-type(4n) {
        margin-right: 0;
      }
      .el-row:first-of-type {
        margin-bottom: 20px;
        .name {
          display: flex;
          align-items: center;
          & > span:first-of-type {
            width: 34px;
            height: 22px;
            line-height: 22px;
            background: #1bc75a;
            border-radius: 11px;
            font-size: 15px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
          }
          & > span:last-of-type {
            margin-left: 8px;
            font-size: 17px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: bold;
            color: #333333;
          }
        }
      }
      .caseInfo {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        width: 100%;
        height: 82px;
        background: #e8e9ec;
        border-radius: 6px;
        & > div:first-of-type {
          display: flex;
          align-items: center;
          & > img {
            width: 30px;
            height: 52px;
          }
          & > div {
            margin-left: 13px;
            font-size: 16px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            & > div:first-of-type {
              margin-bottom: 5px;
            }
            & > div > span:last-of-type {
              margin-left: 10px;
            }
          }
        }
        .statistics {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 88px;
          height: 46px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          & > span:first-of-type {
            font-size: 14px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            color: #333333;
          }
          & > span:last-of-type {
            margin-top: 3px;
            font-size: 14px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            color: #666666;
          }
        }
      }
      .operate {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 16px;
        .operateIcon {
          i {
            font-size: 20px;
            margin-left: 18px;
            color: #a2b0bf;
            cursor: pointer;
            &:hover {
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}
</style>
