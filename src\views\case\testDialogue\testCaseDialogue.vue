<template>
  <div class="app-container">
    <template v-if="info">
      <div class="content_left">
        <div class="caseInfoBox">
          <div class="caseInfoBoxLeft">
            <Avatar :age="info.age" :sex="info.sex" :showSex="true" />
          </div>
          <div class="caseInfoBoxCenter">
            <p>{{ info.name }}</p>
            <span>{{ info.realName }}</span>
            <span>{{ info.age }}岁</span>
          </div>
          <div class="caseInfoBoxRight">
            <div>
              <span>{{ info.questionCount }}</span>
              <span>问题数量</span>
            </div>
            <div>
              <span>{{ testListNum }}</span>
              <span>测试问题数量</span>
            </div>
          </div>
        </div>
        <div class="personView">
          <span>Hi，我是模拟人:{{ info.realName }}</span>
          <img :src="personImg" alt="" />
        </div>
        <div class="overTestBtn" @click="endTest">结束测试</div>
      </div>
      <div class="content_right">
        <div class="headerTitle">与{{ info.realName }}的问诊对话 <i></i></div>
        <div class="content" id="dialogueContentParent">
          <div class="dialogueContent" v-for="(item, index) in recordList" :key="index">
            <div class="ask">
              <section class="askText">
                {{ item.userProblem }}
              </section>
              <section class="photo">
                <img src="@/assets/case/ai/doctorPhoto.png" alt="" />
              </section>
            </div>
            <div class="reply">
              <section class="photo">
                <Avatar :age="info.age" :sex="info.sex" :showSex="false" />
              </section>
              <section class="askText">
                {{ item.answer }}
              </section>
              <section class="operate" v-if="!item.isFake">
                <el-tooltip popper-class="operateTooltip" effect="dark" content="编辑" placement="bottom">
                  <i class="el-icon-edit" @click="edit(item)"></i>
                </el-tooltip>
                <el-tooltip popper-class="operateTooltip" effect="dark" content="删除" placement="bottom">
                  <i class="el-icon-delete" @click="remove(item)"></i>
                </el-tooltip>
                <el-tooltip popper-class="operateTooltip" effect="dark" content="播放语音" placement="bottom">
                  <img v-if="playVoiceQuestionId === item.questionId && playVoiceQuestionIndex === index" src="@/assets/case/ai/play.gif" alt="" @click="clearVoice" />
                  <svg-icon v-else iconClass="earphone" @click="createVoice(item, index)"></svg-icon>
                </el-tooltip>
              </section>
            </div>
          </div>
        </div>
        <div class="footer">
          <el-input v-model="textvalue" :disabled="recording" :placeholder="recording ? '按住F2开始对话' : '请输入问题'" maxlength="60" @keyup.enter.native="send"></el-input>
          <div class="btnBox">
            <div class="sendBtn" @click="send">发送(Enter)</div>
            <!-- <el-popover placement="bottom" popper-class="cutModelPopover" trigger="click">
            <div>
              <img src="@/assets/case/ai/voiceIcon.png" alt="" />
              语音
            </div>
            <div>
              <img src="@/assets/case/ai/keyboardIcon.png" alt="" />
              输入
            </div>
            slot="reference"
          </el-popover> -->

            <div :class="[{ recordingType: recording }, 'inputType']" @click="recording = !recording">
              <el-tooltip effect="dark" :content="recording ? '语音模式：按住语音键说话，不可修改文字内容，自动发送' : '混合模式：语音与键盘输入，可以更改文字内容，手动发送'" placement="top">
                <img v-if="recording" src="@/assets/case/ai/voiceIcon.png" alt="" />
                <img v-else src="@/assets/case/ai/keyboardIcon.png" alt="" />
              </el-tooltip>
            </div>
          </div>
        </div>

        <!-- 语音输入中状态 -->
        <div v-if="InDialogue" class="voice_input_box">
          <img src="@/assets/case/ai/voice_input.png" alt="" />
          <span>正在语音输入中...</span>
        </div>
      </div>
    </template>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑" custom-class="editDialog" center :visible.sync="editDialog" :close-on-click-modal="false" width="width">
      <div v-if="editQuestionInfo" class="editDialog-content">
        <div>
          <label>问题：</label>
          <el-input v-model="editQuestionInfo.problem" placeholder="请输入问题"></el-input>
        </div>
        <div>
          <label>回答：</label>
          <el-input v-model="editQuestionInfo.answer" placeholder="请输入回答"></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="editDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">保存编辑</el-button>
      </div>
    </el-dialog>
    <VoiceToText ref="voiceToText" :recordingStatus="recording" :InDialogue.sync="InDialogue" @audioEnded="audioEnded" @recognitionOver="recognitionResult" />
  </div>
</template>
<script>
import _ from 'lodash'
import { caseDetail, caseUpdateQuestion, caseRemoveQuestionBatch } from '@/api/case.js'
import Avatar from '@/views/case/Avatar'
import VoiceToText from './voiceToText'
import JieBa from '@/utils/jieba.js'

export default {
  name: '',
  components: {
    Avatar,
    VoiceToText
  },
  data() {
    return {
      info: null,
      questionList: [],
      recordList: [],
      textvalue: '',
      testListNum: 0,
      recording: false,
      InDialogue: false, // 对话中
      // 编辑
      editDialog: false,
      editQuestionInfo: null,
      playVoiceQuestionId: null, // 播放语音的问题id
      playVoiceQuestionIndex: null // 播放语音的索引
    }
  },
  created() {
    this.getDetails()
  },
  watch: {
    recordList(val) {
      window.sessionStorage.setItem('caseTestCode', JSON.stringify(val))
    }
  },
  beforeDestroy() {
    window.sessionStorage.removeItem('caseTestCode')
  },
  computed: {
    personImg() {
      try {
        // 预定义所有可能的图片路径
        const imageMap = {
          man1: require('@/assets/case/ai/testCode-man1.png'),
          man2: require('@/assets/case/ai/testCode-man2.png'),
          man3: require('@/assets/case/ai/testCode-man3.png'),
          woman1: require('@/assets/case/ai/testCode-woman1.png'),
          woman2: require('@/assets/case/ai/testCode-woman2.png'),
          woman3: require('@/assets/case/ai/testCode-woman3.png')
        }

        // 确定使用哪张图片
        let imageKey = ''
        const age = this.info.age
        if (this.info.sex === 'M') {
          imageKey = age < 35 ? 'man1' : age < 59 ? 'man2' : 'man3'
        } else {
          imageKey = age < 35 ? 'woman1' : age < 59 ? 'woman2' : 'woman3'
        }

        return imageMap[imageKey]
      } catch (error) {
        console.error('加载图片失败:', error)
        // 返回一个默认图片或空字符串
        return ''
      }
    }
  },
  methods: {
    async getDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.info = data
      this.questionList = data.questions
      const localData = window.sessionStorage.getItem('caseTestCode')
      if (localData) {
        const localList = JSON.parse(localData)
        this.recordList = localList
        const localIds = localList.map((item) => item.questionId)
        this.questionList.forEach((item) => {
          this.$set(item, 'visited', localIds.includes(item.questionId))
          this.recordList.forEach((li) => {
            if (!li.isFake && li.questionId === item.questionId) {
              li.answer = item.answer
            }
          })
        })
        this.computeNum()
      } else {
        this.questionList.forEach((item) => {
          this.$set(item, 'visited', false)
        })
      }
    },
    endTest() {
      this.$router.push('/case')
    },
    audioEnded() {
      this.playVoiceQuestionId = null
      this.playVoiceQuestionIndex = null
    },
    // #region 发送相关逻辑
    recognitionResult(val) {
      this.textvalue = val
      if (this.recording) {
        this.send()
      }
    },
    send() {
      if (!this.textvalue.trim()) {
        this.textvalue = ''
        return
      }
      const results = this.initialMatch(this.textvalue)
      if (results.length) {
        const currentProblem = results[0]
        // 判断是否问过该问题
        const isExist = this.recordList.some((item) => item.questionId === currentProblem.questionId)
        if (!isExist) {
          this.testListNum += 1
        }

        this.recordList.push(currentProblem)
        if (currentProblem.answerFileUrl) {
          this.$refs['voiceToText'].playAudio(currentProblem)
        }
        // const { similarity } = results[0]
        // if (similarity > 0.7) {
        //   const currentProblem = results[0]
        //   console.log(currentProblem)
        //   this.recordList.push(currentProblem)
        //   this.$refs['voiceToText'].playAudio(currentProblem)
        // } else {
        //   // 2.如果最相似阈值小于0.7，则需要拿阈值0.5以上的问题，阈值从高到低依次回答
        //   const searchNoVisited = results.some((item) => !item.visited)
        //   let currentProblem
        //   if (searchNoVisited) {
        //     currentProblem = results.find((item) => !item.visited)

        //     this.questionList.forEach((item) => {
        //       if (item.questionId === currentProblem.questionId) {
        //         item.visited = true
        //       }
        //     })
        //   } else {
        //     currentProblem = results[0]
        //   }
        //   console.log(currentProblem)

        //   this.recordList.push(currentProblem)
        //   this.$refs['voiceToText'].playAudio(currentProblem)
        // }
      } else {
        this.recordList.push({
          problem: this.textvalue,
          userProblem: this.textvalue,
          answer: '抱歉，我不明白您的意思。',
          questionId: (Math.random() + new Date().getTime()).toString(32).slice(0, 8),
          isFake: true,
          answerFileUrl: ''
        })
      }
      this.$nextTick(() => {
        this.textvalue = ''
        this.scrollBottom()
      })
    },
    initialMatch(value) {
      const results = []
      const EXCLUDE_WORDS_ARRAY = ['了', '呢', '啊', '哦', '恩', '嗯', '吧', '吗']
      // 将数组中的词语进行拼接，用于创建正则表达式
      const excludePattern = new RegExp(EXCLUDE_WORDS_ARRAY.join('|'), 'g')
      let userInputProblem = value.replace(excludePattern, '')
      // console.log(value, userInputProblem)

      this.questionList.forEach((item) => {
        const jieba = new JieBa(userInputProblem, item.problem)
        const level = jieba.run()
        results.push({
          ...item,
          visited: item.visited,
          problem: item.problem,
          userProblem: this.textvalue,
          answer: item.answer,
          questionId: item.questionId,
          answerFileUrl: item.answerFileUrl,
          similarity: level
        })
      })

      const bestLevelList = results
        .sort((a, b) => b.similarity - a.similarity)
        // .splice(0, 3)
        .filter((item) => !!item.similarity)
      return bestLevelList
    },
    // #endregion

    // #region 操作功能区域
    // 编辑
    edit(item) {
      this.editQuestionInfo = _.cloneDeep(item)
      this.editDialog = true
    },
    saveEdit() {
      const loading = this.$loading({
        text: '数据保存中,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      caseUpdateQuestion({ ...this.editQuestionInfo })
        .then(() => {
          loading.close()
          this.$message.success('修改问题成功！')
          this.editDialog = false
          this.getDetails()
        })
        .catch(() => {
          loading.close()
        })
    },
    async createVoice(row, index) {
      if (!row.answerFileUrl) this.$message.warning('请前往病史采集生成语音')
      this.playVoiceQuestionId = row.questionId
      this.playVoiceQuestionIndex = index
      this.$refs['voiceToText'].playAudio(row)
    },
    clearVoice() {
      this.playVoiceQuestionId = null
      this.playVoiceQuestionIndex = null
      this.$refs['voiceToText'].clearAudio()
    },
    remove(row) {
      this.$confirm('确定要删除该问题吗? ', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true,
        customClass: 'caseConfirm'
      })
        .then(async () => {
          await caseRemoveQuestionBatch({ ids: [row.questionId], caseId: this.info.caseId })
          this.recordList = this.recordList.filter((item) => item.questionId !== row.questionId)
          this.getDetails()
        })
        .catch(() => {})
    },
    // 计算问到的测试问题数量
    computeNum() {
      const existList = this.recordList.filter((item) => !item.isFake)
      const questions = _.uniqBy(existList, 'questionId')
      this.testListNum = questions.length
    },
    // #endregion
    scrollBottom(id = 'dialogueContentParent') {
      let div = document.getElementById(id)
      if (div) {
        setTimeout(() => {
          div.scrollTop = div.scrollHeight
        }, 300)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100vh;
  padding: 48px 54px;
  background: url('~@/assets/case/ai/testCaseDialogueBg.png') no-repeat;
  background-size: cover;
  .content_left {
    width: 512px;
    .caseInfoBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 102px;
      padding: 0 20px;
      background: #bfd6e3;
      border-radius: 10px;
      .caseInfoBoxLeft {
        .Avatar {
          position: relative;
          width: 70px;
          height: 70px;
          ::v-deep {
            img {
              width: 100%;
              height: 100%;
            }
            .sexIcon {
              position: absolute;
              right: -6px;
              bottom: 6px;
              width: 24px;
              height: 24px;
            }
          }
        }
      }
      .caseInfoBoxCenter {
        p {
          margin: 0;
          margin-bottom: 5px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #000000;
        }
        span {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #666666;
          &:last-child {
            margin-left: 10px;
          }
        }
      }
      .caseInfoBoxRight {
        position: relative;
        display: flex;
        justify-content: space-around;
        width: 182px;
        height: 66px;
        background: #e3f3fc;
        border-radius: 10px;
        &::after {
          content: '';
          position: absolute;
          left: 42%;
          top: 55%;
          height: 33px;
          width: 1px;
          background: rgba($color: #000000, $alpha: 0.5);
          transform: translateY(-50%);
        }
        & > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-family: PingFang SC;

          & > span:first-of-type {
            font-weight: bold;
            font-size: 26px;
            color: #293543;
          }
          & > span:last-of-type {
            margin-top: 4px;
            font-weight: 500;
            font-size: 14px;
            color: rgba(84, 93, 105, 0.8);
          }
        }
      }
    }
    .personView {
      position: relative;
      width: 100%;
      height: 652px;
      margin-top: 24px;
      background: url('~@/assets/case/ai/personBg.png') no-repeat;
      background-size: cover;
      & > span {
        position: absolute;
        left: 30px;
        top: 7px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
      }
      img {
        position: absolute;
        left: 45px;
        bottom: 134px;
        width: 410px;
        height: 500px;
      }
    }
    .overTestBtn {
      width: 136px;
      height: 52px;
      margin: 0 auto;
      margin-top: 25px;
      background: linear-gradient(360deg, #ff6a38 0%, #ff9c49 100%);
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.5);
      border-radius: 10px 10px 10px 10px;
      border: 2px solid #ffbb4f;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 48px;
      text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      cursor: pointer;
    }
  }
  .content_right {
    position: relative;
    flex: 1;
    height: 853px;
    padding: 20px 32px;
    padding-right: 0;
    margin-left: 40px;
    background: rgba(40, 74, 95, 0.7);
    border-radius: 20px 20px 20px 20px;
    & > div {
      padding-right: 32px;
    }
    .headerTitle {
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 25px;
      color: #ffffff;
      i {
        flex: 1;
        height: 1px;
        margin-left: 10px;
        background: rgba(255, 255, 255, 0.5);
      }
    }
    .content {
      height: calc(100% - 128px);
      overflow: auto;
      &::-webkit-scrollbar {
        background: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background: #e3e3e3;
        border-radius: 5px;
      }
      .dialogueContent {
        margin-bottom: 15px;
        .ask,
        .reply {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .askText {
            min-width: 80px;
            max-width: 80%;
            padding: 10px 15px;
            background: #2855ff;
            border-radius: 0 15px 15px 15px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
            font-size: 18px;
            text-align: left;
            line-height: 28px;
          }
          .photo {
            margin-left: 14px;
            ::v-deep {
              img {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                object-fit: scale-down;
              }
            }
          }
          .operate {
            display: flex;
            align-items: center;
            i,
            .svg-icon {
              margin-left: 7px;
              color: #fff;
              font-size: 24px;
              cursor: pointer;
            }
            img {
              margin-left: 7px;
              width: 48px;
              height: 48px;
              cursor: pointer;
            }
          }
        }

        .reply {
          width: 100%;
          justify-content: flex-start;
          margin-top: 15px;
          .photo {
            margin-left: 0;
            margin-right: 14px;
          }
          .askText {
            background: #e3e3e3;
            color: #333333;
          }
        }
      }
    }
    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      width: calc(100% - 32px);
      height: 79px;
      padding-right: 17px;
      margin-top: 20px;
      background: #31556a;
      border-radius: 10px 10px 10px 10px;
      .el-input {
        height: 100%;
        width: calc(100% - 300px);
        ::v-deep {
          .el-input__inner {
            width: 100%;
            height: 100%;
            background: transparent;
            border-radius: 10px;
            border: none;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #ffffff;
          }
          &::placeholder {
            color: #e3e3e3;
          }
        }
      }
      .btnBox {
        display: flex;
        .sendBtn {
          width: 135px;
          height: 50px;
          background: #2a54ff;
          box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
          border-radius: 55px 55px 55px 55px;
          font-family: PingFang SC;
          font-size: 20px;
          color: #ffffff;
          text-align: center;
          line-height: 50px;
          cursor: pointer;
        }
        .inputType {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 45px;
          height: 45px;
          margin-left: 20px;
          background: #455e7e;
          border-radius: 4px 4px 4px 4px;
          cursor: pointer;
        }
      }
    }
    .voice_input_box {
      position: absolute;
      left: 50%;
      top: 35%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 180px;
        height: 180px;
      }
      span {
        margin-top: 30px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 24px;
      }
    }
  }
  ::v-deep {
    .editDialog {
      width: 1006px;
      height: 390px;
      background: #ffffff;
      border-radius: 20px;
      .el-dialog__header {
        padding: 32px 0 0 0;
        .el-dialog__title {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 26px;
          color: #000000;
        }
        .el-dialog__headerbtn {
          top: 10px;
          right: 15px;
          .el-dialog__close {
            font-size: 32px;
            color: #666666;
          }
        }
      }
      .el-dialog__body {
        padding: 45px 50px;
        .editDialog-content {
          & > div {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            label {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 24px;
              color: #666666;
            }
            .el-input {
              flex: 1;
              .el-input__inner {
                width: 100%;
                height: 60px;
                background: #efefef;
                border-radius: 6px;
                border: none;

                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24px;
                color: #000;
                &::placeholder {
                  color: #999999;
                }
              }
            }
          }
        }
      }
      .el-dialog__footer {
        & > div {
          & > .el-button {
            &:first-of-type,
            &:last-of-type {
              padding: 0;
              width: 93px;
              height: 50px;
              border-radius: 63px 63px 63px 63px;
              border: 1px solid #274e6a;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              text-align: center;
              color: #274e6a;
              line-height: 50px;
              cursor: pointer;
              &:hover {
                background: rgba($color: #fff, $alpha: 0.8);
              }
            }
            &:last-of-type {
              width: 132px;
              margin-left: 12px;
              background: #274e6a;
              border: none;
              color: #ffffff;
              &:hover {
                background: rgba($color: #274e6a, $alpha: 0.8);
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
// .cutModelPopover {
//   min-width: 88px;
//   width: 88px;
//   padding: 0;
//   background: rgba(255, 255, 255, 0.5);
//   border-radius: 4px 4px 4px 4px;
//   font-family: PingFang SC;
//   font-weight: 500;
//   font-size: 14px;
//   color: #ffffff;
//   & > div {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     cursor: pointer;
//     img {
//       width: 32px;
//       height: 32px;
//     }
//   }
// }
.operateTooltip {
  font-size: 16px;
}
</style>
