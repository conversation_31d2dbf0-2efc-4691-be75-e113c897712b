<template>
  <div class="app-container">
    <div class="casePractiseBody">
      <el-row>
        <div class="back" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          <span>病例训练记录</span>
        </div>
      </el-row>
      <el-form ref="form" :model="queryInfo" label-width="80px" inline>
        <el-form-item label="病例名称:">
          <el-input v-model="queryInfo.caseName" size="small" maxlength="40" placeholder="请输入病例名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="small" @click="getList">查询</el-button>
          <el-button type="primary" size="small" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="caseContent">
        <ul>
          <li v-for="item in list" :key="item.caseId">
            <div class="info">
              <img v-if="item.sex === 'M'" src="@/assets/case/caseM.png" alt="" />
              <img v-if="item.sex === 'F'" src="@/assets/case/caseF.png" alt="" />
              <div>
                <div class="name">{{ item.name }}</div>
                <div class="person">
                  <span>{{ item.realName }}</span>
                  <span>{{ item.sex === 'F' ? '女' : '男' }}</span>
                  <span>{{ item.age }}岁</span>
                </div>
              </div>
            </div>
            <div class="statistics">
              <div>
                <img src="@/assets/case/timeIcon.png" alt="" />
                <span>操作总用时</span>
                <span>{{ parseInt(item.allTime / 60) }}分钟</span>
              </div>
              <div>
                <img src="@/assets/case/numIcon.png" alt="" />
                <span>操作总次数</span>
                <span>{{ item.allCount }}</span>
              </div>
              <div>
                <img src="@/assets/case/accuracyIcon.png" alt="" />
                <span>平均正确率</span>
                <span>{{ item.avgRate }}%</span>
              </div>
            </div>
            <div class="operate">
              <el-button type="primary" size="small" @click="record(item)">查看训练记录</el-button>
            </div>
          </li>
        </ul>
      </div>
      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total,  prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { casePractiseList } from '@/api/casePractise'
export default {
  name: '',
  data() {
    return {
      queryInfo: {
        caseName: null,
        pageNum: 1,
        pageSize: 8
      },
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await casePractiseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        caseName: null,
        pageNum: 1,
        pageSize: 8
      }
      this.getList()
    },
    record(item) {
      this.$router.push(`/casePractise/details/${item.caseId}/${item.name}`)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  background: #e8eaee;

  .casePractiseBody {
    width: calc(100% - 210px);
    margin: 0 auto;
    .back {
      display: flex;
      align-items: center;
      cursor: pointer;
      i {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        background: #409eff;
        border-radius: 50%;
        text-align: center;
        color: #fff;
      }
      span {
        margin-left: 12px;
        font-size: 18px;
        font-weight: 400;
        color: #1a1a1a;
      }
    }
  }
  ::v-deep {
    .el-form {
      background: #fff;
      padding-top: 15px;
      margin-top: 16px;
      border-radius: 4px;
      .el-form-item__label {
        font-size: 14px;
        font-family:
          Microsoft YaHei,
          Microsoft YaHei;
        font-weight: 400;
        color: #333333;
      }
      .el-form-item {
        margin-bottom: 15px;
      }
    }
  }
}
.caseContent {
  margin: 0 auto;
  padding-left: 20px;
  padding-top: 45px;
  ul {
    display: flex;

    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    li {
      position: relative;
      width: 380px;
      height: 240px;
      margin-bottom: 24px;
      margin-right: 24px;
      padding: 20px 24px;

      background: #ffffff;
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #dbdbdb;
      &:nth-of-type(4n) {
        margin-right: 0;
      }
      .info {
        display: flex;
        & > img {
          width: 30px;
          height: 52px;
        }
        & > div {
          margin-left: 20px;
          .name {
            font-size: 17px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: bold;
            color: #333333;
          }
          .person {
            margin-top: 8px;
            font-size: 16px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            & > span:nth-of-type(2) {
              margin: 0 16px;
            }
          }
        }
      }
      .statistics {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        & > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 98px;
          height: 94px;
          background: #e4e5e8;
          border-radius: 4px 4px 4px 4px;
          &:first-of-type > img {
            width: 18px;
            height: 16px;
          }
          &:nth-of-type(2) {
            margin: 0 14px;
          }
          & > img {
            width: 15px;
            height: 15px;
          }
          & > span:first-of-type {
            margin: 8px 0;
          }
          & > span {
            font-size: 14px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            color: #333333;
          }
        }
      }
      .operate {
        display: flex;
        justify-content: center;
        margin-top: 18px;
      }
    }
  }
}
</style>
