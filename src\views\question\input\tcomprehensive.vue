<template>
  <div>
    <el-form :model="form" :rules="rules" ref="theForm">
      <el-form-item label="题干:" :label-width="labelwidth" prop="question">
        <el-col :span="14" class="small_input">
          <editor-more
            :content="form.question"
            @change="
              (data) => {
                contentChange(data, 'question')
              }
            "
          ></editor-more>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="primary" @click="contentChange({ html: '' }, 'question')">清空</el-button>
        </el-col>
      </el-form-item>
      <el-form-item label="问题:" :label-width="labelwidth" prop="list">
        <el-col :span="14" class="small_input">
          <el-tabs v-model="activeName" :closable="form.list && form.list.length > 1" @edit="handleTabsEdit">
            <el-tab-pane :label="'问题' + (index + 1)" :name="'' + (index + 1)" v-for="(item, index) in form.list"> </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="success" @click="handleTabsEdit(form.list.length + 1, 'add')">添加</el-button>
        </el-col>
      </el-form-item>
      <div v-for="(item, index) in form.list" v-show="activeName == '' + (index + 1)">
        <el-form-item label="题型：" :label-width="labelwidth" prop="questionType">
          <el-radio-group v-model="form.list[index].questionType" @change="typeChange(index)">
            <el-radio :label="item.value" v-for="item in questionTypes">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <tsingle :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form.list[index]" ref="tcompatiblityitem" v-if="form.list[index].questionType == 'SINGLE'"></tsingle>
        <tmultiple :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form.list[index]" ref="tcompatiblityitem" v-if="form.list[index].questionType == 'MULTIPLE'"></tmultiple>
        <tjudge :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form.list[index]" ref="tcompatiblityitem" v-if="form.list[index].questionType == 'JUDGE'"></tjudge>
        <tcompletion :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form.list[index]" ref="tcompatiblityitem" v-if="form.list[index].questionType == 'COMPLETION'"></tcompletion>
        <tshort :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form.list[index]" ref="tcompatiblityitem" v-if="form.list[index].questionType == 'SHORT'"></tshort>
      </div>
    </el-form>
  </div>
</template>

<script>
import EditorMore from '@/components/Editor/index.vue'
import tsingle from '@/views/question/input/components/tsingle'
import tmultiple from '@/views/question/input/components/tmultiple'
import tjudge from '@/views/question/input/components/tjudge'
import tcompletion from '@/views/question/input/components/tcompletion'
import tshort from '@/views/question/input/components/tshort'
export default {
  components: {
    EditorMore,
    tsingle,
    tmultiple,
    tjudge,
    tcompletion,
    tshort
  },
  props: {
    form: {
      type: Object,
      required: false,
      default: null
    },
    letters: {
      type: Array,
      required: true
    },
    getScore: {
      type: Boolean,
      required: false,
      default: false
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    return {
      activeName: '1',
      labelwidth: '110px',
      questionTypes: [
        {
          name: '单选题',
          value: 'SINGLE'
        },
        {
          name: '多选题',
          value: 'MULTIPLE'
        }
        // {
        //   name: '判断题',
        //   value: 'JUDGE'
        // },
        // {
        //   name: '填空题',
        //   value: 'COMPLETION'
        // }
      ],
      deleteIds: [],
      rules: {
        question: [
          {
            required: true,
            message: '请输入题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请添加问题'
          }
        ]
      }
    }
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      var form = this.form
      if (form) {
        if (form.list.length <= 0) {
          this.form.list.push({
            questionType: 'SINGLE',
            question: '',
            options: '',
            optionsArr: ['', '', '', ''],
            answer: '',
            answers: [],
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPREHENSIVE'],
            sortState: '0'
          })
          this.activeName = this.form.list.length + ''
        }
      }
    },
    typeChange(index) {
      var formitem = this.form.list[index]
      if (formitem.questionType == 'SINGLE') {
        formitem = Object.assign(
          {},
          {
            questionType: formitem.questionType,
            question: '',
            options: '',
            optionsArr: ['', '', '', ''],
            answer: '',
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPREHENSIVE']
          }
        )
      }
      if (formitem.questionType == 'MULTIPLE') {
        formitem = Object.assign(
          {},
          {
            questionType: formitem.questionType,
            question: '',
            options: '',
            optionsArr: ['', '', '', ''],
            answer: '',
            answers: [],
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPREHENSIVE']
          }
        )
      }
      if (formitem.questionType == 'JUDGE') {
        formitem = Object.assign(
          {},
          {
            questionType: formitem.questionType,
            question: '',
            options: '{"A":"正确","B":"<p>错误</p>"}',
            optionsArr: ['正确', '错误'],
            answer: '',
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPREHENSIVE']
          }
        )
      }
      if (formitem.questionType == 'COMPLETION') {
        formitem = Object.assign(
          {},
          {
            questionType: formitem.questionType,
            question: '',
            questionText: '',
            sortState: '0',
            answer: '',
            answers: [],
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPREHENSIVE'],
            sortState: '0'
          }
        )
      }
      if (formitem.questionType == 'SHORT') {
        formitem = Object.assign(
          {},
          {
            questionType: formitem.questionType,
            question: '',
            answer: '',
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPREHENSIVE']
          }
        )
      }
      this.$set(this.form.list, index, formitem)
    },
    handleTabsEdit(targetName, action) {
      if (action == 'add') {
        this.form.list.push({
          questionType: 'SINGLE',
          question: '',
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          analysis: '',
          scoreType: 1,
          score: this.socorRegular['COMPREHENSIVE'],
          sortState: '0'
        })
        this.activeName = this.form.list.length + ''
      }
      if (action == 'remove') {
        var tabs = this.form.list
        var activeName = this.activeName * 1
        if (activeName >= this.form.list.length) {
          activeName--
        }
        var index = targetName * 1 - 1
        this.activeName = activeName + ''
        let list = this.form.list
        var topicItem = list[index]
        if (topicItem && topicItem.questionExerciseAnswerId) {
          this.deleteIds.push(topicItem.questionExerciseAnswerId)
        }
        if (topicItem && topicItem.paperQuestionAnswerId) {
          this.deleteIds.push(topicItem.paperQuestionAnswerId)
        }
        list.splice(index, 1)
        this.$set(this.form, 'list', list)
      }
    },
    contentChange(data, label) {
      this.form[label] = data.html
      this.$nextTick(() => {
        this.$refs.theForm.clearValidate()
      })
    },
    beforeSubmit() {
      return new Promise((resolve, reject) => {
        this.$refs.theForm.validate((valid) => {
          if (valid) {
            var promises = []
            for (let i = 0; i < this.form.list.length; i++) {
              var promisesitem = new Promise((resolveitem, rejectitem) => {
                this.$refs.tcompatiblityitem[i]
                  .beforeSubmItem()
                  .then((data) => {
                    var onetopic = Object.assign({}, this.form.list[i], data)
                    resolveitem(onetopic)
                  })
                  .catch(() => {
                    rejectitem()
                  })
              })
              promises.push(promisesitem)
            }
            Promise.all(promises)
              .then((list) => {
                var thetopic = {
                  question: this.form.question,
                  deleteIds: this.deleteIds,
                  list: list
                }
                resolve(thetopic)
              })
              .catch(() => {
                this.$message({
                  type: 'error',
                  message: '小题内容输入不完整！'
                })
                reject()
              })
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>
