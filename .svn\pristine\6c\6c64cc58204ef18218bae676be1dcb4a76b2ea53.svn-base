<template>
  <div class="case-practise">
    <div class="case-practise_header">
      <div class="header_back" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <h3 class="header_title">病例训练记录</h3>
    </div>
    <div class="case-practise_body">
      <div class="body_search-container">
        <span class="caseName">病例名称:</span>
        <el-input v-model="queryInfo.caseName" size="small" maxlength="40" placeholder="请输入病例名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        <span class="searchButton" @click="getList">查询</span>
        <span class="resetButton" @click="reset">重置</span>
      </div>
      <ul class="body_list_container">
        <li v-for="item in list" :key="item.caseId" class="list_item">
          <div class="item_info">
            <Avatar :age="item.age" :sex="item.sex" :showSex="true" />
            <div class="info">
              <div class="info_name">{{ item.name }}</div>
              <div class="info_person">
                <span>{{ item.realName }}</span>
                <span>{{ item.age }}岁</span>
              </div>
            </div>
          </div>
          <div class="item_record-button" @click="record(item)">查看训练记录</div>
          <div class="item_stats">
            <div class="stats_item">
              <span>{{ parseInt(item.allTime / 60) }}分钟</span>
              <span>操作总用时</span>
            </div>
            <div class="stats_item">
              <span>{{ item.allCount }}</span>
              <span>操作总次数</span>
            </div>
          </div>
        </li>
      </ul>
      <div class="body_list_pagination" style="width: 100%; text-align: center">
        <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total,  prev, pager, next" :total="total"> </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { casePractiseList } from '@/api/casePractise'
import Avatar from '@/views/case/Avatar'

export default {
  name: '',
  components: {
    Avatar
  },
  data() {
    return {
      queryInfo: {
        caseName: null,
        pageNum: 1,
        pageSize: 8
      },
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await casePractiseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        caseName: null,
        pageNum: 1,
        pageSize: 8
      }
      this.getList()
    },
    record(item) {
      this.$router.push(`/casePractise/details/${item.caseId}/${item.name}`)
    }
  }
}
</script>
<style scoped lang="scss">
.case-practise {
  width: 100%;
  height: 100%;
  padding: 30px 50px 27px;
  background: linear-gradient(180deg, #d0dae4 0%, #f4f4f4 100%);
  .case-practise_header {
    position: relative;
    .header_back {
      position: absolute;
      left: 0;
      top: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .header_title {
      margin: 0;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      text-align: center;
    }
  }
  .case-practise_body {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 812px;
    margin-top: 18px;
    padding-top: 50px;
    background: linear-gradient(180deg, #26303c 0%, #0b1627 100%);
    border-radius: 30px 30px 30px 30px;
    .body_search-container {
      display: flex;
      align-items: center;
      .caseName {
        margin-right: 8px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #b1b1b1;
      }
      .searchButton {
        width: 94px;
        height: 45px;
        line-height: 45px;
        margin-left: 20px;
        background: #394555;
        border-radius: 63px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
      .resetButton {
        width: 94px;
        height: 45px;
        line-height: 45px;
        margin-left: 10px;
        border-radius: 63px;
        border: 1px solid #ffffff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
    }
    .body_list_container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      padding-left: 40px;
      margin-top: 70px;
      li {
        position: relative;
        width: 420px;
        margin-right: 20px;
        margin-bottom: 20px;
        border-radius: 20px;
        overflow: hidden;
        .item_info {
          display: flex;
          align-items: center;
          height: 120px;
          padding-left: 20px;
          background: #b1c8d6;
          ::v-deep {
            .Avatar {
              position: relative;
              width: 75px;
              height: 75px;
              background: #d9d9d9;
              border-radius: 46px 46px 46px 46px;
              img {
                width: 100%;
                height: 100%;
              }
              .sexIcon {
                position: absolute;
                right: 0px;
                bottom: 0;
                width: 24px;
                height: 24px;
              }
            }
          }
          .info {
            padding-left: 20px;
            .info_name {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 20px;
              color: #222d39;
            }
            .info_person {
              margin-top: 12px;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 14px;
              color: #4d5b68;
            }
          }
        }
        .item_record-button {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -30%);

          width: 146px;
          height: 40px;
          line-height: 40px;
          background: linear-gradient(180deg, #4d5f77 0%, #252f3c 100%);
          border-radius: 63px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
        }
        .item_stats {
          display: flex;
          justify-content: space-around;
          align-items: center;
          height: 100px;
          background: #65849a;
          .stats_item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            & > span:first-of-type {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 20px;
              color: #ffffff;
            }
            & > span:last-of-type {
              margin-top: 6px;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
            }
            &:last-of-type {
              position: relative;
              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 2px;
                height: 34px;
                background: rgba($color: #fff, $alpha: 0.5);
              }
            }
          }
        }
        &:nth-of-type(4n) {
          margin-right: 0;
        }
        &:nth-of-type(n + 5) {
          margin-bottom: 0;
        }
      }
    }
  }
}
.case-practise {
  ::v-deep {
    .case-practise_body {
      .body_search-container {
        .el-input {
          width: 300px;
          height: 45px;
          .el-input__inner {
            width: 100%;
            height: 100%;
            background: #394555;
            border: none;
            border-radius: 74px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #fff;
            &::placeholder {
              color: #a9a9a9;
            }
          }
          .el-input__suffix {
            .el-input__suffix-inner {
              .el-icon-circle-close {
                margin-right: 5px;
                margin-top: 2px;
                font-size: 24px;
              }
            }
          }
        }
      }

      .body_list_pagination {
        margin-top: 40px;
        .el-pagination.is-background .el-pager li:not(.disabled).active {
          background-color: #9ab0be;
          color: #394555;
        }
        .el-pagination.is-background .el-pager li {
          min-width: 40px;
          height: 40px;
          line-height: 40px;
          border-radius: 10px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          background-color: #394555;
          color: #fff;
        }
        .el-pagination.is-background .btn-prev,
        .el-pagination.is-background .btn-next {
          min-width: 40px;
          height: 40px;
          line-height: 40px;
          border-radius: 10px;
          background-color: #394555;
          color: #fff;
          font-size: 16px;
        }
        .el-pagination.is-background .btn-prev:disabled {
          color: rgba(255, 255, 255, 0.3);
        }
        .el-pagination__total {
          height: 40px;
          line-height: 40px;
          color: #fff;
          font-size: 15px;
        }
      }
    }
  }
}
</style>
