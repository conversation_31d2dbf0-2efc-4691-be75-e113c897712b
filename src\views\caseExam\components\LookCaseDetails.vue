<template>
  <div class="lookCaseDetails">
    <el-dialog :visible="showDialog" center custom-class="lookCaseDetailsDialog" top="5px" @close="close">
      <div class="top">
        <span>病史采集问题</span>
        <span>病例详情</span>
      </div>
      <div class="content" v-if="info">
        <div class="left">
          <div class="tabBarContent">
            <DialogueItem :list="info.questions" :listType="'0'" />
          </div>
        </div>
        <div class="right">
          <Avatar :age="info.age" :sex="info.sex" />
          <div class="name">{{ info.realName }}</div>
          <div class="age">{{ info.age }}岁</div>
          <div class="caseDescribe">
            <span>{{ info.name }}</span>
            <span style="white-space: pre-wrap">{{ info.mainDemands }}</span>
          </div>
          <div class="caseStatistics">
            <div class="score">
              <span>{{ info.allScore }}</span>
              <span>病例总分</span>
            </div>
            <div class="questionNum">
              <span>{{ info.questionCount }}</span>
              <span>问题数量</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseExamSelectExamCaseDetailById } from '@/api/caseExam'
import { caseDetail } from '@/api/case'
import Avatar from '@/views/casePractise/components/Avatar'
import DialogueItem from '@/views/casePractise/components/DialogueItem'

export default {
  name: 'LookCaseDetails',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    Avatar,
    DialogueItem
  },
  data() {
    return {
      info: null,
      questionType: '1',
      level1List: [],
      level2List: [],
      level3List: []
    }
  },
  computed: {
    allQuestionLabel() {
      return `全部问题(${this.info.questionCount}个/${this.info.allScore}分)`
    },
    ImportantQuestionLabel() {
      let num = 0
      let score = 0
      const level1 = this.info.questions.filter((item) => {
        return item.level === 1
      })
      num = level1.length
      score = level1.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
      }, 0)
      return `重要问题(${num}个/${score}分)`
    },
    routineQuestionLabel() {
      let num = 0
      let score = 0
      const level2 = this.info.questions.filter((item) => {
        return item.level === 2
      })
      num = level2.length
      score = level2.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
      }, 0)
      return `常规问题(${num}个/${score}分)`
    },
    invalidQuestionLabel() {
      let num = 0
      const level3 = this.info.questions.filter((item) => {
        return item.level === 3
      })
      num = level3.length
      return `无效问题(${num}个)`
    }
  },
  created() {},
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    async getCaseDetails(item, type) {
      console.log(item)
      if (type === 'examDetails') {
        const { data } = await caseExamSelectExamCaseDetailById({ examCaseId: item.caseId })
        this.info = data
      } else {
        const { data } = await caseDetail({ id: item.caseId })
        this.info = data
      }
      if (this.info.questions.length) {
        const level1 = this.info.questions.filter((item) => {
          return item.level === 1
        })
        const level2 = this.info.questions.filter((item) => {
          return item.level === 2
        })
        const level3 = this.info.questions.filter((item) => {
          return item.level === 3
        })
        this.level1List = level1
        this.level2List = level2
        this.level3List = level3
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .lookCaseDetailsDialog {
    width: 1507px;
    height: 852px;
    background: #ffffff;
    border-radius: 30px 30px 30px 30px;
    .el-dialog__header {
      padding: 0;
      .el-dialog__headerbtn {
        top: 24px;
        right: 24px;
        .el-dialog__close {
          font-size: 26px;
          height: 26px;
          width: 26px;
          font-weight: 600;
          color: #666666;
        }
      }
    }
    .el-dialog__body {
      padding: 30px;
      .top {
        display: flex;
        margin-bottom: 40px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
        text-align: center;
        & > span:first-child {
          width: 754px;
          background: #ffffff;
        }
        & > span:last-of-type {
          flex: 1;
        }
      }
      .content {
        display: flex;
        justify-content: space-between;
        height: 716px;
        background: #ffffff;
        .left {
          width: 940px;
          height: 100%;
          .tabBarContent {
            width: 100%;
            height: 100%;
            margin-top: -1px;
            background: #ffffff;
            border-right: 1px solid #e4e4e4;
            border-bottom: 0;
            overflow: auto;
            /* 定义滚动条样式 */
            &::-webkit-scrollbar {
              width: 4px; /* 滚动条宽度 */
              background-color: #fff; /* 滚动条背景色 */
            }

            /* 定义滚动条轨道样式 */
            &::-webkit-scrollbar-track {
              border-radius: 4px; /* 滚动条轨道的弧形形状 */
            }

            /* 定义滚动条滑块样式 */
            &::-webkit-scrollbar-thumb {
              background-color: #e8e8e8; /* 滚动条滑块颜色 */
              border-radius: 4px; /* 滚动条滑块的弧形形状 */
            }

            /* 定义滚动条滑块在悬停状态时的样式 */
            &::-webkit-scrollbar-thumb:hover {
              background-color: #888787; /* 滚动条滑块悬停状态的颜色 */
            }
          }
        }
        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-top: 30px;
          height: 100%;
          .Avatar {
            width: 100px;
            height: 100px;
            & > div > img {
              width: 100px;
              height: 100px;
            }
            & > div > .sexIcon {
              right: -2px;
              bottom: 2px;
              width: 30px;
              height: 30px;
            }
          }
          .name {
            margin-top: 18px;
            font-size: 24px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #333333;
          }
          .age {
            margin-top: 9px;
            font-size: 20px;
            font-family: PingFang SC;
            color: #666;
          }
          .caseDescribe {
            display: flex;
            flex-direction: column;
            margin-top: 8px;
            margin-bottom: 47px;
            padding: 20px;
            width: 95%;
            height: 270px;
            background: #f8f8f9;
            overflow: auto;
            border-radius: 10px;
            &::-webkit-scrollbar {
              width: 6px;
              background: transparent;
            }
            &::-webkit-scrollbar-thumb {
              background: #81909a;
              border-radius: 40px;
            }
            & > span:first-of-type {
              margin-bottom: 20px;
              font-family: PingFang SC;
              font-weight: 600;
              font-size: 20px;
              color: #333333;
            }
            & > span:last-of-type {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 16px;
              color: #333333;
              line-height: 22px;
              text-align: left;
            }
          }
          .caseStatistics {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 95%;

            .score,
            .questionNum {
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 250px;
              height: 146px;
              background: #f8f8f9;
              border-radius: 20px 20px 20px 20px;
              & > span:first-of-type {
                font-size: 34px;
                font-family: PingFang SC;
                font-weight: bold;
                color: #3082bd;
              }
              & > span:last-of-type {
                margin-top: 16px;
                font-size: 18px;
                font-family: PingFang SC;
                font-weight: 500;
                color: #666666;
              }
            }
            .questionNum {
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
