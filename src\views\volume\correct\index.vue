<template>
  <div class="correct-page">
    <div class="correct__search">
      <div class="search-field">
        <span class="search-field__label">考试名称：</span>
        <el-input class="search-field__input" placeholder="请输入考试名称" width="30px" v-model="paperName" clearable />
      </div>
      <div class="search-field search-field--date">
        <span class="search-field__label">考试时间：</span>
        <el-date-picker class="search-field__input" v-model="examineTimes" @change="examineTimesChange" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" clearabled></el-date-picker>
      </div>
      <div class="search-field--action">
        <el-button type="primary" @click="currentChange(1)" size="medium">查询</el-button>
        <el-button plain size="medium" @click="research">重置</el-button>
      </div>
      <!-- <span class="search_label">创建人：</span> -->
      <!-- <el-input placeholder="请输入创建人" width="30px" v-model="createUserName" style="width: 200px; margin-left: 5px" clearable /> -->
      <!-- <span class="search_label">批改状态：</span>
      <el-select v-model="correctState" placeholder="请选择批改状态" style="width: 200px" clearabled>
        <el-option v-for="item in correctStates" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select> -->
      <!-- <span class="search_label">创建时间：</span>
      <el-date-picker v-model="createTimes" @change="createTimesChange" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" clearabled></el-date-picker> -->
    </div>
    <div class="correct__table">
      <el-table :data="exams" cell-class-name="tableCellClassName" header-cell-class-name="tableHeaderClassName">
        <el-table-column prop="paperName" align="center" width="350" show-overflow-tooltip label="考试名称"> </el-table-column>
        <el-table-column prop="clbums" align="center" label="下发班级" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.clbums">
              <span>{{ showClbum(scope.row.clbums) }}</span>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="" align="center" width="440" label="考试时间">
          <template slot-scope="scope"> {{ scope.row.startTime }} ~ {{ scope.row.endTime }} </template>
        </el-table-column>
        <el-table-column prop="totalCount" align="center" width="110" label="总人数"> </el-table-column>
        <el-table-column prop="totalScore" align="center" width="110" label="总分数"> </el-table-column>
        <el-table-column prop="passScore" align="center" width="110" label="及格分"> </el-table-column>
        <!-- <el-table-column prop="waitCorrectCount" align="center" label="待批改"> </el-table-column> -->

        <!-- <el-table-column prop="correctState" align="center" label="批改状态">
          <template slot-scope="scope">
            <span v-for="item in correctStates" v-if="scope.row.correctState == item.value">{{ item.label }}</span>
          </template>
        </el-table-column> -->

        <!-- <el-table-column prop="createUserName" align="center" label="创建人"> </el-table-column> -->
        <!-- <el-table-column prop="createTime" align="center" label="创建时间"> </el-table-column> -->
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <div class="button-group">
              <span v-if="getInTime(scope.row)">考试未结束</span>
              <el-button v-else type="primary" @click="openViewInfo(scope.row)" size="small">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="correct__pagination">
      <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-size="pageSize" layout="total,  prev, pager, next" :total="total"> </el-pagination>
    </div>
  </div>
</template>

<script>
import { paperCorrectList } from '@/api/correct.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    return {
      userinfo: {},
      paperName: '',
      createUserName: '',
      createTimes: [],
      startCreateTime: '',
      endCreateTime: '',
      examineTimes: [],
      startExamineTime: '',
      endExamineTime: '',
      correctState: null,
      pageNum: 1,
      pageSize: 8,
      total: 0,
      exams: [],
      correctStates: [
        {
          label: '全部',
          value: null
        },
        {
          label: '未完成',
          value: 2
        },
        {
          label: '已完成',
          value: 1
        }
      ]
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    showClbum() {
      return (val) => {
        const clbums = val.map((item) => item.clbumName)
        return clbums.join('/')
      }
    }
  },
  methods: {
    getInTime(item) {
      var nowDate = new Date()
      var endTime = new Date(item.endTime)
      if (nowDate < endTime) {
        return true
      } else {
        return false
      }
    },
    createTimesChange() {
      if (this.createTimes && this.createTimes.length == 2) {
        this.startCreateTime = this.createTimes[0]
        this.endCreateTime = this.createTimes[1]
      } else {
        this.startCreateTime = ''
        this.endCreateTime = ''
      }
    },
    research() {
      this.paperName = ''
      this.pageNum = 1
      this.examineTimes = []
      this.startExamineTime = ''
      this.endExamineTime = ''
      this.getGroups()
    },
    examineTimesChange() {
      if (this.examineTimes && this.examineTimes.length == 2) {
        this.startExamineTime = this.examineTimes[0]
        this.endExamineTime = this.examineTimes[1]
      } else {
        this.startExamineTime = ''
        this.endExamineTime = ''
      }
      this.getGroups()
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        paperName: this.paperName,
        createUserName: this.createUserName,
        startCreateTime: this.startCreateTime,
        endCreateTime: this.endCreateTime,
        startExamineTime: this.startExamineTime,
        endExamineTime: this.endExamineTime,
        correctState: this.correctState,
        schoolId: this.userinfo.schoolId
      }
      paperCorrectList(data).then(async (res) => {
        this.exams = res.data.list
        this.total = res.data.total
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.getGroups()
      })
    },
    openViewInfo(item) {
      this.$router.replace({
        name: 'correctlist',
        query: {
          paperId: item.paperId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.correct-page {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 25px;
  .correct__search {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    .search-field {
      display: flex;
      align-items: center;
      justify-content: center;

      &__label {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #666666;
      }
      &__input {
        width: 300px;
        height: 45px;
        ::v-deep {
          .el-input__inner {
            width: 100%;
            height: 100%;
            border-radius: 74px;
            background: #eef0f2;
            border: none;
            color: #333333;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            &::placeholder {
              color: #cccccc;
            }
          }
          .el-icon-circle-close {
            margin-right: 5px;
            margin-top: 2px;
            font-size: 20px;
          }
        }
      }

      &--date {
        margin-left: 20px;
        ::v-deep {
          .el-date-editor {
            width: 420px;
            height: 45px;
            background: #eef0f2;
            border-radius: 74px 74px 74px 74px;
            border: none;
            .el-icon-date {
              font-size: 20px;
              margin-left: 20px;
              margin-top: 5px;
            }
            .el-range-separator {
              margin-top: 12px;
            }
            .el-range-input {
              background: transparent;
              font-size: 20px;
            }
            .el-range__close-icon {
              margin-right: 5px;
              margin-top: 5px;
              font-size: 20px;
            }
          }
        }
      }
      &--action {
        display: flex;
        align-items: center;
        margin-left: 20px;
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 45px;
          padding: 0 18px;
          margin-right: 20px;
          background: #65849a;
          border-radius: 63px 63px 63px 63px;
          border: none;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          &:first-of-type {
            margin-right: 10px;
          }
          &--primary {
            background: #65849a;
            border-color: #65849a;
            color: #fff;
          }
          &.is-plain {
            background: #fff;
            border: 1px solid #65849a;
            color: #65849a;
          }
        }
      }
    }
  }
  .correct__table {
    .el-table {
      &::before {
        display: none;
      }
      ::v-deep {
        .tableCellClassName {
          height: 55px;
          background: #f6f8fa;
          border-bottom: 4px solid #fff;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #333333;
          &:first-of-type {
            border-radius: 8px 0 0 8px;
          }
          &:last-of-type {
            border-radius: 0 8px 8px 0;
          }
        }
        .tableHeaderClassName {
          height: 55px;
          border-bottom: 4px solid #fff;
          background: #f6f8fa;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #999999;
          &:first-of-type {
            border-radius: 8px 0 0 8px;
          }
          &:nth-of-type(7) {
            border-radius: 0 8px 8px 0;
          }
        }

        .button-group {
          display: flex;
          align-items: center;
          justify-content: space-around;
        }
        .el-tag {
          position: relative;
          left: -5px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 66px;
          height: 38px;
          padding: 0;
          border-radius: 8px 8px 8px 8px;

          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          &.el-tag--success {
            border-color: #c2dfc6;
            background: #e2efe7;
            color: #33a141;
          }
          &.el-tag--danger {
            border-color: #ffd3d3;
            background: #f3e7e9;
            color: #dd4b4b;
          }
        }
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;

          // min-width: 78px;
          // height: 38px;
          padding: 10px;
          margin: 0;
          background: #ffffff;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #e2e2e2;

          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          i {
            font-size: 16px;
            margin-right: 4px;
          }
          span {
            margin-left: 0;
          }

          &--primary {
            color: #3381b9;
          }
        }
      }
    }
  }
  .correct__pagination {
    position: absolute;
    left: 50%;
    bottom: 30px;
    transform: translateX(-50%);
  }
}
</style>
