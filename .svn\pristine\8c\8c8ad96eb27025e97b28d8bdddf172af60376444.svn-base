<template>
  <el-row :gutter="20">
    <el-col :span="4">
      <div class="user_left">
        <div class="user_lable">选择专业</div>
        <div class="user_leftitem" v-for="(item, index) in majors" :class="{ active: item.majorId == majorId }" @click="selectItem(item)">
          {{ item.majorName }}
        </div>
      </div>
    </el-col>
    <el-col :span="20">
      <div style="margin: 20px">
        <span class="search_label">题干名称：</span>
        <el-input placeholder="请输入题干名称" v-model="question" style="width: 200px; margin-left: 5px" clearable />
        <span class="search_label">题目类型：</span>
        <el-select v-model="questionType" placeholder="请选择题目类型" @change="handleCurrentChange(1)" clearable>
          <el-option v-for="item in questionTypes" :key="item.value" :label="item.name" :value="item.value"> </el-option>
        </el-select>
        <el-button type="primary" @click="handleCurrentChange(1)" style="margin-left: 5px">搜索</el-button>
        <el-button type="primary" @click="openAdd" style="margin-left: 15px">添加题目</el-button>
        <el-button type="success" @click="dialogImport = true" icon="el-icon-upload2">批量导入</el-button>
        <el-button type="success" @click="exportTopics" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出题目' }}</el-button>
        <div style="margin: 10px">
          <el-table :data="topics" border row-key="questionId" @selection-change="selectionChange">
            <el-table-column type="selection" width="55" align="center" :selectable="selectableItem"> </el-table-column>
            <el-table-column prop="question" label="题干" width="320">
              <template slot-scope="scope">
                <span v-if="scope.row.questionType != 'COMPATIBILITY'">
                  <span class="editor_box" v-html="scope.row.question"></span>
                </span>
                <div v-else>
                  <div class="list_question" v-for="(item, index) in getQuestion(scope.row)">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="questionType" label="题型" align="center">
              <template slot-scope="scope">
                <span v-for="item in questionTypes" v-if="item.value == scope.row.questionType">{{ item.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="difficulty" label="难度" align="center">
              <template slot-scope="scope">
                <span v-for="item in difficultys" v-if="item.value == scope.row.difficulty">{{ item.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="majorName" label="所属专业" align="center"> </el-table-column>
            <el-table-column prop="disabled" label="状态" align="center">
              <template slot-scope="scope">
                <el-tag type="danger" v-if="scope.row.disabled == '0'">禁用</el-tag>
                <el-tag type="success" v-if="scope.row.disabled == '1'">正常</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center"> </el-table-column>
            <el-table-column prop="createUserName" label="创建人" align="center"> </el-table-column>
            <el-table-column label="操作" align="center" width="350">
              <template slot-scope="scope">
                <el-button :type="scope.row.disabled == 1 ? 'info' : 'success'" @click="openState(scope.row)" size="small">{{ scope.row.disabled == 1 ? '禁用' : '启用' }}</el-button>
                <el-button type="warning" @click="openView(scope.row)" size="small">查看</el-button>
                <el-button type="primary" @click="openEdit(scope.row)" size="small">编辑</el-button>
                <el-button type="danger" @click="openDelete(scope.row)" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin: 10px; text-align: center">
            <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
          </div>
        </div>
      </div>
    </el-col>
    <el-dialog title="批量导入" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="dialogImport" width="720px">
      <el-form :model="importUserForm" :rules="importUserRules" ref="importUserValidate" :inline="true">
        <el-form-item label="所属专业" prop="majorId" label-width="110px">
          <el-select v-model="importUserForm.majorId" placeholder="请选择所属专业" style="width: 207px">
            <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件" prop="excelFile" label-width="110px">
          <div style="height: 80px">
            <p style="margin: auto 0; text-align: center">请下载模板，并严格按照模板格式整理数据后上传；只能上传xls/xlsx格式文件</p>
            <p style="margin: auto 0; text-align: center; color: red">*注意：请勿重复导入相同数据</p>
          </div>
          <el-upload style="text-align: center" ref="upload" action="" :limit="1" :on-change="fileChange" :before-remove="fileRemove" accept=".doc,.docx,.xls,.xlsx" :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin: 0 0 0 20px" size="small" plain @click="downloadTemplate(1)">下载Word模板</el-button>
            <el-button style="margin: 0 0 0 20px" size="small" plain @click="downloadTemplate(2)">下载Excel模板</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetImport">取 消</el-button>
        <el-button type="success" @click="submitUpload">导入</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="getType(form)" class="mini_dialog" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="questionView" width="600px">
      <questionview :form="form" :letters="letters" :difficultys="difficultys" v-if="questionView"></questionview>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="questionView = false">确定</el-button>
      </div>
    </el-dialog>
  </el-row>
</template>

<script>
import { questionList, selectQuestionDetail, saveQuestion, updateQuestion, removeQuestion, updateState, importWordQuestion, importQuestion, questionExport } from '@/api/question.js'
import { selectTeacherById } from '@/api/teacher.js'
import { putProgress } from '@/utils/oss.js'
import questionview from '@/views/question/view/index'
import { questionTypes } from '@/filters'

export default {
  components: {
    questionview
  },
  data() {
    return {
      userinfo: {},
      majors: [],
      majorId: '',
      questionType: '',
      question: '',
      questionUse: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      topics: [],
      dialogImport: false,
      questionView: false,
      form: {},
      multipleSelect: [],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,

      importUserForm: {
        questionUse: 0,
        majorId: '',
        excelFile: ''
      },
      importUserRules: {
        excelFile: [
          {
            required: true,
            message: '请选择导入文件'
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    selectableItem(row, index) {
      if (row.questionType == 'COMPATIBILITY' || row.questionType == 'COMPREHENSIVE') {
        return false
      } else {
        return true
      }
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    getType(form) {
      var questionTypes = this.questionTypes
      var questionTypeName = ''
      if (form && form.questionType) {
        questionTypes.map((item) => {
          if (form.questionType == item.value) {
            questionTypeName = item.name
          }
        })
      }
      return questionTypeName || '题目详情'
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (res.data.majors && res.data.majors.length > 0) {
          this.majorId = res.data.majors[0].majorId
          this.getList()
        }
      })
    },
    selectItem(item) {
      if (this.majorId != item.majorId) {
        this.majorId = item.majorId
        this.research()
      }
    },
    research() {
      this.questionType = ''
      this.question = ''
      this.questionUse = ''
      this.pageNum = 1
      this.getList()
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getList()
    },
    getList() {
      var data = {
        majorId: this.majorId,
        questionUse: this.questionUse,
        question: this.question ? this.question : null,
        questionType: this.questionType ? this.questionType : null,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    //导入试题
    fileChange(file) {
      this.importUserForm.excelFile = file.raw
    },
    fileRemove(file) {
      this.importUserForm.excelFile = ''
    },
    resetImport() {
      this.dialogImport = false
      this.importUserForm.excelFile = ''
      this.$refs.upload.clearFiles()
      this.$refs.importUserValidate.clearValidate()
      this.$refs.importUserValidate.resetFields()
    },
    submitUpload() {
      this.$refs.importUserValidate.validate((valid) => {
        if (valid) {
          if (this.importUserForm.excelFile == '') {
            this.$message({
              message: '文件不能为空',
              type: 'error'
            })
            return
          } else {
            const loading = this.$loading({
              lock: true,
              text: '正在导入请稍等···',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            var formData = new FormData()
            var filename = this.importUserForm.excelFile.name
            var filetype = filename.substring(filename.lastIndexOf('.') + 1)
            formData.append('file', this.importUserForm.excelFile)
            formData.append('questionUse', this.importUserForm.questionUse)
            formData.append('majorId', this.importUserForm.majorId)
            formData.append('schoolId', this.userinfo.schoolId)
            if (filetype == 'doc' || filetype == 'docx') {
              importWordQuestion(formData).then(async (res) => {
                if (res.code == '200') {
                  this.$message({
                    message: '导入成功!',
                    type: 'success'
                  })
                } else {
                  this.$message({
                    message: res.message,
                    type: 'error'
                  })
                }
                loading.close()
                this.resetImport()
                this.getList()
              })
            }
            if (filetype == 'xls' || filetype == 'xlsx') {
              importQuestion(formData).then(async (res) => {
                if (res.code == '200') {
                  this.$message({
                    message: '导入成功!',
                    type: 'success'
                  })
                } else {
                  this.$message({
                    message: res.message,
                    type: 'error'
                  })
                }
                loading.close()
                this.resetImport()
                this.getList()
              })
            }
          }
        }
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    exportTopics() {
      var multipleSelect = this.multipleSelect
      var questionIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          questionIds.push(item.questionId)
        })
      }
      questionExport({
        questionIds: questionIds && questionIds.length > 0 ? questionIds : null,
        majorId: this.majorId
      })
    },
    downloadTemplate(type) {
      if (type == 1) {
        var download = document.createElement('a')
        download.href = this.baseurl + 'zxks/templates/question.docx'
        $('body').append(download)
        download.click()
      }
      if (type == 2) {
        var download = document.createElement('a')
        download.href = this.baseurl + 'zxks/templates/question.xls'
        $('body').append(download)
        download.click()
      }
    },
    openView(item) {
      selectQuestionDetail({
        questionId: item.questionId
      }).then((res) => {
        this.form = Object.assign({}, item, res.data)
        this.questionView = true
      })
    },
    openAdd() {
      this.$router.replace({
        name: 'questionadd'
      })
    },
    openEdit(item) {
      this.$router.replace({
        name: 'questionadd',
        query: {
          questionId: item.questionId
        }
      })
    },
    openState(item) {
      var message = item.disabled == '1' ? '是否禁用该试题?' : '是否启用该试题?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          updateState({
            questionId: item.questionId,
            state: item.disabled == '1' ? '0' : '1'
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.getList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    openDelete(item) {
      this.$confirm('此操作将永久删除此试题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeQuestion({
            questionId: item.questionId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>
