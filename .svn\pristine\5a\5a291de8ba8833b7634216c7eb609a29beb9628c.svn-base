<template>
  <div>
    <el-dialog title="选择班级学生" :visible="selectStudentDialog" width="width" custom-class="selectStudentDialog" @close="close">
      <el-card>
        <div slot="header">班级列表</div>
        <el-tree ref="classTree" show-checkbox default-expand-all class="tree" node-key="id" :data="classList" :props="props" check-on-click-node highlight-current @check="treeCheck"> </el-tree>
      </el-card>
      <el-card>
        <div slot="header">已选择</div>
        <div v-for="item in checkStudents" :key="item.id">
          <div class="checkedClass">{{ item.className }}</div>
          <ul class="students">
            <li v-for="li in item.children" :key="li.id">
              <el-tag type="success" closable @close="tagClose(li, item)">{{ li.name }}</el-tag>
            </li>
          </ul>
        </div>
      </el-card>
      <div slot="footer">
        <el-button type="primary" @click="confirm">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { systemClbumAll } from '@/api/clbum'
import { selectTeacherById } from '@/api/teacher.js'

export default {
  name: 'SelectStudentDialog',
  props: {
    selectStudentDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      classList: [],
      props: {
        label: 'name',
        children: 'children'
      },
      checkStudents: [], // 被选中的学生及班级
      checkIds: [] // 被选中的班级及学生的id
    }
  },
  created() {},
  methods: {
    getClassAll() {
      selectTeacherById().then(async (res) => {
        const { data } = await systemClbumAll({ schoolId: res.data.schoolId, clbumIds: res.data.clbums.map((item) => item.id) })
        this.classList = data
        this.$nextTick(() => {
          this.$refs['classTree'].setCheckedKeys(this.checkIds)
        })
      })
    },
    treeCheck(val, { checkedNodes, checkedKeys }) {
      if (val.type === 1) {
        const isRepeat = this.checkStudents.some((item) => item.className === val.name)
        if (isRepeat) {
          const repeatIndex = this.checkStudents.findIndex((item) => item.className == val.name)
          // 出现这种情况就得覆盖数据，而不是删除
          const isCover = checkedNodes.some((item) => item.id == this.checkStudents[repeatIndex].classId)
          if (isCover) {
            this.$set(this.checkStudents[repeatIndex], 'className', val.name)
            this.$set(this.checkStudents[repeatIndex], 'classId', val.id)
            this.$set(this.checkStudents[repeatIndex], 'children', JSON.parse(JSON.stringify(val.children)))
          } else {
            this.checkStudents.splice(repeatIndex, 1)
          }
        } else {
          this.checkStudents.push({
            className: val.name,
            classId: val.id,
            children: JSON.parse(JSON.stringify(val.children))
          })
        }
      } else {
        /**
         * 1.如果选择的是学生，先去查他的班级是否已被选中了
         * 2.如果选中了再去看当前选择的学生的是否也存在被选中的班级中
         * 3.如果存在就给他删了,如果不存在就加上,如果删了后该班级已经没有人了，则需要将该班级也删除
         * */
        const existClassIndex = this.checkStudents.findIndex((item) => item.classId == val.parentId)
        if (existClassIndex !== -1) {
          const existStudentIndex = this.checkStudents[existClassIndex].children.findIndex((item) => item.id === val.id)
          if (existStudentIndex !== -1) {
            this.checkStudents[existClassIndex].children.splice(existStudentIndex, 1)
            // 判断当前班级是否还有选中的学生，如果没有就把班级也删了
            if (!this.checkStudents[existClassIndex].children.length) {
              this.checkStudents.splice(existClassIndex, 1)
            }
          } else {
            this.checkStudents[existClassIndex].children.push({ ...val })
          }
        } else {
          // 如果本身没有班级，则需要创建一个班级再将学生加入到该班级中
          const parentClass = this.classList.find((item) => item.id === val.parentId)
          this.checkStudents.push(
            JSON.parse(
              JSON.stringify({
                className: parentClass.name,
                classId: parentClass.id,
                children: [{ ...val }]
              })
            )
          )
        }
        // console.log(val)
      }
      this.checkIds = checkedKeys
    },
    tagClose(li, data) {
      // 删除左侧属性选中的数据
      this.checkIds = this.checkIds.filter((item) => item !== li.id)
      const checkedNodeKeys = this.$refs['classTree'].getCheckedKeys(true).filter((item) => item !== li.id)
      this.$refs['classTree'].setCheckedKeys(checkedNodeKeys)
      // 删除右侧选中的数据
      const index = data.children.findIndex((element) => element.id === li.id)
      data.children.splice(index, 1)
      if (!data.children.length) {
        const parentIndex = this.checkStudents.findIndex((item) => item.classId === data.classId)
        this.checkStudents.splice(parentIndex, 1)
        this.$emit('success', { checkedList: this.checkStudents, checkIds: this.$refs['classTree'].getCheckedKeys(true) })
      }
    },
    TagClose(li, data) {
      this.checkIds = this.checkIds.filter((item) => item !== li.id)
      // 删除右侧选中的数据
      const index = data.children.findIndex((element) => element.id === li.id)
      data.children.splice(index, 1)
      this.$emit('success', { checkedList: this.checkStudents, checkIds: this.checkIds })
    },
    close() {
      this.$emit('update:selectStudentDialog', false)
    },
    confirm() {
      if (!this.checkStudents.length) return this.$message.warning('请选择学生')
      this.$emit('success', { checkedList: this.checkStudents, checkIds: this.$refs['classTree'].getCheckedKeys(true) })
      this.close()
    },
    // 修改考核时回显处用
    showData(data) {
      const parentIds = data.clbumIds.split(',')
      const studentList = []
      parentIds.forEach((item) => {
        studentList.push({
          classId: parseInt(item),
          className: null,
          children: []
        })
      })
      studentList.forEach((li) => {
        data.students.forEach((item) => {
          if (li.classId === item.clbum_id) {
            li.className = item.clbum_name
            li.children.push({
              id: item.student_id,
              name: item.name
            })
          }
        })
      })
      this.checkStudents = studentList
      this.checkIds = data.students.map((item) => item.student_id)
      this.$nextTick(() => {
        this.$emit('success', { checkedList: this.checkStudents, checkIds: this.checkIds })
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .selectStudentDialog {
    .el-dialog__body {
      display: flex;
      justify-content: space-between;
      flex: 5;
      .el-card {
        height: 500px;
        .el-card__body {
          overflow: auto;
          &::-webkit-scrollbar {
            width: 3px;
          }

          // 里面的滑块
          &::-webkit-scrollbar-thumb {
            background: #d2d2d2;
          }

          // 外面的背景
          &::-webkit-scrollbar-track-piece {
            background: transparent;
          }
        }
        .el-card__header,
        .el-checkbox__label {
          font-size: 16px;
          color: #1a1a1a;
        }
        &:first-of-type {
          flex: 2;
          margin-right: 15px;
          .el-card__body {
            padding: 10px;
            padding-top: 18px;
            max-height: 500px;
            overflow: auto;
            &::-webkit-scrollbar {
              width: 3px;
            }
            // 里面的滑块
            &::-webkit-scrollbar-thumb {
              background: #d2d2d2;
            }
            // 外面的背景
            &::-webkit-scrollbar-track-piece {
              background: transparent;
            }
          }
        }
        &:last-of-type {
          flex: 3;
          .el-card__body {
            padding: 10px;
            max-height: 500px;
            overflow: auto;
            &::-webkit-scrollbar {
              width: 3px;
            }
            // 里面的滑块
            &::-webkit-scrollbar-thumb {
              background: #d2d2d2;
            }
            // 外面的背景
            &::-webkit-scrollbar-track-piece {
              background: transparent;
            }
            .el-row {
              display: flex;
              align-items: center;
              // margin: 15px 0;
              height: 35px;
            }
          }
        }
      }

      .checkedClass {
        padding-left: 10px;
        margin-top: 15px;
        font-size: 18px;
        font-weight: bold;
      }
      .students {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        padding-left: 10px;
        margin: 0;
        margin-top: 10px;
        list-style: none;
        li {
          margin-right: 10px;
          .el-tag {
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}
</style>
