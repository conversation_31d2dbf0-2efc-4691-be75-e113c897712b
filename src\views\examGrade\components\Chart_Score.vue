<template>
  <div class="Chart_Score" ref="ChartScore"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
export default {
  name: 'ChartScore',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  created() {},
  methods: {
    init(data) {
      // 转换数据为 ECharts 需要的格式
      var clbumNames = data.map(function (item) {
        return item.clbumName
      })
      var maxScores = data.map(function (item) {
        return item.max
      })
      var minScores = data.map(function (item) {
        return item.min
      })

      // ECharts 折线图配置
      var option = {
        title: {
          text: '班级最高分、最低分统计',
          left: '29px',
          top: '20px',
          textStyle: {
            color: '#293543',
            fontSize: 26,
            fontFamily: 'PingFang SC',
            fontWeight: 400
          }
        },
        legend: {
          top: '5%', // 距离容器上边界5%
          right: '10%' // 距离容器右边界10%
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // 提示框背景颜色为黑色的50%透明度
          borderColor: 'transparent',
          borderRadius: 10,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 16
          },
          formatter: function (params) {
            return params[0].name + '<br/>最高分：' + params[0].value + '<br/>最低分：' + params[1].value
          },
          axisPointer: {
            type: 'line'
          }
        },
        xAxis: {
          type: 'category',
          data: clbumNames,
          axisTick: {
            show: false // 刻度标去掉
          },
          axisLabel: {
            color: '#999999',
            fontSize: 16,
            margin: 10 // x轴的文字距离图形的距离
          },
          axisLine: {
            lineStyle: {
              color: '#E3E3E3'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '分', // y轴顶部加一个文字“分”
          nameTextStyle: {
            color: '#999999',
            fontSize: 16,
            padding: [0, 30, 10, 0] // 调整文字位置
          },
          axisLabel: {
            color: '#999999',
            fontSize: 16
          }
        },
        grid: {
          left: '120px',
          right: '100px',
          top: '120px',
          bottom: '40px' // 调整网格距离底部的距离
          // 额外配置，确保图形显示完整
        },
        series: [
          {
            name: '最高分',
            type: 'line',
            data: maxScores,
            itemStyle: {
              color: '#1890ff'
            },
            symbol: 'circle',
            symbolSize: 10,
            showSymbol: true,
            lineStyle: {
              width: 2,
              color: '#1890ff',
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(24, 144, 255, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(24, 144, 255, 0.0)'
                }
              ])
            }
          },
          {
            name: '最低分',
            type: 'line',
            data: minScores,
            itemStyle: {
              color: '#24cdc2'
            },
            symbol: 'circle',
            symbolSize: 10,
            showSymbol: true,
            lineStyle: {
              width: 2,
              color: '#24cdc2',
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(36, 205, 194, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(36, 205, 194, 0)'
                }
              ])
            }
          }
        ]
      }

      // 基于准备好的dom，初始化echarts实例并应用配置
      this.chart = echarts.init(this.$refs['ChartScore'])
      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.Chart_Score {
  width: 100%;
  height: 100%;
}
</style>
