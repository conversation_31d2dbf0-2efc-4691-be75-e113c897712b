<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="addDialog" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px">
        <el-form-item label="类型名称:" prop="name">
          <el-input v-model="form.name" placeholder="请输入类型名称" maxlength="40" clearable></el-input>
        </el-form-item>
        <el-form-item label="说明:">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入说明" maxlength="40" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseQuestionTypeAdd, caseQuestionTypeUpdate } from '@/api/caseQuestionType'
export default {
  name: '',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      form: { name: null, remark: null },
      rules: {
        name: [{ required: true, message: '请输入问诊类型名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.form.typeId ? '编辑问诊类型' : '添加问诊类型'
    }
  },
  created() {},
  methods: {
    close() {
      this.$refs['form'].resetFields()
      this.$emit('update:addDialog', false)
    },
    confirm() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.form.typeId) {
            // 修改
            caseQuestionTypeUpdate(this.form)
              .then(() => {
                this.$message.success('编辑问诊类型成功！')
                loading.close()
                this.close()
                this.$emit('success')
              })
              .catch((err) => {
                console.log(err)
                loading.close()
              })
          } else {
            // 新增
            try {
              await caseQuestionTypeAdd(this.form)
              this.$message.success('添加问诊类型成功！')
              loading.close()
              this.close()
              this.$emit('success')
            } catch (error) {
              console.log(error)
              loading.close()
            }
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss"></style>
