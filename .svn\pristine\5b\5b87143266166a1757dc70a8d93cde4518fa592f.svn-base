<template>
  <div>
    <el-form :model="form" :rules="rules" ref="theForm">
      <el-form-item label="题干:" :label-width="labelwidth" prop="question">
        <el-col :span="14" class="small_input">
          <editor-more
            :content="form.question"
            @change="
              (data) => {
                contentChange(data, 'question')
              }
            "
          ></editor-more>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="primary" @click="contentChange({ html: '' }, 'question')">清空</el-button>
        </el-col>
      </el-form-item>
      <!--<el-form-item label="选项:" :label-width="labelwidth" prop="options">
				<el-row v-for="(item,index) in form.optionsArr">
					<el-col :span='14' class="small_input">
						<el-form-item :label="letters[index]+'、'" :label-width="labelwidth">
							<editor-more :content='item' @change="((data)=>{optionChange(data,index)})"></editor-more>
						</el-form-item>
					</el-col>
					<el-col :span='10' class="small_inputbtn">
						<el-button type='primary' @click="optionChange({html:''},index)">清空</el-button>
					</el-col>
				</el-row>
			</el-form-item>-->
      <el-form-item label="答案:" :label-width="labelwidth" prop="answer">
        <el-radio-group v-model="form.answer">
          <el-radio :label="item" v-for="(item, index) in form.optionsArr">{{ item }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="试题分值：" :label-width="labelwidth" prop="scoreType" v-if="getScore">
        <el-radio-group v-model="form.scoreType" @change="socreTypeChange">
          <el-radio :label="1" name="1">自动分值</el-radio>
          <el-radio :label="2" name="2">手动分值</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分数：" :label-width="labelwidth" prop="score" v-if="getScore">
        <el-col :span="22">
          <el-input-number v-model="form.score" :min="0" :step="1" step-strictly label="请输入分数" :disabled="form.scoreType != 2"></el-input-number>
        </el-col>
      </el-form-item>
      <el-form-item label="解析:" :label-width="labelwidth" prop="analysis">
        <el-col :span="14" class="small_input">
          <editor-more
            :content="form.analysis"
            @change="
              (data) => {
                contentChange(data, 'analysis')
              }
            "
          ></editor-more>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="primary" @click="contentChange({ html: '' }, 'analysis')">清空</el-button>
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import EditorMore from '@/components/Editor/index.vue'
export default {
  components: {
    EditorMore
  },
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({})
    },
    letters: {
      type: Array,
      required: true
    },
    getScore: {
      type: Boolean,
      required: false,
      default: false
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    return {
      labelwidth: '110px',
      rules: {
        question: [
          {
            required: true,
            message: '请输入题干'
          }
        ],
        options: [
          {
            required: true,
            message: '选项不能为空'
          }
        ],
        answer: [
          {
            required: true,
            message: '请填写答案'
          }
        ],
        scoreType: [
          {
            required: true,
            message: '请选择试题分值'
          }
        ],
        score: [
          {
            required: true,
            message: '请输入分数'
          }
        ]
      }
    }
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      var form = this.form
      if (form.list && form.list.length > 0) {
        form.list.map((item) => {
          if (item.options) {
            var optionObject = JSON.parse(item.options)
            this.form.optionsArr = Object.values(optionObject)
          } else {
            this.form.optionsArr = ['正确', '错误']
          }
          this.form.answer = item.answer ? item.answer : ''
          this.form.analysis = item.analysis ? item.analysis : ''
          this.form.scoreType = item.scoreType ? item.scoreType : 1
          this.form.score = item.score ? item.score : 0
        })
      } else {
        this.form.optionsArr = ['正确', '错误']
        this.form.answer = ''
        this.form.analysis = ''
        this.form.scoreType = 1
        this.form.score = this.socorRegular[this.form.questionType]
      }
      var optionsObj = {}
      this.form.optionsArr.map((item, index) => {
        optionsObj[this.letters[index]] = item
      })
      this.form.options = JSON.stringify(optionsObj)
    },
    optionChange(data, index) {
      this.$set(this.form.optionsArr, index, data.html)
      var flag = this.form.optionsArr.filter((item) => {
        return !item
      })
      if (flag.length > 0) {
        this.form.options = ''
      } else {
        var optionsObj = {}
        this.form.optionsArr.map((item, index) => {
          optionsObj[this.letters[index]] = item
        })
        this.form.options = JSON.stringify(optionsObj)
      }
    },
    contentChange(data, label) {
      this.form[label] = data.html
      this.$nextTick(() => {
        this.$refs.theForm.clearValidate()
      })
    },
    socreTypeChange() {
      var socorRegular = this.socorRegular
      this.form.score = this.form.scoreType == 1 ? socorRegular[this.form.questionType] : this.form.score
    },
    beforeSubmit() {
      return new Promise((resolve, reject) => {
        this.$refs.theForm.validate((valid) => {
          if (!valid) {
            reject()
          } else {
            var thetopic = {
              question: this.form.question,
              list: [
                {
                  options: this.form.options,
                  answer: this.form.answer,
                  analysis: this.form.analysis,
                  score: this.form.score,
                  scoreType: this.form.scoreType,
                  questionExerciseAnswerId: this.form.list.length && this.form.list[0].questionExerciseAnswerId ? this.form.list[0].questionExerciseAnswerId : null,
                  questionExerciseId: this.form.list.length && this.form.list[0].questionExerciseId ? this.form.list[0].questionExerciseId : null,
                  paperQuestionAnswerId: this.form.list.length && this.form.list[0].paperQuestionAnswerId ? this.form.list[0].paperQuestionAnswerId : null,
                  paperQuestionId: this.form.list.length && this.form.list[0].paperQuestionId ? this.form.list[0].paperQuestionId : null
                }
              ]
            }
            resolve(thetopic)
          }
        })
      })
    }
  }
}
</script>
