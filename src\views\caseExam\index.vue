<template>
  <div class="app-container">
    <div class="page-header">
      <div class="back-btn" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="page-title">病例考核管理</div>
    </div>

    <div class="content-wrapper">
      <div class="search-form">
        <el-form ref="form" :model="queryInfo" label-width="110px" inline>
          <el-form-item label="考试名称:">
            <el-input v-model="queryInfo.name" size="small" maxlength="40" placeholder="请输入考试名称" clearable @keydown.native.enter="getList" @clear="getList" class="search-input"> </el-input>
          </el-form-item>
          <el-form-item label="训练时间:">
            <el-date-picker v-model="time" size="small" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange" clearable class="date-picker"> </el-date-picker>
          </el-form-item>
          <el-form-item label="状态:">
            <el-radio-group v-model="queryInfo.state" @change="getList" class="status-radio">
              <el-radio :label="1">未开考</el-radio>
              <el-radio :label="2">考试中</el-radio>
              <el-radio :label="3">已结束</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="btn-group">
            <el-button type="primary" size="small" class="search-btn" @click="getList">查询</el-button>
            <el-button size="small" class="reset-btn" @click="reset">重置</el-button>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addDialog = true" class="add-btn">添加考核</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table-container">
        <el-table :data="list" class="exam-table" header-cell-class-name="el_table_header_cell_class">
          <el-table-column prop="name" label="考试名称" width="200" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column label="考核时间" width="420" align="center">
            <template v-slot="{ row }">
              <div>{{ row.startTime }}~{{ row.endTime }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="time" label="考试限时" width="120" align="center">
            <template v-slot="{ row }">
              <div class="time-limit">{{ row.time }}分钟</div>
            </template>
          </el-table-column>
          <el-table-column prop="cou" label="考试人数" width="width" align="center">
            <template v-slot="{ row }">
              <div class="exam-count">
                <span class="current-count">{{ row.alreadyNumber }}</span>
                <span class="separator">/</span>
                <span class="total-count">{{ row.allNumber }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="caseName" label="考试病例" min-width="200" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="allScore" label="总分" width="80" align="center"> </el-table-column>
          <el-table-column prop="passScore" label="及格分" width="80" align="center"> </el-table-column>
          <el-table-column prop="state" label="状态" width="110" align="center">
            <template v-slot="{ row }">
              <el-tag :type="rowStateStyle(row)" class="status-tag">{{ row.state | examState }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="操作" width="200" align="center">
            <template v-slot="{ row }">
              <div class="action-buttons">
                <el-button v-if="row.state === 1" type="text" size="small" @click="edit(row)" icon="el-icon-edit-outline" class="edit-btn">编辑</el-button>
                <el-button v-if="row.state === 1" type="text" size="small" icon="el-icon-delete" @click="del(row)" class="delete-btn">删除</el-button>
                <el-button v-if="row.state === 2 || row.state === 3" type="text" size="small" icon="el-icon-tickets" @click="details(row)" class="detail-btn">详情</el-button>
                <el-button v-if="row.state === 3" type="text" size="small" icon="el-icon-copy-document" @click="openCopyDialog(row)" class="copy-btn">复制</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="prev, pager, next " :total="total"> </el-pagination>
        </div>
      </div>
    </div>
    <!-- 新增/编辑考核 -->
    <AddExam ref="AddExam" :addDialog.sync="addDialog" @success="getList" />
    <!-- 考核详情 -->
    <ExamDetails ref="ExamDetails" :detailsDialog.sync="detailsDialog" />
  </div>
</template>
<script>
import { caseExamList, caseExamDetail, caseExamRemove } from '@/api/caseExam'
import { formatDate } from '@/filters'
import AddExam from './add'
import ExamDetails from '@/views/caseExam/components/ExamDetails'
export default {
  name: 'CaseExam',
  components: {
    AddExam,
    ExamDetails
  },
  data() {
    return {
      queryInfo: {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 8
      },
      time: null,
      list: [],
      total: 0,
      addDialog: false,
      detailsDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseExamList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 8
      }
      this.time = null
      this.getList()
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? 'success' : ' '
      return type
    },
    async edit(row) {
      const { data } = await caseExamDetail({ id: row.examId })
      this.$refs['AddExam'].showData(data)
      this.addDialog = true
    },
    del(row) {
      this.$confirm('确定要删除该考核吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseExamRemove({ id: row.examId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async details(row) {
      const { data } = await caseExamDetail({ id: row.examId })
      this.$refs['ExamDetails'].getInfo(data)
      this.detailsDialog = true
    },
    async openCopyDialog(row) {
      const { data } = await caseExamDetail({ id: row.examId })
      this.$refs['AddExam'].showData(data, 'copy')
      this.addDialog = true
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  padding: 0 50px;
  padding-top: 10px;
  background: #e3e8ed;
  overflow: hidden;

  .page-header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 18px;
    margin-bottom: 22px;

    .back-btn {
      position: absolute;
      left: 0;
      top: -10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;

      img {
        width: 38px;
        height: 38px;
      }

      &:hover {
        background: #e7f0ff;
      }
    }

    .page-title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      text-align: center;
    }
  }

  .content-wrapper {
    width: 100%;
    height: 810px;
    padding: 30px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px 30px 30px 30px;
    position: relative;
  }
}
</style>

<style lang="scss" scoped>
.content-wrapper {
  position: relative;
  ::v-deep {
    .search-form {
      .el-form {
        display: flex;
        .el-form-item {
          display: flex;
          align-items: center;

          .el-form-item__label {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #666666;
          }
          .el-form-item__content {
            .el-input {
              .el-input__inner {
                width: 300px;
                height: 45px;
                background: #eef0f2;
                border: none;
                border-radius: 74px 74px 74px 74px;
                font-size: 20px;
                color: #333333;
                &::placeholder {
                  color: #cccccc;
                }
              }
              .el-input__suffix {
                .el-input__suffix-inner {
                  .el-icon-circle-close {
                    margin-right: 5px;
                    margin-top: 2px;
                    font-size: 20px;
                  }
                }
              }
            }

            .el-radio-group {
              display: flex;
              .el-radio {
                display: flex;
                align-items: center;
                margin-right: 15px;
                .el-radio__inner {
                  width: 18px;
                  height: 18px;
                  background: #fff;
                  border-color: #b1b1b1;
                }
                .el-radio__label {
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  font-size: 20px;
                  color: #b1b1b1;
                }
              }

              .el-radio__input.is-checked .el-radio__inner {
                &::after {
                  background: #274e6a;
                  width: 12px;
                  height: 12px;
                }
              }
              .el-radio__input.is-checked + .el-radio__label {
                color: #274e6a;
              }
            }
            .el-date-editor {
              width: 420px;
              height: 45px;
              background: #eef0f2;
              border-radius: 74px 74px 74px 74px;
              border: none;
              .el-icon-date {
                font-size: 20px;
                margin-left: 20px;
                margin-top: 5px;
              }
              .el-range-separator {
                margin-top: 12px;
              }
              .el-range-input {
                background: transparent;
                font-size: 20px;
              }
              .el-range__close-icon {
                margin-right: 5px;
                margin-top: 5px;
                font-size: 20px;
              }
            }
          }
          .search-btn {
            padding: 0;
            width: 70px;
            height: 45px;
            background: #65849a;
            border-radius: 63px 63px 63px 63px;
            border: none;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            text-align: center;
          }
          .reset-btn {
            padding: 0;
            width: 70px;
            height: 45px;
            background: #fff;
            border-radius: 63px 63px 63px 63px;
            border: 1px solid #65849a;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #65849a;
            text-align: center;
          }
          .add-btn {
            padding: 0;
            width: 120px;
            height: 45px;
            background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
            border-radius: 63px 63px 63px 63px;
            border: none;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            text-align: center;
          }
        }
      }
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }

    .el-table {
      border: none;
      &::before {
        display: none;
      }
      .el-table__row {
        border-radius: 8px 8px 8px 8px;
        .el-table__cell {
          height: 55px;
          background: #f6f8fa;
          border-top: 2px solid #fff;
          .cell {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            text-align: center;
          }
        }
      }
      .el_table_header_cell_class {
        height: 55px;
        background: #f6f8fa;
        text-align: center;
        & > .cell {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #999999;
        }
      }

      .time-range {
        display: flex;
        flex-direction: column;
        align-items: center;
        line-height: 1.4;

        .time-separator {
          color: #909399;
          margin: 2px 0;
        }
      }

      .time-limit {
        color: #333333;
        font-weight: 500;
      }

      .exam-count {
        .current-count {
          color: #409eff;
          font-weight: 600;
        }

        .separator {
          color: #909399;
          margin: 0 4px;
        }

        .total-count {
          color: #333333;
        }
      }

      .status-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 84px;
        height: 38px;
        background: rgba(101, 132, 154, 0.05);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e2e2e2;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #3381b9;
      }

      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;

        .edit-btn,
        .delete-btn,
        .detail-btn,
        .copy-btn {
          padding: 0;
          width: 88px;
          height: 38px;
          background: #ffffff;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #e2e2e2;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #3381b9;
        }
      }
    }

    .pagination-wrapper {
      position: absolute;
      left: 50%;
      bottom: 15px;
      transform: translateX(-50%);
      .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #65849a;
        color: #eef0f2;
      }
      .el-pagination.is-background .el-pager li {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        background-color: #eef0f2;
        color: #65849a;
      }
      .el-pagination.is-background .btn-prev,
      .el-pagination.is-background .btn-next {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        background-color: #eef0f2;
        color: #65849a;
        font-size: 16px;
      }
      .el-pagination.is-background .btn-prev:disabled {
        color: rgba($color: #65849a, $alpha: 0.3);
      }
      .el-pagination__total {
        height: 40px;
        line-height: 40px;
        color: #fff;
        font-size: 15px;
      }
    }
  }
}
</style>

<style lang="scss">
.el-tooltip__popper {
  font-size: 14px;
}
</style>
