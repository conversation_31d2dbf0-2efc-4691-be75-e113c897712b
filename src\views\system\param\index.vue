<template>
  <div class="div-user-sum">
    <div class="div-top">
      <span>参数管理</span>
    </div>
    <el-button type="primary" style="margin: 0 0 10px 10px" @click="openAddRouter({ id: 0 })" icon="el-icon-circle-plus-outline">添加根参数</el-button>
    <div style="margin: 10px">
      <el-table :data="tableRouter" row-key="id" border :tree-props="treeProps">
        <el-table-column prop="paramName" label="参数名称"> </el-table-column>
        <el-table-column prop="code" label="参数编码"> </el-table-column>
        <el-table-column prop="value" label="参数值"> </el-table-column>
        <!--<el-table-column prop="schoolName" label="所属学校">
					<template slot-scope="scope">
						{{scope.row.schoolName || '-'}}
					</template>
				</el-table-column>
				<el-table-column prop="majorName" label="所属专业">
					<template slot-scope="scope">
						{{scope.row.majorName || '-'}}
					</template>
				</el-table-column>-->
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" @click="openAddRouter(scope.row)" size="small">添加下级</el-button>
            <el-button type="primary" @click="openEditRouter(scope.row)" plain size="small">编辑</el-button>
            <el-button type="danger" @click="removeRouter(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="添加参数" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="saveParamDialog" width="350px">
      <el-form :model="addForm" :rules="rules" ref="addForm">
        <el-form-item label="参数名称" prop="paramName">
          <el-input v-model="addForm.paramName" placeholder="请输入参数名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="参数编码" prop="code">
          <el-input v-model="addForm.code" placeholder="请输入参数编码" maxlength="40" clearable> </el-input>
        </el-form-item>
        <el-form-item label="参数值" prop="value">
          <el-input v-model="addForm.value" placeholder="请输入参数值" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="comment">
          <el-input v-model="addForm.comment" placeholder="请输入备注" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="saveParamReset">取 消</el-button>
        <el-button type="primary" @click="toAddRouter" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑参数" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editRouterDialog" width="350px">
      <el-form :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="参数名称" prop="paramName">
          <el-input v-model="editForm.paramName" placeholder="请输入参数名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="参数编码" prop="code">
          <el-input v-model="editForm.code" placeholder="请输入参数编码" maxlength="40" clearable> </el-input>
        </el-form-item>
        <el-form-item label="参数值" prop="value">
          <el-input v-model="editForm.value" placeholder="请输入参数值" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="comment">
          <el-input v-model="editForm.comment" placeholder="请输入备注" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editRouterReset">取 消</el-button>
        <el-button type="primary" @click="editRouter">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { paramTree, saveParam, updateParam, removeParam } from '@/api/param.js'
export default {
  data() {
    return {
      tableRouter: [],
      dataloading: false,
      saveParamDialog: false,
      editRouterDialog: false,
      treeProps: {
        children: 'children',
        hasChildren: null
      },
      addForm: {
        parentId: '',
        paramName: '',
        code: '',
        value: '',
        comment: ''
      },
      editForm: {
        paramId: '',
        parentId: '',
        paramName: '',
        code: '',
        value: '',
        comment: ''
      },
      rules: {
        paramName: [
          {
            required: true,
            message: '请输入参数名称'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入参数编码'
          }
        ],
        value: [
          {
            required: true,
            message: '请输入参数值'
          }
        ]
      }
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      paramTree({}).then((res) => {
        this.tableRouter = res.data
      })
    },
    openAddRouter(item) {
      this.saveParamDialog = true
      this.addForm.parentId = item.id
    },
    toAddRouter() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm)
          saveParam(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getDataList()
              this.saveParamReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    saveParamReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.saveParamDialog = false
    },
    openEditRouter(item) {
      this.editForm = Object.assign({}, item)
      this.editRouterDialog = true
    },
    editRouter() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.editForm)
          data.paramId = data.id
          updateParam(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getDataList()
              this.editRouterReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    editRouterReset() {
      this.editRouterDialog = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    removeRouter(item) {
      this.$confirm('是否删除该参数及子参数?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeParam({
            paramId: item.id
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getDataList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
