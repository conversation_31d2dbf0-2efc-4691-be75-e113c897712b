<template>
  <div>
    <ul class="question">
      <li v-for="(item, index) in list" :key="index" :class="{ notCollectLi: listType === '0' }">
        <!-- 顶部icon -->
        <template v-if="item.level === 1">
          <span class="levelIcon levelIcon1">
            <img src="@/assets/casePractise/level1.png" alt="" />
            <i> 重 </i>
          </span>
        </template>
        <template v-else-if="item.level === 2">
          <span class="levelIcon levelIcon2">
            <img src="@/assets/casePractise/level2.png" alt="" />
            <i> 常 </i>
          </span>
        </template>
        <template v-else>
          <span class="levelIcon levelIcon3">
            <img src="@/assets/casePractise/level3.png" alt="" />
            <i> 无 </i>
          </span>
        </template>
        <!-- 问诊对话 -->
        <template v-if="listType === '1'">
          <div>
            <div class="trouble">
              <span class="icon">Q</span>
              <span class="text">
                <i class="type" v-if="item.level">【{{ item.typeName }}】</i>
                {{ item.userProblem }}
                <i v-if="item.level && item.level !== 3" class="score">({{ item.score }}分)</i>
              </span>
            </div>
            <div class="systemQuestion">
              <span class="icon">P</span>
              <span class="text">{{ item.problem ? item.problem : '暂无' }}</span>
            </div>
            <div class="systemQuestion">
              <span class="icon">A</span>
              <span class="text">{{ item.answer }}</span>
            </div>
          </div>
        </template>
        <!-- 未采集的问题 -->
        <template v-else>
          <div class="trouble">
            <span class="icon">Q</span>
            <span class="text">
              <i class="type">【{{ item.typeName }}】</i>
              {{ item.problem }}
              <i v-if="item.level && item.level !== 3" class="score">({{ item.score }}分)</i>
            </span>
          </div>
          <div class="systemQuestion">
            <span class="icon">A</span>
            <span class="text">{{ item.answer }}</span>
          </div>
        </template>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: '',
  props: {
    list: Array,
    listType: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>
<style scoped lang="scss">
ul {
  padding: 0 16px;
  margin: 0;
  li {
    position: relative;
    list-style: none;
    width: 100%;
    min-height: 124px;
    background: #f4f7ff;
    margin-bottom: 10px;
    padding-left: 65px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-right: 15px;
    border-radius: 10px 10px 10px 10px;
    .levelIcon {
      position: absolute;
      left: 19px;
      top: 18px;
      width: 35px;
      height: 31px;
      img {
        width: 100%;
        height: 100%;
      }
      i {
        position: absolute;
        left: 46%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-style: normal;
        font-size: 18px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
      }
    }
    .levelIcon1 {
      color: #ff3b65;
    }
    .levelIcon2 {
      color: #15b2cf;
    }
    .levelIcon3 {
      color: #666666;
    }
    .trouble {
      display: flex;
      .icon {
        position: relative;
        width: 21px;
        height: 21px;
        margin-right: 14px;
        line-height: 21px;
        background: rgba(57, 127, 244, 0.2);
        font-size: 12px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #397ff4;
        text-align: center;
        border-radius: 50%;
        &::after {
          content: ':';
          position: absolute;
          right: -8px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 18px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #397ff4;
        }
      }
      .text {
        flex: 1;
        line-height: 24px;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        i {
          font-style: normal;
        }
        .type {
          color: #397ff4;
        }
        .score {
          margin-left: 4px;
          color: #999999;
        }
      }
    }
    .systemQuestion {
      display: flex;
      align-items: center;
      margin-top: 12px;
      .icon {
        position: relative;
        width: 21px;
        height: 21px;
        margin-right: 4px;
        line-height: 21px;
        background: rgba(57, 127, 244, 0.2);
        font-size: 12px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #397ff4;
        text-align: center;
        border-radius: 50%;
        &::after {
          content: ':';
          position: absolute;
          right: -8px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 18px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #397ff4;
        }
      }
      .text {
        margin-left: 14px;
        font-size: 18px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #666666;
      }
    }
  }
  .notCollectLi {
    height: 93px;
  }
}
</style>
