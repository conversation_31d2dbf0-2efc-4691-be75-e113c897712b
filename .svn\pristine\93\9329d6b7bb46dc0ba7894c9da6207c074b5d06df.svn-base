<template>
  <div>
    <el-dialog custom-class="questionRecord" :visible="showDialog" top="5vh" @close="close" @opened="open">
      <div>
        <div class="tabBar">
          <span :class="[{ checked: tabBarType === 0 }, 'tabBar_item']" @click="tabBarType = 0">未采集的问题({{ notCollect.length }})</span>
          <span :class="[{ checked: tabBarType === 1 }, 'tabBar_item']" @click="tabBarType = 1">问诊对话</span>
        </div>
        <div class="tabBarContent">
          <el-tabs v-model="questionType">
            <!-- 全部问题 -->
            <el-tab-pane :label="allQuestionLabel" name="1">
              <template v-if="tabBarType === 0">
                <DialogueItem :list="notCollect" :listType="'0'" />
              </template>
              <template v-else>
                <DialogueItem :list="recordInfo.allList" />
              </template>
            </el-tab-pane>
            <!-- 重要问题 -->
            <el-tab-pane :label="ImportantQuestionLabel" name="2">
              <template v-if="tabBarType === 0">
                <DialogueItem :list="notCollectInfo.level1List" :listType="'0'" />
              </template>
              <template v-else>
                <DialogueItem :list="recordInfo.level1List" />
              </template>
            </el-tab-pane>
            <el-tab-pane :label="routineQuestionLabel" name="3">
              <!-- 常规问题 -->
              <template v-if="tabBarType === 0">
                <DialogueItem :list="notCollectInfo.level2List" :listType="'0'" />
              </template>
              <template v-else>
                <DialogueItem :list="recordInfo.level2List" />
              </template>
            </el-tab-pane>
            <el-tab-pane :label="invalidQuestionLabel" name="4">
              <!-- 无效问题 -->
              <template v-if="tabBarType === 0">
                <DialogueItem :list="notCollectInfo.level3List" :listType="'0'" />
              </template>
              <template v-else>
                <DialogueItem :list="recordInfo.level3List" />
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DialogueItem from '@/views/casePractise/components/DialogueItem'
export default {
  name: 'LookDialogue',
  components: {
    DialogueItem
  },
  props: {
    showDialog: {
      type: Boolean,
      require: true
    },
    notCollect: {
      type: Array,
      default: []
    },
    record: {
      type: Array,
      default: []
    }
  },

  computed: {
    allQuestionLabel() {
      let num = 0
      let score = 0
      if (this.tabBarType === 0) {
        num = this.notCollect.length
        score = this.notCollect.reduce((accumulator, currentValue) => {
          return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
        }, 0)
      } else {
        num = this.record.length
        score = this.record.reduce((accumulator, currentValue) => {
          return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
        }, 0)
      }
      return `全部问题(${num}个/${score}分)`
    },
    ImportantQuestionLabel() {
      let num = 0
      let score = 0
      if (this.tabBarType === 0) {
        const level1 = this.notCollect.filter((item) => {
          return item.level === 1
        })
        num = level1.length
        score = level1.reduce((accumulator, currentValue) => {
          return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
        }, 0)
      } else {
        const level1 = this.record.filter((item) => {
          return item.level === 1
        })
        num = level1.length
        score = level1.reduce((accumulator, currentValue) => {
          return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
        }, 0)
      }
      return `重要问题(${num}个/${score}分)`
    },
    routineQuestionLabel() {
      let num = 0
      let score = 0
      if (this.tabBarType === 0) {
        const level2 = this.notCollect.filter((item) => {
          return item.level === 2
        })
        num = level2.length
        score = level2.reduce((accumulator, currentValue) => {
          return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
        }, 0)
      } else {
        const level2 = this.record.filter((item) => {
          return item.level === 2
        })
        num = level2.length
        score = level2.reduce((accumulator, currentValue) => {
          return accumulator + currentValue.score ? parseFloat(currentValue.score) : 0
        }, 0)
      }
      return `常规问题(${num}个/${score}分)`
    },
    invalidQuestionLabel() {
      let num = 0
      if (this.tabBarType === 0) {
        const level3 = this.notCollect.filter((item) => {
          return item.level === 3
        })
        num = level3.length
      } else {
        const level3 = this.record.filter((item) => {
          return item.level === 3 || !item.level
        })
        num = level3.length
      }
      return `无效问题(${num}个)`
    }
  },
  data() {
    return {
      tabBarType: 1,
      questionType: '1', // 1:重要 2：常规 3：无效
      notCollectInfo: {
        level1List: [],
        level2List: [],
        level3List: []
      },
      recordInfo: {
        allList: [],
        level1List: [],
        level2List: [],
        level3List: []
      }
    }
  },
  created() {},
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    open() {
      // 问诊对话记录
      if (this.record.length) {
        const level1 = this.record.filter((item) => {
          return item.level === 1
        })
        const level2 = this.record.filter((item) => {
          return item.level === 2
        })
        const level3 = this.record.filter((item) => {
          return item.level === 3 || !item.level
        })
        this.recordInfo.level1List = level1
        this.recordInfo.level2List = level2
        this.recordInfo.level3List = level3
        this.recordInfo.allList = [...level1, ...level2, ...level3]
        console.log(this.recordInfo)
      }
      // 未采集的问题
      if (this.notCollect.length) {
        const level1 = this.notCollect.filter((item) => {
          return item.level === 1
        })
        const level2 = this.notCollect.filter((item) => {
          return item.level === 2
        })
        const level3 = this.notCollect.filter((item) => {
          return item.level === 3
        })
        this.notCollectInfo.level1List = level1
        this.notCollectInfo.level2List = level2
        this.notCollectInfo.level3List = level3
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .questionRecord {
    width: 876px;
    height: 824px;
    background: #ffffff;
    border-radius: 0px 0px 0px 0px;
    .el-dialog__header {
      .el-dialog__headerbtn {
        .el-dialog__close {
          font-size: 26px;
          height: 26px;
          width: 26px;
        }
      }
    }
    .el-dialog__body {
      display: flex;
      justify-content: center;
      padding-top: 15px;
      padding-left: 0;
      padding-right: 0;
    }
    .tabBar {
      display: flex;
      .tabBar_item {
        margin-right: 10px;
        padding: 12px 22px;
        background: #f0f2f5;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #e0e4e8;
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #666666;
        cursor: pointer;
      }
      .checked {
        position: relative;
        background: #ffffff;
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #397ff4;
        border-bottom: none;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 6px;
          width: 100%;
          background: #397ff4;
          border-radius: 4px 4px 0px 0px;
        }
      }
    }
    .tabBarContent {
      width: 806px;
      height: 705px;
      margin-top: -1px;
      background: #ffffff;
      border: 1px solid #e0e4e8;
      overflow: auto;
      /* 定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 4px; /* 滚动条宽度 */
        background-color: #f1f1f1; /* 滚动条背景色 */
      }

      /* 定义滚动条轨道样式 */
      &::-webkit-scrollbar-track {
        border-radius: 4px; /* 滚动条轨道的弧形形状 */
      }

      /* 定义滚动条滑块样式 */
      &::-webkit-scrollbar-thumb {
        background-color: #a8a8a8; /* 滚动条滑块颜色 */
        border-radius: 4px; /* 滚动条滑块的弧形形状 */
      }

      /* 定义滚动条滑块在悬停状态时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #888787; /* 滚动条滑块悬停状态的颜色 */
      }
      .el-tabs__nav-scroll {
        height: 70px;
        padding-left: 35px;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background: #e0e4e8;
      }
      .el-tabs__item {
        height: 70px;
        line-height: 70px;
        font-size: 18px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        color: #666666;
      }
      .el-tabs__item.is-active {
        color: #397ff4;
      }
    }
  }
}
</style>
