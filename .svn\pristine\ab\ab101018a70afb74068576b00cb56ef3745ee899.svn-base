{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20220303.165231.6636.0.001.json", "dumpEventTime": "2022-03-03T16:52:31Z", "dumpEventTimeStamp": "1646297551109", "processId": 6636, "cwd": "F:\\li\\zf_exam_new\\zf_exam_new_admin", "commandLine": ["node", "F:\\li\\zf_exam_new\\zf_exam_new_admin\\node_modules\\.bin\\\\..\\_@vue_cli-service@3.5.3@@vue\\cli-service\\bin\\vue-cli-service.js", "serve"], "nodejsVersion": "v12.13.1", "wordSize": 64, "arch": "x64", "platform": "win32", "componentVersions": {"node": "12.13.1", "v8": "7.7.299.13-node.16", "uv": "1.33.1", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.39.2", "napi": "5", "llhttp": "1.1.4", "http_parser": "2.8.0", "openssl": "1.1.1d", "cldr": "35.1", "icu": "64.2", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.13.1/node-v12.13.1-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.13.1/node-v12.13.1.tar.gz", "libUrl": "https://nodejs.org/download/release/v12.13.1/win-x64/node.lib"}, "osName": "Windows_NT", "osRelease": "10.0.19042", "osVersion": "Windows 10 Pro", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i5-3470 CPU @ 3.20GHz", "speed": 3193, "user": 21577593, "nice": 0, "sys": 23594750, "idle": 550845343, "irq": 2261765}, {"model": "Intel(R) Core(TM) i5-3470 CPU @ 3.20GHz", "speed": 3193, "user": 28417187, "nice": 0, "sys": 23944234, "idle": 543656031, "irq": 431140}, {"model": "Intel(R) Core(TM) i5-3470 CPU @ 3.20GHz", "speed": 3193, "user": 24116468, "nice": 0, "sys": 18969421, "idle": 552931546, "irq": 344109}, {"model": "Intel(R) Core(TM) i5-3470 CPU @ 3.20GHz", "speed": 3193, "user": 31550234, "nice": 0, "sys": 21233171, "idle": 543234046, "irq": 359734}], "networkInterfaces": [{"name": "以太网", "internal": false, "mac": "94:de:80:7d:ce:51", "address": "fe80::14d1:14a:8209:b24a", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 6}, {"name": "以太网", "internal": false, "mac": "94:de:80:7d:ce:51", "address": "************", "netmask": "*************", "family": "IPv4"}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}], "host": "DESKTOP-C14GK2M"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00007ff70ce11729", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+10873"}, {"pc": "0x00007ff70ce15b4c", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+28316"}, {"pc": "0x00007ff70ce14b08", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+24152"}, {"pc": "0x00007ff70cf0369b", "symbol": "v8::base::CPU::has_sse+37723"}, {"pc": "0x00007ff70d7082de", "symbol": "v8::Isolate::ReportExternalAllocationLimitReached+94"}, {"pc": "0x00007ff70d6f0321", "symbol": "v8::SharedArrayBuffer::Externalize+833"}, {"pc": "0x00007ff70d5bdbec", "symbol": "v8::internal::Heap::EphemeronKeyWriteBarrierFromCode+1436"}, {"pc": "0x00007ff70d5c8f90", "symbol": "v8::internal::Heap::ProtectUnprotectedMemoryChunks+1312"}, {"pc": "0x00007ff70d5c5ac4", "symbol": "v8::internal::Heap::PageFlagsAreConsistent+3204"}, {"pc": "0x00007ff70d5bb353", "symbol": "v8::internal::Heap::CollectGarbage+1283"}, {"pc": "0x00007ff70d5b9b24", "symbol": "v8::internal::Heap::AddRetainedMap+2356"}, {"pc": "0x00007ff70d5e19b3", "symbol": "v8::internal::Factory::NewRawOneByteString+83"}, {"pc": "0x00007ff70d408cda", "symbol": "v8::internal::String::SlowFlatten+442"}, {"pc": "0x00007ff70d2d1cef", "symbol": "unibrow::Utf8::EncodeOneByte+687"}, {"pc": "0x00007ff70d406d7e", "symbol": "v8::Name::GetIdentityHash+558"}, {"pc": "0x00007ff70d30c30a", "symbol": "v8::internal::StubCache::value_reference+11658"}, {"pc": "0x00007ff70db3404d", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+567949"}, {"pc": "0x00007ff70dab2903", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+37699"}, {"pc": "0x00007ff70db20d50", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+489360"}, {"pc": "0x00007ff70dab3bdc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42524"}, {"pc": "0x00007ff70dab3bdc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+42524"}, {"pc": "0x00007ff70db02e42", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+366722"}, {"pc": "0x00007ff70dad06ab", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+159979"}, {"pc": "0x00007ff70dab107c", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+31420"}, {"pc": "0x00007ff70d616360", "symbol": "v8::internal::Execution::CallWasm+1536"}, {"pc": "0x00007ff70d616453", "symbol": "v8::internal::Execution::CallWasm+1779"}, {"pc": "0x00007ff70d616832", "symbol": "v8::internal::Execution::TryCall+354"}, {"pc": "0x00007ff70d5fa065", "symbol": "v8::internal::MicrotaskQueue::RunMicrotasks+517"}, {"pc": "0x00007ff70dab47fc", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+45628"}, {"pc": "0x0000033a211f2d7e", "symbol": ""}], "javascriptHeap": {"totalMemory": 2150649856, "totalCommittedMemory": 2150649856, "usedMemory": 2130326536, "availableMemory": 57309496, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 262144, "capacity": 261872, "used": 32296, "available": 229576}, "new_space": {"memorySize": 4194304, "committedMemory": 4194304, "capacity": 2094976, "used": 5176, "available": 2089800}, "old_space": {"memorySize": 176533504, "committedMemory": 176533504, "capacity": 175125024, "used": 169395320, "available": 5729704}, "code_space": {"memorySize": 5406720, "committedMemory": 5406720, "capacity": 4469856, "used": 4469856, "available": 0}, "map_space": {"memorySize": 10752000, "committedMemory": 10752000, "capacity": 3500960, "used": 3500960, "available": 0}, "large_object_space": {"memorySize": 1952583680, "committedMemory": 1952583680, "capacity": 1952093744, "used": 1952093744, "available": 0}, "code_large_object_space": {"memorySize": 917504, "committedMemory": 917504, "capacity": 829184, "used": 829184, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 2094976, "used": 0, "available": 2094976}}}, "resourceUsage": {"userCpuSeconds": 916.171, "kernelCpuSeconds": 101.125, "cpuConsumptionPercent": 0.506985, "maxRss": 1267060736, "pageFaults": {"IORequired": 23638641, "IONotRequired": 0}, "fsActivity": {"reads": 12180, "writes": 1145}}, "libuv": [], "environmentVariables": {"=C:": "C:\\", "=F:": "F:\\li\\zf_exam_new\\zf_exam_new_admin", "ALLUSERSPROFILE": "C:\\ProgramData", "ANDROID_HOME": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "BABEL_ENV": "development", "ChocolateyInstall": "C:\\ProgramData\\chocolatey", "ChocolateyLastPathUpdate": "132857423067514635", "CLASSPATH": "C:\\Program Files\\Java\\jdk1.8.0_152\\lib\\dt.jar;C:\\Program Files\\Java\\jdk1.8.0_152\\lib\\tools.jar;", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "DESKTOP-C14GK2M", "ComSpec": "C:\\WINDOWS\\system32\\cmd.exe", "dp0": "F:\\li\\zf_exam_new\\zf_exam_new_admin\\node_modules\\.bin\\", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "ENV": "development", "GRADLE_HOME": "C:\\Users\\<USER>\\gradle-7.3.3\\bin", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\admin", "INIT_CWD": "F:\\li\\zf_exam_new\\zf_exam_new_admin", "JAVA_HOME": "C:\\Program Files\\Java\\jdk1.8.0_152", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\DESKTOP-C14GK2M", "NODE": "C:\\Program Files\\nodejs\\node.exe", "NODE_ENV": "development", "NODE_EXE": "C:\\Program Files\\nodejs\\\\node.exe", "NPM_CLI_JS": "C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js", "npm_config_access": "", "npm_config_allow_same_version": "", "npm_config_also": "", "npm_config_always_auth": "", "npm_config_argv": "{\"remain\":[],\"cooked\":[\"run\",\"dev\"],\"original\":[\"run\",\"dev\"]}", "npm_config_audit": "true", "npm_config_audit_level": "low", "npm_config_auth_type": "legacy", "npm_config_before": "", "npm_config_bin_links": "true", "npm_config_browser": "", "npm_config_ca": "", "npm_config_cache": "C:\\Users\\<USER>\\AppData\\Roaming\\npm-cache", "npm_config_cache_lock_retries": "10", "npm_config_cache_lock_stale": "60000", "npm_config_cache_lock_wait": "10000", "npm_config_cache_max": "Infinity", "npm_config_cache_min": "10", "npm_config_cafile": "", "npm_config_cert": "", "npm_config_cidr": "", "npm_config_color": "true", "npm_config_commit_hooks": "true", "npm_config_depth": "Infinity", "npm_config_description": "true", "npm_config_dev": "", "npm_config_dry_run": "", "npm_config_editor": "notepad.exe", "npm_config_engine_strict": "", "npm_config_fetch_retries": "2", "npm_config_fetch_retry_factor": "10", "npm_config_fetch_retry_maxtimeout": "60000", "npm_config_fetch_retry_mintimeout": "10000", "npm_config_force": "", "npm_config_format_package_lock": "true", "npm_config_git": "git", "npm_config_git_tag_version": "true", "npm_config_global": "", "npm_config_globalconfig": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc", "npm_config_globalignorefile": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmignore", "npm_config_global_style": "", "npm_config_group": "", "npm_config_ham_it_up": "", "npm_config_heading": "npm", "npm_config_https_proxy": "", "npm_config_if_present": "", "npm_config_ignore_prepublish": "", "npm_config_ignore_scripts": "", "npm_config_init_author_email": "", "npm_config_init_author_name": "", "npm_config_init_author_url": "", "npm_config_init_license": "ISC", "npm_config_init_module": "C:\\Users\\<USER>\\.npm-init.js", "npm_config_init_version": "1.0.0", "npm_config_json": "", "npm_config_key": "", "npm_config_legacy_bundling": "", "npm_config_link": "", "npm_config_local_address": "", "npm_config_loglevel": "notice", "npm_config_logs_max": "10", "npm_config_long": "", "npm_config_maxsockets": "50", "npm_config_message": "%s", "npm_config_metrics_registry": "https://registry.npm.taobao.org/", "npm_config_node_gyp": "C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_node_options": "", "npm_config_node_version": "12.13.1", "npm_config_noproxy": "", "npm_config_offline": "", "npm_config_onload_script": "", "npm_config_only": "", "npm_config_optional": "true", "npm_config_otp": "", "npm_config_package_lock": "true", "npm_config_package_lock_only": "", "npm_config_parseable": "", "npm_config_prefer_offline": "", "npm_config_prefer_online": "", "npm_config_prefix": "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "npm_config_preid": "", "npm_config_production": "", "npm_config_progress": "true", "npm_config_proxy": "", "npm_config_read_only": "", "npm_config_rebuild_bundle": "true", "npm_config_registry": "https://registry.npm.taobao.org/", "npm_config_rollback": "true", "npm_config_sass_binary_site": "https://npm.taobao.org/mirrors/node-sass/", "npm_config_save": "true", "npm_config_save_bundle": "", "npm_config_save_dev": "", "npm_config_save_exact": "", "npm_config_save_optional": "", "npm_config_save_prefix": "^", "npm_config_save_prod": "", "npm_config_scope": "", "npm_config_scripts_prepend_node_path": "warn-only", "npm_config_script_shell": "", "npm_config_searchexclude": "", "npm_config_searchlimit": "20", "npm_config_searchopts": "", "npm_config_searchstaleness": "900", "npm_config_send_metrics": "", "npm_config_shell": "C:\\WINDOWS\\system32\\cmd.exe", "npm_config_shrinkwrap": "true", "npm_config_sign_git_commit": "", "npm_config_sign_git_tag": "", "npm_config_sso_poll_frequency": "500", "npm_config_sso_type": "o<PERSON>h", "npm_config_strict_ssl": "true", "npm_config_tag": "latest", "npm_config_tag_version_prefix": "v", "npm_config_timing": "", "npm_config_tmp": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "npm_config_umask": "0000", "npm_config_unicode": "", "npm_config_unsafe_perm": "true", "npm_config_update_notifier": "true", "npm_config_usage": "", "npm_config_user": "", "npm_config_userconfig": "C:\\Users\\<USER>\\.npmrc", "npm_config_user_agent": "npm/6.12.1 node/v12.13.1 win32 x64", "npm_config_version": "", "npm_config_versions": "", "npm_config_viewer": "browser", "npm_execpath": "C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "npm_lifecycle_event": "dev", "npm_lifecycle_script": "vue-cli-service serve", "npm_node_execpath": "C:\\Program Files\\nodejs\\node.exe", "npm_package_author_email": "<EMAIL>", "npm_package_author_name": "Pan", "npm_package_browserslist_0": "> 1%", "npm_package_browserslist_1": "last 2 versions", "npm_package_bugs_url": "https://github.com/PanJiaChen/vue-element-admin/issues", "npm_package_dependencies_ali_oss": "^6.8.0", "npm_package_dependencies_axios": "0.18.1", "npm_package_dependencies_clipboard": "2.0.4", "npm_package_dependencies_codemirror": "5.45.0", "npm_package_dependencies_core_js": "^2.6.11", "npm_package_dependencies_downloadjs": "^1.4.7", "npm_package_dependencies_driver_js": "0.9.5", "npm_package_dependencies_dropzone": "5.5.1", "npm_package_dependencies_echarts": "4.2.1", "npm_package_dependencies_element_ui": "^2.13.0", "npm_package_dependencies_file_saver": "2.0.1", "npm_package_dependencies_fuse_js": "3.4.4", "npm_package_dependencies_jsonlint": "1.6.3", "npm_package_dependencies_jszip": "3.2.1", "npm_package_dependencies_js_cookie": "2.2.0", "npm_package_dependencies_normalize_css": "7.0.0", "npm_package_dependencies_nprogress": "0.2.0", "npm_package_dependencies_path_to_regexp": "2.4.0", "npm_package_dependencies_qrcodejs2": "^0.0.2", "npm_package_dependencies_quill_image_resize_module": "^3.0.0", "npm_package_dependencies_screenfull": "4.2.0", "npm_package_dependencies_showdown": "1.9.0", "npm_package_dependencies_sortablejs": "1.8.4", "npm_package_dependencies_vue": "2.6.10", "npm_package_dependencies_vue2_editor": "^2.10.2", "npm_package_dependencies_vuedraggable": "2.20.0", "npm_package_dependencies_vuex": "3.1.0", "npm_package_dependencies_vue_count_to": "1.0.13", "npm_package_dependencies_vue_quill_editor": "^3.0.6", "npm_package_dependencies_vue_router": "3.0.2", "npm_package_dependencies_vue_splitpane": "1.0.4", "npm_package_dependencies_wangeditor": "^3.1.1", "npm_package_dependencies_xlsx": "0.14.1", "npm_package_dependencies__riophae_vue_treeselect": "^0.4.0", "npm_package_description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "npm_package_devDependencies_autoprefixer": "^9.5.1", "npm_package_devDependencies_babel_core": "7.0.0-bridge.0", "npm_package_devDependencies_babel_eslint": "10.0.1", "npm_package_devDependencies_babel_jest": "23.6.0", "npm_package_devDependencies_chalk": "2.4.2", "npm_package_devDependencies_chokidar": "2.1.5", "npm_package_devDependencies_connect": "3.6.6", "npm_package_devDependencies_eslint": "5.15.3", "npm_package_devDependencies_eslint_plugin_vue": "5.2.2", "npm_package_devDependencies_html_webpack_plugin": "3.2.0", "npm_package_devDependencies_husky": "1.3.1", "npm_package_devDependencies_lint_staged": "8.1.5", "npm_package_devDependencies_mockjs": "1.0.1-beta3", "npm_package_devDependencies_node_sass": "^4.9.0", "npm_package_devDependencies_plop": "2.3.0", "npm_package_devDependencies_runjs": "^4.3.2", "npm_package_devDependencies_sass_loader": "^7.1.0", "npm_package_devDependencies_script_ext_html_webpack_plugin": "2.1.3", "npm_package_devDependencies_script_loader": "0.7.2", "npm_package_devDependencies_serve_static": "^1.13.2", "npm_package_devDependencies_svgo": "1.2.0", "npm_package_devDependencies_svg_sprite_loader": "4.1.3", "npm_package_devDependencies_vue_template_compiler": "2.6.10", "npm_package_devDependencies__babel_core": "7.0.0", "npm_package_devDependencies__babel_register": "7.0.0", "npm_package_devDependencies__vue_cli_plugin_babel": "3.5.3", "npm_package_devDependencies__vue_cli_plugin_eslint": "^3.9.1", "npm_package_devDependencies__vue_cli_plugin_unit_jest": "3.5.3", "npm_package_devDependencies__vue_cli_service": "3.5.3", "npm_package_devDependencies__vue_test_utils": "1.0.0-beta.29", "npm_package_engines_node": ">=8.9", "npm_package_engines_npm": ">= 3.0.0", "npm_package_homepage": "https://github.com/PanJiaChen/vue-element-admin#readme", "npm_package_husky_hooks_pre_commit": "lint-staged", "npm_package_keywords_0": "vue", "npm_package_keywords_1": "admin", "npm_package_keywords_2": "dashboard", "npm_package_keywords_3": "element-ui", "npm_package_keywords_4": "boilerplate", "npm_package_keywords_5": "admin-template", "npm_package_keywords_6": "management-system", "npm_package_license": "MIT", "npm_package_lint_staged_src_______js_vue__0": "eslint --fix", "npm_package_lint_staged_src_______js_vue__1": "git add", "npm_package_name": "vue-element-admin", "npm_package_repository_type": "git", "npm_package_repository_url": "git+https://github.com/PanJiaChen/vue-element-admin.git", "npm_package_scripts_build": "vue-cli-service build", "npm_package_scripts_build_stage": "vue-cli-service build --mode staging", "npm_package_scripts_dev": "vue-cli-service serve", "npm_package_scripts_lint": "eslint --ext .js,.vue src", "npm_package_scripts_new": "plop", "npm_package_scripts_preview": "node build/index.js --preview", "npm_package_scripts_svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "npm_package_scripts_test_ci": "npm run lint && npm run test:unit", "npm_package_scripts_test_unit": "jest --clearCache && vue-cli-service test:unit", "npm_package_version": "4.2.1", "NPM_PREFIX_NPM_CLI_JS": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js", "NUMBER_OF_PROCESSORS": "4", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OS": "Windows_NT", "Path": "C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\npm-lifecycle\\node-gyp-bin;F:\\li\\zf_exam_new\\zf_exam_new_admin\\node_modules\\.bin;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\TortoiseSVN\\bin;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Program Files\\Java\\jdk1.8.0_152\\bin;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools;C:\\Users\\<USER>\\gradle-7.3.3\\bin;;D:\\微信web开发者工具\\dll;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC;.CPL", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 58 Stepping 9, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "3a09", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "SystemDrive": "C:", "SystemRoot": "C:\\WINDOWS", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "USERDOMAIN": "DESKTOP-C14GK2M", "USERDOMAIN_ROAMINGPROFILE": "DESKTOP-C14GK2M", "USERNAME": "admin", "USERPROFILE": "C:\\Users\\<USER>", "VUE_APP_BASE_API": "/dev-api", "VUE_CLI_BABEL_TRANSPILE_MODULES": "true", "VUE_CLI_ENTRY_FILES": "[\"F:\\\\li\\\\zf_exam_new\\\\zf_exam_new_admin\\\\src\\\\main.js\"]", "WEBPACK_DEV_SERVER": "true", "windir": "C:\\WINDOWS", "_prog": "node"}, "sharedObjects": ["C:\\Program Files\\nodejs\\node.exe", "C:\\WINDOWS\\SYSTEM32\\ntdll.dll", "C:\\WINDOWS\\System32\\KERNEL32.DLL", "C:\\WINDOWS\\System32\\KERNELBASE.dll", "C:\\WINDOWS\\System32\\WS2_32.dll", "C:\\WINDOWS\\System32\\RPCRT4.dll", "C:\\WINDOWS\\System32\\ADVAPI32.dll", "C:\\WINDOWS\\System32\\msvcrt.dll", "C:\\WINDOWS\\SYSTEM32\\dbghelp.dll", "C:\\WINDOWS\\System32\\sechost.dll", "C:\\WINDOWS\\System32\\ucrtbase.dll", "C:\\WINDOWS\\System32\\USER32.dll", "C:\\WINDOWS\\System32\\win32u.dll", "C:\\WINDOWS\\System32\\GDI32.dll", "C:\\WINDOWS\\System32\\gdi32full.dll", "C:\\WINDOWS\\System32\\msvcp_win.dll", "C:\\WINDOWS\\System32\\PSAPI.DLL", "C:\\WINDOWS\\System32\\CRYPT32.dll", "C:\\WINDOWS\\System32\\bcrypt.dll", "C:\\WINDOWS\\SYSTEM32\\USERENV.dll", "C:\\WINDOWS\\SYSTEM32\\IPHLPAPI.DLL", "C:\\WINDOWS\\SYSTEM32\\WINMM.dll", "C:\\WINDOWS\\System32\\IMM32.DLL", "C:\\WINDOWS\\SYSTEM32\\powrprof.dll", "C:\\WINDOWS\\SYSTEM32\\UMPDC.dll", "C:\\WINDOWS\\SYSTEM32\\CRYPTBASE.DLL", "C:\\WINDOWS\\system32\\uxtheme.dll", "C:\\WINDOWS\\System32\\combase.dll", "C:\\WINDOWS\\system32\\mswsock.dll", "C:\\WINDOWS\\SYSTEM32\\kernel.appcore.dll", "C:\\WINDOWS\\System32\\bcryptprimitives.dll", "C:\\WINDOWS\\system32\\napinsp.dll", "C:\\WINDOWS\\system32\\pnrpnsp.dll", "C:\\WINDOWS\\system32\\wshbth.dll", "C:\\WINDOWS\\system32\\NLAapi.dll", "C:\\WINDOWS\\SYSTEM32\\DNSAPI.dll", "C:\\WINDOWS\\System32\\NSI.dll", "C:\\WINDOWS\\System32\\winrnr.dll", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc6.DLL", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc.DLL", "\\\\?\\F:\\li\\zf_exam_new\\zf_exam_new_admin\\node_modules\\_node-sass@4.14.1@node-sass\\vendor\\win32-x64-72\\binding.node"]}