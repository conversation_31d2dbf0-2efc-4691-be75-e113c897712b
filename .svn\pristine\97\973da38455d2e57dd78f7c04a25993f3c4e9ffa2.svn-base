<template>
  <div>
    <div class="div-top">
      <span>学校管理</span>
    </div>
    <el-button type="primary" style="margin: 0 0 10px 10px" @click="openAdd()">添加学校</el-button>
    <div style="margin: 10px">
      <el-table :data="datalist" style="width: 100%" border>
        <el-table-column prop="logo" label="学校logo" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" content="点击图片预览" placement="top">
              <el-image style="width: 100px; height: 100px" :src="baseurl + scope.row.logo" :preview-src-list="[baseurl + scope.row.logo]"></el-image>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="schoolName" label="学校名称" align="center"> </el-table-column>
        <el-table-column prop="schoolSign" label="学校标识" align="center"> </el-table-column>
        <el-table-column prop="frontName" label="前台系统名称" align="center"> </el-table-column>
        <el-table-column prop="backName" label="后台系统名称" align="center"> </el-table-column>
        <el-table-column prop="majorList" label="关联专业" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.majorList && scope.row.majorList.length > 0">
              <span v-for="item in scope.row.majorList">【{{ item.majorName }}】</span>
            </div>
            <div v-else>暂无</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" @click="openEdit(scope.row)" size="small">编辑</el-button>
            <el-button type="danger" @click="toRemove(scope.row.schoolId)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="currentChange" :current-page="pageNum" :page-size="pageSize" layout="total, prev, pager, next" :total="total"> </el-pagination>
      </div>
    </div>
    <el-dialog title="添加学校" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="addDialog" width="700px">
      <el-form :model="addForm" :rules="rules" ref="addForm" :inline="true">
        <el-form-item label-width="110px" label="学校名称" prop="schoolName">
          <el-input v-model="addForm.schoolName" placeholder="请输入学校名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="学校标识" prop="schoolSign">
          <el-input v-model="addForm.schoolSign" placeholder="请输入学校标识" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="前台系统名称" prop="frontName">
          <el-input v-model="addForm.frontName" placeholder="请输入前台系统名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="后台系统名称" prop="backName">
          <el-input v-model="addForm.backName" placeholder="请输入后台系统名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="关联专业" prop="majorIds">
          <el-select v-model="addForm.majorIds" multiple placeholder="请选择关联专业" style="width: 202px" clearable>
            <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label-width="110px" label="logo" prop="logo">
          <div class="avataruploads">
            <div class="avatarupcover avatarupcover-add" @click="$refs.logo_add.click()">
              <i class="el-icon-plus"></i>
            </div>
            <img :src="baseurl + addForm.logo" v-if="addForm.logo != ''" />
            <input ref="logo_add" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="handleLogoAdd($event)" />
          </div>
        </el-form-item>
        <el-form-item label-width="110px" label="banner" prop="banner">
          <div class="avataruploads">
            <div class="avatarupcover avatarupcover-add" @click="$refs.banner_add.click()">
              <i class="el-icon-plus"></i>
            </div>
            <img :src="baseurl + addForm.banner" v-if="addForm.banner != ''" />
            <input ref="banner_add" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="handleBannerAdd($event)" />
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addReset">取 消</el-button>
        <el-button type="primary" @click="toAdd">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑学校" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editDialog" width="700px">
      <el-form :model="editForm" :rules="rules" ref="editForm" :inline="true">
        <el-form-item label-width="110px" label="学校名称" prop="schoolName">
          <el-input v-model="editForm.schoolName" placeholder="请输入学校名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="学校标识" prop="schoolSign">
          <el-input v-model="editForm.schoolSign" placeholder="请输入学校标识" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="前台系统名称" prop="frontName">
          <el-input v-model="editForm.frontName" placeholder="请输入前台系统名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="后台系统名称" prop="backName">
          <el-input v-model="editForm.backName" placeholder="请输入后台系统名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="关联专业" prop="majorIds">
          <el-select v-model="editForm.majorIds" multiple placeholder="请选择关联专业" style="width: 202px" clearable>
            <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label-width="110px" label="logo" prop="logo">
          <div class="avataruploads">
            <div class="avatarupcover avatarupcover-add" @click="$refs.logo_edit.click()">
              <i class="el-icon-plus"></i>
            </div>
            <img :src="baseurl + editForm.logo" v-if="editForm.logo != ''" />
            <input ref="logo_edit" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="handleLogoEdit($event)" />
          </div>
        </el-form-item>
        <el-form-item label-width="110px" label="banner" prop="banner">
          <div class="avataruploads">
            <div class="avatarupcover avatarupcover-add" @click="$refs.banner_edit.click()">
              <i class="el-icon-plus"></i>
            </div>
            <img :src="baseurl + editForm.banner" v-if="editForm.banner != ''" />
            <input ref="banner_edit" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="handleBannerEdit($event)" />
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editReset">取 消</el-button>
        <el-button type="primary" @click="toEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { schoolList, schoolSave, schoolUpdate, schoolRemove } from '@/api/school.js'
import { majorList, updateState, majorSave, majorUpdate, majorRemove } from '@/api/major.js'
import { putProgress } from '@/utils/oss.js'
export default {
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      datalist: [],
      majors: [],
      addDialog: false,
      editDialog: false,
      addForm: {
        schoolName: '',
        frontName: '',
        backName: '',
        schoolSign: '',
        logo: '',
        banner: '',
        majorIds: []
      },
      editForm: {
        schoolId: '',
        schoolName: '',
        frontName: '',
        backName: '',
        schoolSign: '',
        logo: '',
        banner: '',
        majorIds: []
      },
      rules: {
        schoolName: [
          {
            required: true,
            message: '请输入学校名称'
          }
        ],
        frontName: [
          {
            required: true,
            message: '请输入前台系统名称'
          }
        ],
        backName: [
          {
            required: true,
            message: '请输入后台系统名称'
          }
        ],
        schoolSign: [
          {
            required: true,
            message: '请输入学校标识'
          }
        ],
        logo: [
          {
            required: true,
            message: '请上传学校logo'
          }
        ],
        banner: [
          {
            required: true,
            message: '请上传学校banner'
          }
        ],
        majorIds: [
          {
            required: true,
            message: '请选择专业'
          }
        ]
      }
    }
  },
  created() {
    this.getMajors()
    this.getDataList()
  },
  methods: {
    getMajors() {
      majorList({ disabled: 1 }).then((res) => {
        this.majors = res.data
      })
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getDataList()
    },
    getDataList() {
      schoolList({
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }).then((res) => {
        if (res.code == '200') {
          this.datalist = res.data.list
          this.total = res.data.total
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    openAdd() {
      this.addDialog = true
    },
    handleLogoAdd(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/logo/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.addForm.logo = key
      })
    },
    handleBannerAdd(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/banner/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.addForm.banner = key
      })
    },
    toAdd() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm)
          schoolSave(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.addReset()
              this.getDataList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    addReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.addDialog = false
    },
    openEdit(item) {
      var majorIds = []
      if (item.majorList && item.majorList.length > 0) {
        item.majorList.map((item) => {
          majorIds.push(item.majorId)
        })
      }
      this.editForm = Object.assign({}, { majorIds }, item)
      this.editDialog = true
    },
    handleLogoEdit(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/logo/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.editForm.logo = key
      })
    },
    handleBannerEdit(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/banner/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.editForm.banner = key
      })
    },
    toEdit() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.editForm)
          schoolUpdate(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getDataList()
              this.editReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    editReset() {
      this.editDialog = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    toRemove(schoolId) {
      this.$confirm('是否删除该学校?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          schoolRemove({
            schoolId: schoolId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getDataList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>
