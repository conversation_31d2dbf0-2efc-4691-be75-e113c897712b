import request from '@/utils/request'
//问诊类型

/**问诊类型列表 */
export function caseQuestionTypeList(params) {
  return request({
    url: '/caseQuestionType/list',
    method: 'get',
    params
  })
}
/**问诊类型详情 */
export function caseQuestionTypeDetais(params) {
  return request({
    url: '/caseQuestionType/detail',
    method: 'get',
    params
  })
}

/** 添加问诊类型 */
export function caseQuestionTypeAdd(data) {
  return request({
    url: '/caseQuestionType/add',
    method: 'post',
    data
  })
}

/** 修改问诊类型 */
export function caseQuestionTypeUpdate(data) {
  return request({
    url: '/caseQuestionType/update',
    method: 'post',
    data
  })
}

/** 删除问诊类型 */
export function caseQuestionTypeRemove(params) {
  return request({
    url: '/caseQuestionType/remove',
    method: 'DELETE',
    params
  })
}
