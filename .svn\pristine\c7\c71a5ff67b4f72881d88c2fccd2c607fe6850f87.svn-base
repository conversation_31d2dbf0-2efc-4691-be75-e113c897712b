<template>
  <div>
    <el-dialog custom-class="examDetailsDialog" title="考核详情" :visible="detailsDialog" top="70px" width="960px" @close="close">
      <el-descriptions border :column="1">
        <el-descriptions-item label="考试名称">{{ examInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="考试时间">{{ examInfo.startTime }} ~ {{ examInfo.endTime }}</el-descriptions-item>
        <el-descriptions-item label="考试限时">{{ examInfo.time }}分钟</el-descriptions-item>
        <el-descriptions-item label="及格分">{{ examInfo.passScore }}</el-descriptions-item>
        <el-descriptions-item label="考核病例">
          <div class="caseBox">
            <div v-for="item in caseList" :key="item.caseId" class="caseItem">
              <div class="weight">
                <span>分数权重</span>
                <el-select v-model="item.weight" placeholder="选择权重" :disabled="true">
                  <el-option v-for="item in weightOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </div>
              <div class="question" @click="lookCaseDetails(item)">病例详情 <i class="el-icon-arrow-right"></i></div>
              <div class="caseItemInfo">
                <div class="caseItem_top_left">
                  <casePhoto :sex="item.sex" :age="item.age" />
                </div>
                <div class="caseItem_top_right">
                  <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
                    <div class="caseName">{{ item.name }}</div>
                  </el-tooltip>
                  <div class="patientInfo">
                    <span> {{ item.realName }} </span>
                    <svg-icon :icon-class="item.sex === 'M' ? 'nan' : 'nv'"></svg-icon>
                    <span> {{ item.age }} 岁</span>
                  </div>
                  <div class="score">
                    <span>病例总分</span>
                    <span> {{ item.allScore }}分</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="班级学生">
          <div class="checkedStudents">
            <div v-for="item in studentList" :key="item.id">
              <div class="checkedClass">{{ item.className }}</div>
              <ul class="students">
                <li v-for="li in item.children" :key="li.id">
                  <el-tag type="success">{{ li.name }}</el-tag>
                </li>
              </ul>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="primary" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 病例详情 -->
    <LookCaseDetails ref="LookCaseDetails" :showDialog.sync="lookCaseDetailsDialog" />
  </div>
</template>
<script>
import casePhoto from '@/components/casePhoto'
import LookCaseDetails from '@/views/caseExam/components/LookCaseDetails'

export default {
  name: '',
  props: {
    detailsDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    casePhoto,
    LookCaseDetails
  },
  data() {
    return {
      examInfo: {},
      caseList: [],
      studentList: [],
      weightOptions: [
        { value: 10, label: '10%' },
        { value: 20, label: '20%' },
        { value: 30, label: '30%' },
        { value: 40, label: '40%' },
        { value: 50, label: '50%' },
        { value: 60, label: '60%' },
        { value: 70, label: '70%' },
        { value: 80, label: '80%' },
        { value: 90, label: '90%' },
        { value: 100, label: '100%' }
      ],
      lookCaseDetailsDialog: false
    }
  },
  created() {},
  methods: {
    getInfo(data) {
      this.examInfo = { ...data }
      this.examInfo.cases.forEach((item) => {
        this.caseList.push({
          caseId: item.exam_case_id,
          allScore: item.all_score,
          weight: item.weight,
          sex: item.sex,
          name: item.name,
          age: item.age,
          form: item.form,
          mainDemands: item.main_demands
        })
      })
      const parentIds = data.clbumIds.split(',')
      const studentList = []
      parentIds.forEach((item) => {
        studentList.push({
          classId: parseInt(item),
          className: null,
          children: []
        })
      })
      studentList.forEach((li) => {
        data.students.forEach((item) => {
          if (li.classId === item.clbum_id) {
            li.className = item.clbum_name
            li.children.push({
              id: item.student_id,
              name: item.name
            })
          }
        })
      })
      this.studentList = studentList
    },
    // 查看病例详情
    lookCaseDetails(item) {
      console.log(this.examInfo)
      this.lookCaseDetailsDialog = true
      this.$refs['LookCaseDetails'].getCaseDetails(item, 'examDetails')
    },
    close() {
      this.examInfo = {}
      this.caseList = []
      this.studentList = []
      this.$emit('update:detailsDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .examDetailsDialog {
    .el-dialog__body {
      height: 700px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 3px;
      }

      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #d2d2d2;
      }

      // 外面的背景
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
    }
  }
}
// 选择后病例样式
.caseBox {
  display: flex;
  flex-wrap: wrap;
  .caseItem {
    position: relative;
    width: 399px;
    height: 186px;
    padding: 10px 18px;
    margin-top: 10px;
    margin-right: 10px;
    background: #f0f0f0;
    border-radius: 10px;

    .weight {
      & > span {
        margin-right: 12px;
        font-size: 16px;
        font-family:
          Abyssinica SIL,
          Abyssinica SIL;
        font-weight: 400;
        color: #333333;
      }
      .el-select {
        .el-input__inner {
          width: 136px;
          height: 40px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #f4f7ff;
        }
      }
    }
    .question {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 14px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      color: #1890ff;
      cursor: pointer;
    }
    .caseItemInfo {
      display: flex;
      width: 363px;
      height: 108px;
      margin-top: 13px;
      background: #ffffff;
      border-radius: 10px;
      border: 1px solid #ffffff;
      overflow: hidden;
      .caseItem_top_left {
        ::v-deep {
          img {
            width: 100%;
            height: 100%;
            object-fit: scale-down;
          }
        }
      }
      .caseItem_top_right {
        flex: 1;
        padding: 10px;
        line-height: 24px;
        overflow: hidden;
        .caseName {
          font-size: 18px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #000000;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .patientInfo {
          & > span {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #666666;
          }
          .svg-icon {
            margin: 0 4px;
          }
        }
        .score {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 246px;
          height: 42px;
          padding: 0 38px;
          background: #f0f0f0;
          border-radius: 8px;
          & > span:first-of-type {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #333333;
          }
          & > span:last-of-type {
            font-size: 18px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            color: #1890ff;
          }
        }
      }
    }
  }
}
.checkedStudents {
  .checkedClass {
    padding-left: 10px;
    margin-top: 15px;
    font-size: 18px;
    font-weight: bold;
  }
  .students {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    padding-left: 10px;
    margin: 0;
    margin-top: 10px;
    list-style: none;
    li {
      margin-right: 10px;
    }
  }
}
</style>
<style>
.caseNameTooltip {
  font-size: 18px;
}
</style>
