<template>
  <div>
    <div class="div-top">
      <span>专业管理</span>
    </div>
    <el-button type="primary" style="margin: 0 0 10px 10px" @click="openAddRouter()" icon="el-icon-circle-plus-outline">添加专业</el-button>
    <div style="margin: 10px">
      <el-table :data="tableRouter" style="width: 100%" border>
        <el-table-column prop="majorName" label="专业名称" align="center"> </el-table-column>
        <el-table-column prop="description" label="专业描述" align="center"> </el-table-column>
        <el-table-column prop="disabled" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag type="danger" v-if="scope.row.disabled == '0'">禁用</el-tag>
            <el-tag type="success" v-if="scope.row.disabled == '1'">正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button :type="scope.row.disabled == 1 ? 'info' : 'success'" @click="openState(scope.row)" size="small">{{ scope.row.disabled == 1 ? '禁用' : '启用' }}</el-button>
            <el-button type="primary" @click="openEditRouter(scope.row)" size="small">编辑</el-button>
            <el-button type="danger" @click="removeRouter(scope.row.majorId)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="添加专业" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="majorSaveDialog" width="350px">
      <el-form :model="addForm" :rules="rules" ref="addForm">
        <el-form-item label="专业名称" prop="majorName">
          <el-input v-model="addForm.majorName" placeholder="请输入专业名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="专业描述" prop="description">
          <el-input v-model="addForm.description" placeholder="请输入专业描述" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="majorSaveReset">取 消</el-button>
        <el-button type="primary" @click="toAddRouter">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑专业" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editRouterDialog" width="350px">
      <el-form :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="专业名称" prop="majorName">
          <el-input v-model="editForm.majorName" placeholder="请输入专业名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="专业描述" prop="description">
          <el-input v-model="editForm.description" placeholder="请输入专业描述" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editRouterReset">取 消</el-button>
        <el-button type="primary" @click="editRouter">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { majorList, updateState, majorSave, majorUpdate, majorRemove } from '@/api/major.js'
export default {
  data() {
    return {
      tableRouter: [],
      majorSaveDialog: false,
      editRouterDialog: false,
      treeProps: {
        children: 'children',
        hasChildren: null
      },
      addForm: {
        majorName: '',
        description: ''
      },
      editForm: {
        majorId: '',
        majorName: '',
        description: ''
      },
      rules: {
        majorName: [
          {
            required: true,
            message: '请输入专业名称'
          }
        ],
        description: [
          {
            required: true,
            message: '请输入专业描述'
          }
        ]
      }
    }
  },
  created() {
    this.getRouterList()
  },
  methods: {
    getRouterList() {
      majorList({}).then((res) => {
        this.tableRouter = res.data
      })
    },
    openAddRouter() {
      this.majorSaveDialog = true
    },
    toAddRouter() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          majorSave({
            majorName: this.addForm.majorName,
            description: this.addForm.description
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.majorSaveReset()
              this.getRouterList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    majorSaveReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.majorSaveDialog = false
    },
    openEditRouter(item) {
      this.editForm.majorId = item.majorId
      this.editForm.majorName = item.majorName
      this.editForm.description = item.description
      this.editRouterDialog = true
    },
    editRouter() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          majorUpdate({
            majorName: this.editForm.majorName,
            description: this.editForm.description,
            majorId: this.editForm.majorId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getRouterList()
              this.editRouterReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    editRouterReset() {
      this.editRouterDialog = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    openState(item) {
      var message = item.disabled == '1' ? '是否禁用该专业?' : '是否启用该专业?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          updateState({
            majorId: item.majorId,
            state: item.disabled == '1' ? '0' : '1'
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.getRouterList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    removeRouter(majorId) {
      this.$confirm('是否删除该专业?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          majorRemove({
            majorId: majorId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getRouterList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
