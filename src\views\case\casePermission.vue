<template>
  <div>
    <el-dialog title="病例权限" :visible="casePermissionDialog" destroy-on-close top="70px" custom-class="casePermissionDialog" center @open="dialogOpen" @close="permissionAllot">
      <el-card class="class-tree">
        <div slot="header">班级列表</div>
        <el-tree ref="caseTree" class="tree" node-key="id" :data="classList" :props="props" check-on-click-node highlight-current @node-click="handleNodeClick"> </el-tree>
      </el-card>
      <el-card>
        <template v-slot:header>
          <span class="title">病例列表 </span>
          <div v-show="studentId" class="search">
            <el-input v-model="caseName" placeholder="请输入病例名称" clearable @keyup.native.enter="searchCase" @clear="clearSearchCase"></el-input>
            <el-button type="primary" @click="searchCase">查询</el-button>
          </div>
        </template>
        <div v-show="studentId">
          <el-table :data="searchList" ref="caseTableRef" style="width: 100%" header-cell-class-name="el_table_header_cell_class" cell-class-name="el_table_cell_class" @selection-change="set_checkedItem">
            <el-table-column align="center" type="selection" width="80"> </el-table-column>
            <el-table-column align="center" prop="name" label="病例名称" width="width"> </el-table-column>
            <el-table-column align="center" prop="realName" label="病例姓名" width="130"> </el-table-column>
          </el-table>
        </div>
      </el-card>

      <div slot="footer">
        <!-- <el-button @click="casePermissionDialog = false">取 消</el-button> -->
        <el-button type="primary" @click="permissionAllot">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { systemClbumAll } from '@/api/clbum'
import { selectTeacherById } from '@/api/teacher.js'
import { casePower_selectByStudentId, caseAllList, casePowerAdd } from '@/api/case'
export default {
  name: '',
  props: {
    casePermissionDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      casePermissionList: [],
      searchList: [],
      checkedList: [],
      classList: [],
      studentId: null,
      caseName: null,
      props: {
        label: 'name',
        children: 'children'
        // children: 'zones',
        // isLeaf: 'leaf'
      },
      disabledSave: false // 防止在初始化选择时调用保存接口
    }
  },

  created() {},
  methods: {
    dialogOpen() {
      this.getClbumAll()
      this.getCaseAll()
    },
    getClbumAll() {
      selectTeacherById().then(async (res) => {
        const { data } = await systemClbumAll({ schoolId: res.data.schoolId, clbumIds: res.data.clbums.map((item) => item.id) })
        this.classList = data
      })
    },
    // 查询所有病例
    async getCaseAll() {
      const { data } = await caseAllList({ caseType: 1 })
      this.casePermissionList = data
      this.searchList = data
    },
    searchCase() {
      const conformData = this.casePermissionList.filter((item) => {
        if (item.name.includes(this.caseName) || item.name === this.caseName) {
          return item
        }
      })
      this.disabledSave = true // 禁用接口调用
      this.searchList = conformData
      // 搜索后重新设置选中状态
      this.$nextTick(() => {
        this.restoreTableSelection()
        this.disabledSave = false
      })
      console.log(conformData)
    },
    clearSearchCase() {
      this.disabledSave = true // 禁用接口调用
      this.searchList = this.casePermissionList
      // 清空搜索后重新设置选中状态
      this.$nextTick(() => {
        this.restoreTableSelection()
        this.disabledSave = false
      })
    },
    async set_checkedItem(selections) {
      if (!this.studentId) {
        return this.$message.warning('请选择用户')
      }
      if (this.disabledSave) return

      // 在搜索状态下，需要合并选择的数据而不是覆盖
      if (this.caseName && this.caseName.trim()) {
        // 搜索状态下，合并当前选择和之前的选择
        // 移除当前搜索结果中未选中的项目
        const searchResultIds = this.searchList.map((item) => item.caseId)
        const filteredPreviousSelection = this.checkedList.filter((item) => !searchResultIds.includes(item.caseId))

        // 合并选择
        const mergedSelection = [...filteredPreviousSelection, ...selections]
        this.checkedList = mergedSelection

        const caseIds = mergedSelection.map((item) => item.caseId)
        await casePowerAdd({ caseIds: caseIds, studentId: this.studentId })
      } else {
        // 非搜索状态下，直接使用当前选择
        const caseIds = selections.map((item) => item.caseId)
        this.checkedList = selections
        await casePowerAdd({ caseIds: caseIds, studentId: this.studentId })
      }
    },
    async handleNodeClick(val) {
      this.checkedList = []
      this.studentId = val.id
      const { data } = await casePower_selectByStudentId({ studentId: val.id })
      this.disabledSave = true
      // 设置需要被选中的数据
      const checkedCaseIds = data.map((item) => item.caseId)
      const checkedList = this.casePermissionList.filter((item) => {
        return checkedCaseIds.includes(item.caseId)
      })
      this.checkedList = checkedList
      this.setTableChecked(checkedList)
      this.disabledSave = false
    },
    setTableChecked(list) {
      this.$refs['caseTableRef'].clearSelection()
      list.forEach((item) => {
        this.$refs['caseTableRef'].toggleRowSelection(item, true)
      })
    },
    // 恢复表格选中状态（用于搜索后恢复选中）
    restoreTableSelection() {
      if (!this.$refs['caseTableRef'] || !this.checkedList.length) return
 
      this.$refs['caseTableRef'].clearSelection()

      // 找到当前显示列表中需要选中的项目
      const checkedIds = this.checkedList.map((item) => item.caseId)

      this.searchList.forEach((item) => {
        if (checkedIds.includes(item.caseId)) {
          this.$refs['caseTableRef'].toggleRowSelection(item, true)
        }
      })
    },
    async permissionAllot() {
      this.$emit('update:casePermissionDialog', false)
      this.studentId = null
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .casePermissionDialog {
    width: 1016px;
    height: 784px;
    background: #ffffff;
    border-radius: 30px 30px 30px 30px;
    .el-dialog__header {
      .el-dialog__title {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          font-size: 24px;
          font-weight: 600;
          color: #666;
        }
      }
    }
    .el-dialog__body {
      display: flex;
      justify-content: space-between;
      flex: 5;
      padding: 20px 40px;
      .el-card {
        height: 578px;
        background: #ffffff;
        box-shadow: 0px 3px 7px 0px rgba(204, 198, 198, 0.25);
        border-radius: 14px 14px 14px 14px;
        border: 1px solid #edeef2;
        .el-card__header {
          height: 50px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #000000;
        }

        &:last-of-type {
          flex: 1;
          .el-card__header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 10px;
            padding-bottom: 10px;
            .search {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              .el-input {
                flex: 1;
                .el-input__inner {
                  width: 240px;
                  height: 36px;
                  background: #eef0f2;
                  border: none;
                  border-radius: 74px 74px 74px 74px;
                }
              }

              .el-button {
                width: 70px;
                height: 36px;
                padding: 0;
                margin-left: 10px;
                background: #65849a;
                border: none;
                border-radius: 63px 63px 63px 63px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 12px;
                color: #ffffff;
                text-align: center;
              }
            }
          }
          .el-card__body {
            padding: 0;
            padding-top: 9px;
            max-height: 520px;
            overflow: auto;
            &::-webkit-scrollbar {
              width: 3px;
              background: transparent;
            }
            // 里面的滑块
            &::-webkit-scrollbar-thumb {
              background: #274e6a;
            }
            // 外面的背景
            &::-webkit-scrollbar-track-piece {
              background: transparent;
            }
            .el-row {
              display: flex;
              align-items: center;
              // margin: 15px 0;
              height: 35px;
            }
          }
        }
        .el-checkbox {
          margin-right: 5px;
          .el-checkbox__inner {
            width: 20px;
            height: 20px;
            &::after {
              left: 6px;
              height: 11px;
              width: 4px;
            }
          }
        }
      }
      .class-tree {
        width: 340px;
        margin-right: 28px;
        .el-card__body {
          padding: 8px 0 0 0;
          max-height: 520px;
          overflow: auto;
          &::-webkit-scrollbar {
            width: 3px;
          }
          // 里面的滑块
          &::-webkit-scrollbar-thumb {
            background: #274e6a;
          }
          // 外面的背景
          &::-webkit-scrollbar-track-piece {
            background: transparent;
          }
        }
      }
    }
    .el-dialog__footer {
      padding-top: 30px;
      & > div {
        .el-button {
          width: 93px;
          height: 50px;
          background: #274e6a;
          border: none;
          border-radius: 63px 63px 63px 63px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
        }
      }
    }
  }
}

::v-deep {
  .casePermissionDialog {
    .el-tree {
      .el-tree-node__content {
        // padding-left: 19px !important;
        height: 48px;
        .el-tree-node__expand-icon {
          font-size: 17px;
          font-weight: 600;
          color: #576e7f;
        }
        .el-tree-node__expand-icon.is-leaf {
          color: transparent;
        }
        .el-tree-node__label {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #333333;
        }
      }
    }
    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
      background: #f3f5f9;
    }

    .el-table__row::hover {
      .el_table_header_cell_class {
        color: #f3f5f9;
      }
    }
    .el_table_cell_class {
      border: none;
      height: 48px;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
    }
    .el_table_header_cell_class {
      height: 50px;
      background: #e7edf8;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #274e6a;
    }
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background: #274e6a;
      border-color: #274e6a;
    }
    .el-checkbox.is-checked {
      .is-checked {
        .el-checkbox__inner {
          background: #274e6a;
          border-color: #274e6a;
        }
      }
    }
  }
}
</style>
