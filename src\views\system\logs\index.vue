<template>
  <div>
    <div class="div-top">
      <span>登录日志</span>
    </div>
    <div style="margin: 20px">
      <span class="search_label">用户：</span>
      <el-input v-model="userName" style="width: 200px" placeholder="请输入用户名" clearable></el-input>
      <span class="search_label">时间：</span>
      <el-date-picker v-model="startEnd" value-format="yyyy-MM-dd HH:mm:ss" style="width: 380px" type="datetimerange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      <el-button type="primary" @click="search" style="margin-left: 5px">搜索</el-button>
      <el-button type="primary" @click="tomodel" style="margin-left: 5px">操作日志</el-button>
    </div>
    <div class="coursenav-body">
      <el-table :data="treeData" border style="width: 100%">
        <el-table-column align="center" prop="userName" label="用户"> </el-table-column>
        <el-table-column align="center" prop="model" label="操作">
          <template slot-scope="scope"> 登录 </template>
        </el-table-column>
        <el-table-column align="center" prop="ip" label="登录ip"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="操作时间"> </el-table-column>
      </el-table>
      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="current" :page-sizes="[5, 10, 20, 40]" :page-size="limit" layout="total, sizes, prev, pager, next, jumper" :total="totalRow"> </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { logList } from '@/api/logs'
export default {
  data() {
    return {
      userName: '',
      current: 1,
      limit: 10,
      totalRow: null,
      treeData: null,
      startEnd: null,
      stattime: null,
      endtime: null
    }
  },
  created() {
    this.getList()
  },
  mounted() {},
  methods: {
    tomodel() {
      this.$router.replace({
        name: 'logsmodel'
      })
    },
    search() {
      this.current = 1
      this.getList()
    },
    handleCurrentChange(current) {
      this.current = current
      this.getList()
    },
    handleSizeChange(limit) {
      this.current = 1
      this.limit = limit
      this.getList()
    },
    getList() {
      var data = {
        userName: this.userName,
        pageNum: this.current,
        pageSize: this.limit
      }
      if (this.startEnd && this.startEnd.length == 2) {
        data.startDate = this.startEnd[0]
        data.endDate = this.startEnd[1]
      }
      logList(data).then(async (res) => {
        this.treeData = res.data.list
        this.totalRow = res.data.total
      })
    }
  }
}
</script>
