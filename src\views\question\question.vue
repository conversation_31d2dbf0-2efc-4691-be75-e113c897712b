<template>
  <div class="question-container">
    <div class="major-select">
      <div class="major-select__title">选择专业</div>
      <div class="major-select__list">
        <div class="major-select__item" v-for="item in majors" :key="item.majorName" :class="{ active: item.majorId == majorId }" @click="selectItem(item)">
          {{ item.majorName }}
        </div>
      </div>
    </div>
    <div class="topic-manage">
      <div class="topic-manage__search">
        <div class="search-field">
          <span class="search-field__label">题干名称：</span>
          <el-input class="search-field__input" placeholder="请输入题干名称" v-model="question" clearable />
        </div>
        <div class="search-field">
          <span class="search-field__label">题目类型：</span>
          <el-radio-group class="search-field__radio" v-model="questionType" @change="handleCurrentChange(1)">
            <el-radio v-for="item in questionTypes" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </div>
        <div class="search-field">
          <el-button @click="handleCurrentChange(1)">查询</el-button>
          <el-button class="reset-button" @click="research">重置</el-button>
          <el-button icon="el-icon-plus" @click="openAdd">添加题目</el-button>
          <el-button @click="dialogImport = true" icon="el-icon-upload2">批量导入</el-button>
          <el-button @click="exportTopics" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出题目' }}</el-button>
        </div>
      </div>
      <div class="topic-manage__table">
        <el-table :data="topics" row-key="questionId" cell-class-name="tableCellClassName" header-cell-class-name="tableHeaderClassName" @selection-change="selectionChange">
          <el-table-column type="selection" width="60" align="center" :selectable="selectableItem"> </el-table-column>
          <el-table-column prop="question" label="题干" width="280" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.questionType != 'COMPATIBILITY'">
                <span class="editor_box" v-html="scope.row.question"></span>
              </span>
              <div v-else>
                <div class="list_question" v-for="(item, index) in getQuestion(scope.row)" :key="index">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="questionType" label="题型" align="center" width="90px">
            <template v-slot="{ row }">
              <span> {{ row.questionType | questionTypeFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度" align="center" width="90px">
            <template v-slot="{ row }">
              <span>{{ difficultyFilter(row.difficulty) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="majorName" label="所属专业" align="center"> </el-table-column>
          <el-table-column prop="disabled" label="状态" align="center" width="80px">
            <template slot-scope="scope">
              <el-tag type="danger" v-if="scope.row.disabled == '0'">禁用</el-tag>
              <el-tag type="success" v-if="scope.row.disabled == '1'">正常</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" width="210px"> </el-table-column>
          <el-table-column prop="createUserName" label="创建人" align="center" width="120px"> </el-table-column>
          <el-table-column label="操作" align="center" width="370">
            <template v-slot="{ row }">
              <div class="button-group">
                <el-button :type="row.disabled == 1 ? 'danger' : 'success'" :icon="row.disabled == 1 ? 'el-icon-remove-outline' : 'el-icon-circle-check'" @click="openState(row)" size="small">{{ row.disabled == 1 ? '禁用' : '启用' }}</el-button>
                <el-button type="primary" icon="el-icon-document" @click="openView(row)" size="small">查看</el-button>
                <el-button type="primary" icon="el-icon-edit-outline" @click="openEdit(row)" size="small">编辑</el-button>
                <el-button type="primary" icon="el-icon-delete" @click="openDelete(row)" size="small">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="topic-manage__pagination">
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-size="pageSize" layout="total,  prev, pager, next" :total="total"> </el-pagination>
      </div>
    </div>

    <el-dialog title="批量导入" custom-class="batch-export-dialog" top="75px" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="dialogImport">
      <el-form :model="importUserForm" :rules="importUserRules" ref="importUserValidate" :inline="false">
        <el-form-item label="所属专业:" prop="majorId" label-width="110px">
          <el-select v-model="importUserForm.majorId" placeholder="请选择所属专业">
            <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传文件:" prop="excelFile" label-width="110px" class="upload-item">
          <el-upload style="text-align: center" ref="upload" action="" drag :limit="1" :on-change="fileChange" :before-remove="fileRemove" accept=".doc,.docx,.xls,.xlsx" :auto-upload="false">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">点击或将文件拖拽到这里上传</div>
            <div class="el-upload__message">请下载模板，并严格按照模板格式整理数据后上传；只能上传xls/xlsx格式文件</div>

            <div class="el-upload__template">
              <el-button type="text" @click.stop="downloadTemplate(1)">下载Word模板</el-button>
              <el-button type="text" @click.stop="downloadTemplate(2)">下载Excel模板</el-button>
            </div>
            <p class="el-upload__tip" slot="tip">*注意：请勿重复导入相同数据</p>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button plain @click="resetImport">取 消</el-button>
        <el-button type="success" @click="submitUpload">导入</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="getType(form)" custom-class="view_dialog" top="70px" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="questionView">
      <questionview :form="form" :letters="letters" :difficultys="difficultys" v-if="questionView"></questionview>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="questionView = false">确定</el-button>
      </div>
    </el-dialog>
    <!-- 新增试题 -->
    <Add ref="addTopicRef" @refreshList="getList" />
  </div>
</template>

<script>
import { questionList, selectQuestionDetail, saveQuestion, updateQuestion, removeQuestion, updateState, importWordQuestion, importQuestion, questionExport } from '@/api/question.js'
import { selectTeacherById } from '@/api/teacher.js'
import { putProgress } from '@/utils/oss.js'
import questionview from '@/views/question/view/index'
import { questionTypes } from '@/filters'
import add from './components/add'

export default {
  components: {
    questionview,
    Add: add
  },
  data() {
    return {
      userinfo: {},
      majors: [],
      majorId: '',
      questionType: '',
      question: '',
      questionUse: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      dialogImport: false,
      questionView: false,
      form: {},
      multipleSelect: [],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,

      importUserForm: {
        questionUse: 0,
        majorId: '',
        excelFile: ''
      },
      importUserRules: {
        excelFile: [
          {
            required: true,
            message: '请选择导入文件'
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  computed: {
    difficultyFilter() {
      return (val) => {
        return this.difficultys.filter((item) => item.value == val)[0].name
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    selectableItem(row, index) {
      if (row.questionType == 'COMPATIBILITY' || row.questionType == 'COMPREHENSIVE') {
        return false
      } else {
        return true
      }
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    getType(form) {
      var questionTypes = this.questionTypes
      var questionTypeName = ''
      if (form && form.questionType) {
        questionTypes.map((item) => {
          if (form.questionType == item.value) {
            questionTypeName = item.name
          }
        })
      }
      return questionTypeName || '题目详情'
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (res.data.majors && res.data.majors.length > 0) {
          this.majorId = res.data.majors[0].majorId
          this.getList()
        }
      })
    },
    selectItem(item) {
      if (this.majorId != item.majorId) {
        this.majorId = item.majorId
        this.research()
      }
    },
    research() {
      this.questionType = ''
      this.question = ''
      this.questionUse = ''
      this.pageNum = 1
      this.getList()
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getList()
    },
    getList() {
      var data = {
        majorId: this.majorId,
        questionUse: this.questionUse,
        question: this.question ? this.question : null,
        questionType: this.questionType ? this.questionType : null,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    //导入试题
    fileChange(file) {
      this.importUserForm.excelFile = file.raw
    },
    fileRemove(file) {
      this.importUserForm.excelFile = ''
    },
    resetImport() {
      this.dialogImport = false
      this.importUserForm.excelFile = ''
      this.$refs.upload.clearFiles()
      this.$refs.importUserValidate.clearValidate()
      this.$refs.importUserValidate.resetFields()
    },
    submitUpload() {
      this.$refs.importUserValidate.validate((valid) => {
        if (valid) {
          if (this.importUserForm.excelFile == '') {
            this.$message({
              message: '文件不能为空',
              type: 'error'
            })
            return
          } else {
            const loading = this.$loading({
              lock: true,
              text: '正在导入请稍等···',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            var formData = new FormData()
            var filename = this.importUserForm.excelFile.name
            var filetype = filename.substring(filename.lastIndexOf('.') + 1)
            formData.append('file', this.importUserForm.excelFile)
            formData.append('questionUse', this.importUserForm.questionUse)
            formData.append('majorId', this.importUserForm.majorId)
            formData.append('schoolId', this.userinfo.schoolId)
            if (filetype == 'doc' || filetype == 'docx') {
              importWordQuestion(formData).then(async (res) => {
                if (res.code == '200') {
                  this.$message({
                    message: '导入成功!',
                    type: 'success'
                  })
                } else {
                  this.$message({
                    message: res.message,
                    type: 'error'
                  })
                }
                loading.close()
                this.resetImport()
                this.getList()
              })
            }
            if (filetype == 'xls' || filetype == 'xlsx') {
              importQuestion(formData).then(async (res) => {
                if (res.code == '200') {
                  this.$message({
                    message: '导入成功!',
                    type: 'success'
                  })
                } else {
                  this.$message({
                    message: res.message,
                    type: 'error'
                  })
                }
                loading.close()
                this.resetImport()
                this.getList()
              })
            }
          }
        }
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    exportTopics() {
      var multipleSelect = this.multipleSelect
      var questionIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          questionIds.push(item.questionId)
        })
      }
      questionExport({
        questionIds: questionIds && questionIds.length > 0 ? questionIds : null,
        majorId: this.majorId
      })
    },
    downloadTemplate(type) {
      if (type == 1) {
        var download = document.createElement('a')
        download.href = this.baseurl + 'zxks/templates/question.docx'
        $('body').append(download)
        download.click()
      }
      if (type == 2) {
        var download = document.createElement('a')
        download.href = this.baseurl + 'zxks/templates/question.xls'
        $('body').append(download)
        download.click()
      }
    },
    openView(item) {
      selectQuestionDetail({
        questionId: item.questionId
      }).then((res) => {
        this.form = Object.assign({}, item, res.data)
        console.log(this.form)

        this.questionView = true
      })
    },
    openAdd() {
      this.$refs['addTopicRef'].dialogVisible = true
      // this.$router.replace({
      //   name: 'questionadd'
      // })
    },
    openEdit(item) {
      this.$refs['addTopicRef'].dialogVisible = true
      this.$refs['addTopicRef'].questionId = item.questionId
    },
    openState(item) {
      var message = item.disabled == '1' ? '是否禁用该试题?' : '是否启用该试题?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'generalConfirm'
      })
        .then(() => {
          updateState({
            questionId: item.questionId,
            state: item.disabled == '1' ? '0' : '1'
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.getList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    openDelete(item) {
      this.$confirm('此操作将永久删除此试题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'generalConfirm'
      })
        .then(() => {
          removeQuestion({
            questionId: item.questionId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.question-container {
  display: flex;
  width: 100%;
  height: 100%;
  .major-select {
    width: 300px;
    height: 100%;
    border-right: 1px solid #e4e4e4;
    &__title {
      padding: 20px 0 30px 38px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #303030;
    }
    &__item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 60px;
      padding-left: 38px;
      cursor: pointer;
      &:hover {
        background: #f6f8fa;
      }
    }
    &__item.active {
      background: #f6f8fa;
      color: #274e6a;
      font-weight: 600;
    }
  }
  .topic-manage {
    position: relative;
    flex: 1;
    height: 100%;
    padding-left: 20px;
    padding-top: 30px;
    padding-right: 30px;
    overflow: auto;
    &__search {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      .search-field {
        display: flex;
        align-items: center;
        margin-right: 30px;
        &__label {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #666666;
        }
        &__input {
          width: 300px;
          height: 45px;
          ::v-deep {
            .el-input__inner {
              width: 100%;
              height: 100%;
              border-radius: 74px;
              background: #eef0f2;
              border: none;
              color: #333333;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              &::placeholder {
                color: #cccccc;
              }
            }
            .el-input__suffix {
              top: 3px;
              right: 10px;
              .el-input__suffix-inner {
                .el-icon-circle-close {
                  font-size: 20px;
                }
              }
            }
          }
        }
        &__radio {
          display: flex;
          ::v-deep {
            .el-radio {
              display: flex;
              align-items: center;
              margin-right: 40px;
              &:last-of-type {
                margin-right: 0;
              }
              .el-radio__inner {
                width: 16px;
                height: 16px;
                background: #fff;
                border-color: #b1b1b1;
              }
              .el-radio__label {
                padding-left: 6px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 20px;
                color: #b1b1b1;
              }
            }

            .el-radio__input.is-checked .el-radio__inner {
              &::after {
                background: #274e6a;
                width: 10px;
                height: 10px;
              }
            }
            .el-radio__input.is-checked + .el-radio__label {
              color: #274e6a;
            }
          }
        }
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 45px;
          padding: 0 18px;
          margin-right: 20px;
          background: #65849a;
          border-radius: 63px 63px 63px 63px;
          border: none;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          &:first-of-type {
            margin-right: 10px;
          }
        }
        .reset-button {
          background: #fff;
          border: 1px solid #65849a;
          color: #65849a;
        }
      }
    }
    &__table {
      .el-table {
        &::before {
          display: none;
        }
        ::v-deep {
          .tableCellClassName {
            height: 55px;
            background: #f6f8fa;
            border-bottom: 4px solid #fff;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            &:first-of-type {
              border-radius: 8px 0 0 8px;
            }
            &:nth-of-type(9) {
              border-radius: 0 8px 8px 0;
            }

            .el-checkbox {
              &__input.is-checked {
                .el-checkbox__inner {
                  background-color: #274e6a;
                  border-color: #274e6a;
                }
              }
              &__inner {
                width: 28px;
                height: 28px;
                border-radius: 4px 4px 4px 4px;
                &::after {
                  width: 8px;
                  height: 14px;
                  left: 8px;
                  top: 2px;
                  border-width: 2px;
                }
              }
            }
          }
          .tableHeaderClassName {
            height: 55px;
            border-bottom: 4px solid #fff;
            background: #f6f8fa;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #999999;
            &:first-of-type {
              border-radius: 8px 0 0 8px;
            }
            &:nth-of-type(9) {
              border-radius: 0 8px 8px 0;
            }
            .el-checkbox {
              &__input.is-checked,
              &__input.is-indeterminate {
                .el-checkbox__inner {
                  background-color: #274e6a;
                  border-color: #274e6a;
                }
              }
              &__inner {
                width: 28px;
                height: 28px;
                border-radius: 4px 4px 4px 4px;
                &::after {
                  width: 8px;
                  height: 14px;
                  left: 8px;
                  top: 2px;
                  border-width: 2px;
                }
                &::before {
                  width: 14px;
                  top: 12px;
                  left: 50%;
                  transform: translateX(-50%) scale(1);
                }
              }
            }
          }
          .el-tag {
            position: relative;
            left: -5px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 66px;
            height: 38px;
            padding: 0;
            border-radius: 8px 8px 8px 8px;

            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            &.el-tag--success {
              border-color: #c2dfc6;
              background: #e2efe7;
              color: #33a141;
            }
            &.el-tag--danger {
              border-color: #ffd3d3;
              background: #f3e7e9;
              color: #dd4b4b;
            }
          }
          .button-group {
            display: flex;
            align-items: center;
            justify-content: space-around;
          }
          .el-button {
            display: flex;
            align-items: center;
            justify-content: center;

            width: 78px;
            height: 38px;
            padding: 0;
            margin: 0;
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #e2e2e2;

            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            i {
              font-size: 16px;
              margin-right: 4px;
            }
            span {
              margin-left: 0;
            }
            &--success {
              color: #33a141;
            }
            &--danger {
              color: #d94444;
            }
            &--primary {
              color: #3381b9;
            }
          }
        }
      }
    }
    &__pagination {
      position: absolute;
      left: 50%;
      bottom: 30px;
      transform: translateX(-50%);
      ::v-deep {
        .el-pagination.is-background .el-pager li:not(.disabled).active {
          background-color: #65849a;
          color: #eef0f2;
        }
        .el-pagination.is-background .el-pager li {
          min-width: 40px;
          height: 40px;
          line-height: 40px;
          border-radius: 10px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          background-color: #eef0f2;
          color: #65849a;
        }
        .el-pagination.is-background .btn-prev,
        .el-pagination.is-background .btn-next {
          min-width: 40px;
          height: 40px;
          line-height: 40px;
          border-radius: 10px;
          background-color: #eef0f2;
          color: #65849a;
          font-size: 16px;
        }
        .el-pagination.is-background .btn-prev:disabled {
          color: rgba($color: #65849a, $alpha: 0.3);
        }
        .el-pagination__total {
          height: 40px;
          line-height: 40px;
          color: #fff;
          font-size: 15px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.el-tooltip__popper {
  padding: 10px;
  max-width: 392px;

  font-family: PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #f6f8fa;
  line-height: 26px;
}

.batch-export-dialog {
  position: relative;
  width: 1101px;
  height: 762px;
  background: #ffffff;
  border-radius: 30px 30px 30px 30px;
  padding: 0 34px 0 44px;

  .el-dialog__header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0 0 0;
    .el-dialog__title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
    }
    .el-dialog__headerbtn {
      top: 36px;
      right: 30px;
      .el-dialog__close {
        font-size: 24px;
        color: #666666;
        font-weight: 600;
      }
    }
  }
  .el-dialog__body {
    padding-top: 60px;
    .el-form {
      &-item {
        display: flex;
        align-items: center;
        &__label {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #555555;
        }
        &__content {
          flex: 1;
          margin-left: 0 !important;
          .el-select {
            width: 507px;
            height: 50px;
            .el-input {
              width: 100%;
              height: 100%;
              .el-input__inner {
                width: 100%;
                height: 100%;
                background: #e9e9e9;
                border-radius: 10px 10px 10px 10px;
                border: none;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 16px;
                color: #333333;
              }
            }
          }
          .el-upload {
            width: 100%;
            height: 233px;
            border-radius: 8px 8px 8px 8px;
            .el-upload-dragger {
              display: flex;
              flex-direction: column;
              width: 100%;
              height: 100%;
              border: none;
              background: #f8f8f8;
              .el-icon-upload {
                color: #3381b9;
              }
              .el-upload__text {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
              }
              .el-upload__message {
                margin-top: 0;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #777777;
              }
            }
          }
          .el-upload__tip {
            text-align: left;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #fc0c0c;
          }
          .el-upload-list {
            .el-upload-list__item-name {
              color: #333333;
              font-size: 16px;
              i {
                color: #333333;
                font-size: 16px;
              }
            }
          }
        }
      }
      .upload-item {
        align-items: flex-start;
        margin-top: 40px;
      }
    }
  }
  .el-dialog__footer {
    position: absolute;
    right: 54px;
    bottom: 33px;
    padding: 0;
    & > div {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 93px;
        height: 50px;
        background: #274e6a;
        border: none;
        border-radius: 63px 63px 63px 63px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #ffffff;
      }
      .el-button.is-plain {
        background: #fff;
        border: 1px solid #274e6a;
        color: #274e6a;
      }
    }
  }
}
.view_dialog {
  width: 1170px;
  height: 820px;
  background: #ffffff;
  padding: 0 34px 0 44px;

  border-radius: 30px 30px 30px 30px;
  .el-dialog__header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0 0 0;
    .el-dialog__title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
    }
    .el-dialog__headerbtn {
      top: 36px;
      right: 30px;
      .el-dialog__close {
        font-size: 24px;
        color: #666666;
        font-weight: 600;
      }
    }
  }
  .el-dialog__body {
    width: 100%;
    height: 622px;
    margin-top: 35px;
    padding-top: 25px;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    border: 1px solid #dddee3;
    overflow: auto;
    &::-webkit-scrollbar {
      background: transparent;
    }
  }
  .el-dialog__footer {
    padding-top: 27px;
    display: flex;
    justify-content: flex-end;
    .el-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 93px;
      height: 50px;
      background: #274e6a;
      border: none;
      border-radius: 63px 63px 63px 63px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
    }
  }
}
</style>
