<template>
  <div class="div-user-sum">
    <div class="div-top">
      <span>角色管理</span>
    </div>
    <el-button type="primary" style="margin: 0 0 10px 10px" @click="roleSaveDialog = true" icon="el-icon-circle-plus-outline">添加角色</el-button>
    <div style="margin: 10px">
      <el-table :data="tableRole" border>
        <el-table-column prop="name" label="角色名称" align="center"> </el-table-column>
        <el-table-column prop="number" label="角色标识" align="center"> </el-table-column>
        <el-table-column prop="schoolName" label="所属学校" align="center"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"> </el-table-column>
        <el-table-column prop="description" label="描述" align="center"> </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" @click="openEdit(scope.row)" size="small">编辑</el-button>
            <el-button type="danger" @click="removeRole(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="width: 100%; margin: 15px 0; text-align: center" v-show="tableRole && tableRole.length > 0">
        <el-pagination background layout="total" :total="tableRole.length"> </el-pagination>
      </div>
    </div>
    <!-- 添加角色 -->
    <el-dialog title="添加角色" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="roleSaveDialog" width="380px">
      <el-form :model="addForm" :rules="rules" ref="addForm">
        <el-form-item label="角色名称" prop="name" label-width="90px">
          <el-input v-model="addForm.name" placeholder="请输入角色名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="角色标识" prop="number" label-width="90px">
          <el-input v-model="addForm.number" placeholder="请输入角色标识" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="所属学校" prop="schoolId" label-width="90px">
          <el-select v-model="addForm.schoolId" placeholder="请选择所属学校" style="width: 250px" clearable>
            <el-option v-for="item in schools" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色描述" prop="description" label-width="90px">
          <el-input v-model="addForm.description" placeholder="请输入角色描述" maxlength="50" clearable> </el-input>
        </el-form-item>
        <el-form-item label="角色权限" prop="permissions" label-width="90px">
          <div class="eltreeBox">
            <el-tree :data="menuData" node-key="id" :props="props" show-checkbox ref="addTree" @check-change="roleSaveChange"> </el-tree>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetAdd">取 消</el-button>
        <el-button type="primary" @click="roleSave" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑角色窗口 -->
    <el-dialog title="编辑角色" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editRoleDialog" width="380px" @opened="editOpend">
      <el-form :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="角色标识" prop="number" label-width="90px">
          <el-input v-model="editForm.number" placeholder="请输入角色标识" maxlength="20" readonly clearable> </el-input>
        </el-form-item>
        <el-form-item label="角色名称" prop="name" label-width="90px">
          <el-input v-model="editForm.name" placeholder="请输入角色名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="所属学校" prop="schoolId" label-width="90px">
          <el-select v-model="editForm.schoolId" placeholder="请选择所属学校" style="width: 250px" clearable>
            <el-option v-for="item in schools" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色描述" prop="description" label-width="90px">
          <el-input v-model="editForm.description" placeholder="请输入角色描述" maxlength="50" clearable> </el-input>
        </el-form-item>
        <el-form-item label="角色权限" prop="permissions" label-width="90px">
          <div class="eltreeBox">
            <el-tree :default-expand-all="true" :data="menuData" :check-strictly="checkStrictly" node-key="id" :props="props" show-checkbox ref="editTree" @check-change="editRoleChange"> </el-tree>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetEdit">取 消</el-button>
        <el-button type="primary" @click="editRole">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { roleList, roleSave, roleUpdate, roleRemove } from '@/api/role.js'
import { permissionTree, permissionSave, permissionUpdate, permissionRemove } from '@/api/permission.js'
import { schoolList, schoolSave, schoolUpdate, schoolRemove } from '@/api/school.js'
export default {
  data() {
    return {
      menuData: [],
      schools: [],
      tableRole: [],
      dataloading: false,
      roleSaveDialog: false,
      editRoleDialog: false,
      checkStrictly: true,
      multipleSelect: [],
      props: {
        label: 'name',
        value: 'id'
      },
      addForm: {
        name: '',
        number: '',
        description: '',
        schoolId: '',
        permissions: []
      },
      editForm: {
        roleId: '',
        name: '',
        number: '',
        schoolId: '',
        description: '',
        permissions: []
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入角色名称'
          }
        ],
        number: [
          {
            required: true,
            message: '请输入角色标识'
          }
        ],
        schoolId: [
          {
            required: true,
            message: '请选择所属学校'
          }
        ],
        description: [
          {
            required: true,
            message: '请输入角色描述'
          }
        ]
      }
    }
  },
  created() {
    this.getRoleList()
    this.getSchoolList()
    this.getRouterList()
  },
  methods: {
    getRouterList() {
      permissionTree({}).then(async (res) => {
        this.menuData = res.data
      })
    },
    getSchoolList() {
      schoolList({
        pageNum: 1,
        pageSize: 200
      }).then(async (res) => {
        this.schools = res.data.list
      })
    },
    getRoleList() {
      roleList({}).then(async (res) => {
        this.tableRole = res.data
      })
    },
    roleSaveChange() {
      var checkedNodes = this.$refs.addTree.getCheckedNodes(false, true)
      var checkedKeys = []
      checkedNodes.map((item) => {
        checkedKeys.push(item.id)
      })
      this.addForm.permissions = checkedKeys
    },
    roleSave() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm)
          roleSave(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.resetAdd()
              this.getRoleList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    resetAdd() {
      this.$refs.addTree.setCheckedNodes([])
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.roleSaveDialog = false
    },
    openEdit(item) {
      this.editForm = Object.assign({}, item)
      this.editRoleDialog = true
      this.checkStrictly = true
    },
    editOpend() {
      this.$refs.editTree.setCheckedKeys(this.editForm.permissions)
      this.checkStrictly = false
    },
    editRoleChange() {
      var checkedNodes = this.$refs.editTree.getCheckedNodes(false, true)
      var checkedKeys = []
      checkedNodes.map((item) => {
        checkedKeys.push(item.id)
      })
      this.editForm.permissions = checkedKeys
    },
    editRole() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.editForm)
          roleUpdate(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.resetEdit()
              this.getRoleList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    resetEdit() {
      this.editForm.permissions = []
      this.editForm.roleId = ''
      this.editForm.name = ''
      this.editForm.number = ''
      this.editForm.description = ''
      this.$refs.editForm.clearValidate()
      this.$refs.editTree.setCheckedNodes([])
      this.editRoleDialog = false
    },
    removeRole(item) {
      this.$confirm('是否删除该角色?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          roleRemove({
            roleId: item.roleId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
            this.getRoleList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
