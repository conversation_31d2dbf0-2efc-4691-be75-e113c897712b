<template>
  <div class="ChartGpa" ref="ChartGpa"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
export default {
  name: 'ChartGpa',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },

  methods: {
    init(data) {
      // 转换数据为 ECharts 需要的格式
      var clbumNames = data.map(function (item) {
        return item.clbumName
      })
      var avgScores = data.map(function (item) {
        return item.avgScore
      })
      var testCounts = data.map(function (item) {
        return item.testCount
      })

      // ECharts 柱状图配置
      var option = {
        title: {
          text: '班级平均分统计',
          left: '20px',
          top: '20px',
          textStyle: {
            color: '#333333',
            fontSize: 20
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // 提示框背景颜色为黑色的50%透明度
          borderColor: 'transparent',
          borderRadius: 10,
          formatter: function (params) {
            var idx = params[0].dataIndex // 获取当前柱状图的索引
            return params[0].name + '<br/>平均分：' + params[0].value + '<br/>参考人数：' + testCounts[idx] // 使用对应索引的参考人数
          },
          textStyle: {
            color: '#FFFFFF', // 设置 tooltip 文字颜色为白色
            fontSize: 16
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '120px',
          right: '100px',
          top: '100px',
          bottom: '40px' // 调整网格距离底部的距离
          // 额外配置，确保图形显示完整
        },
        xAxis: {
          type: 'category',
          data: clbumNames,
          axisTick: {
            show: false // 刻度标去掉
          },
          axisLabel: {
            color: '#999999',
            fontSize: 16,
            margin: 10 // x轴的文字距离图形的距离
          }
          // 额外配置，确保图形显示完整
        },
        yAxis: {
          type: 'value',
          name: '分', // y轴顶部加一个文字“分”
          nameTextStyle: {
            color: '#999999',
            fontSize: 16,
            padding: [0, 30, 10, 0] // 调整文字位置
          },
          axisLabel: {
            color: '#999999',
            fontSize: 16
          }
          // 额外配置，确保图形显示完整
        },
        series: [
          {
            data: avgScores,
            type: 'bar',
            showBackground: false, // 柱形图后面的灰色阴影去掉
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#1890FF'
                },
                {
                  offset: 1,
                  color: '#86c5ff'
                }
              ])
            }
            // 配置更多的系列（series）属性 ...
          }
        ]
      }

      // 基于准备好的dom，初始化echarts实例并应用配置
      this.chart = echarts.init(this.$refs['ChartGpa'])
      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.ChartGpa {
  width: 100%;
  height: 100%;
}
</style>
