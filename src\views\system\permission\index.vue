<template>
  <div class="div-user-sum">
    <div class="div-top">
      <span>权限管理</span>
    </div>
    <el-button type="primary" style="margin: 0 0 10px 10px" @click="openAddRouter(0)" icon="el-icon-circle-plus-outline">添加根权限</el-button>
    <div style="margin: 10px">
      <el-table :data="tableRouter" row-key="id" border :tree-props="treeProps">
        <el-table-column prop="name" label="权限名称"> </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" @click="openAddRouter(scope.row.id)" size="small">添加下级</el-button>
            <el-button type="primary" @click="openEditRouter(scope.row)" plain size="small">编辑</el-button>
            <el-button type="danger" @click="removeRouter(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="添加权限" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="permissionSaveDialog" width="350px">
      <el-form :model="addForm" :rules="rules" ref="addForm">
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入权限名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="权限标识" prop="number">
          <el-input v-model="addForm.number" placeholder="请输入权限标识，示例：sys:menu" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionSaveReset">取 消</el-button>
        <el-button type="primary" @click="toAddRouter" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑权限" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editRouterDialog" width="350px">
      <el-form :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入权限名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="权限标识" prop="number">
          <el-input v-model="editForm.number" placeholder="请输入权限标识，示例：sys:menu" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editRouterReset">取 消</el-button>
        <el-button type="primary" @click="editRouter">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { permissionTree, permissionSave, permissionUpdate, permissionRemove } from '@/api/permission.js'
export default {
  data() {
    return {
      tableRouter: [],
      dataloading: false,
      permissionSaveDialog: false,
      editRouterDialog: false,
      treeProps: {
        children: 'children',
        hasChildren: null
      },
      addForm: {
        parentId: '',
        name: '',
        number: ''
      },
      editForm: {
        permissionId: '',
        name: '',
        number: ''
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入权限名称'
          }
        ],
        number: [
          {
            required: true,
            message: '请输入权限标识'
          }
        ]
      }
    }
  },
  created() {
    this.getRouterList()
  },
  methods: {
    getRouterList() {
      permissionTree({}).then((res) => {
        this.tableRouter = res.data
      })
    },
    openAddRouter(parentId) {
      this.permissionSaveDialog = true
      this.addForm.parentId = parentId
    },
    toAddRouter() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          permissionSave({
            parentId: this.addForm.parentId,
            name: this.addForm.name,
            number: this.addForm.number
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getRouterList()
              this.permissionSaveReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    permissionSaveReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.permissionSaveDialog = false
    },
    openEditRouter(item) {
      this.editRouterDialog = true
      this.editForm.permissionId = item.id
      this.editForm.name = item.name
      this.editForm.number = item.number
    },
    editRouter() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          permissionUpdate({
            name: this.editForm.name,
            number: this.editForm.number,
            permissionId: this.editForm.permissionId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getRouterList()
              this.editRouterReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    editRouterReset() {
      this.editRouterDialog = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    removeRouter(item) {
      this.$confirm('是否删除该权限及子权限?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          permissionRemove({
            permissionId: item.id
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getRouterList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
