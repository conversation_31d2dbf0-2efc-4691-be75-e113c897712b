<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>base_admin</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>com.aptana.ide.core.unifiedBuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.aptana.projects.webnature</nature>
	</natures>
	<filteredResources>
		<filter>
			<id>0</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-node_modules</arguments>
			</matcher>
		</filter>
		<filter>
			<id>0</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-node_modules</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1589189746726</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-node_modules</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1611735756618</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-node_modules</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1631862846755</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-node_modules</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1644816918417</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-node_modules</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
