<template>
  <div class="app-container">
    <div class="caseBody">
      <el-row type="flex" justify="center" align="middle">
        <div class="goBackButton" @click="goBack">
          <img src="@/assets/case/goBackIcon.png" alt="" />
        </div>
        <span class="moduleTitle">病例库</span>
      </el-row>
      <div class="caseDataContent">
        <el-form ref="form" :model="queryInfo" label-width="100px" inline>
          <el-form-item label="疾病名称:">
            <el-input v-model="queryInfo.name" maxlength="40" placeholder="请输入疾病名称" clearable prefix-icon="el-icon-search" @keydown.native.enter="getList" @clear="getList"></el-input>
          </el-form-item>
          <!-- <el-form-item label="病例类型:" style="margin-left: 10px">
            <el-radio-group v-model="queryInfo.caseType" @change="getList">
              <el-radio :label="1">学习病例</el-radio>
              <el-radio :label="2">考核病例</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="创建类型:" style="margin-left: 10px">
            <el-radio-group v-model="queryInfo.createType" @change="getList">
              <el-radio :label="1">手动添加</el-radio>
              <el-radio :label="2">AI创建</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item style="float: right">
            <div class="btnBox">
              <span class="btn searchButton" @click="getList">查询</span>
              <span class="btn resetButton" @click="reset">重置</span>
              <span class="btn casePermissionButton" style="margin-left: 30px" @click="casePermissionDialog = true">病例权限</span>
              <span class="btn caseAddButton" @click="addDialog = true">手动添加</span>
              <span class="btn aiAddButton" @click="aiCreateCase">
                <img src="@/assets/case/aiIcon.png" alt="" />
                AI添加
              </span>
            </div>
          </el-form-item>
        </el-form>
        <div class="caseContent">
          <el-tabs v-model="activeName">
            <el-tab-pane label="学习病例" name="learn">
              <ul class="learn-case">
                <li v-for="item in list" :key="item.caseId" :class="{ disable: !item.isEnable }">
                  <div class="operateBox">
                    <el-popover popper-class="caseOperatePopover" placement="bottom" width="113px" trigger="click">
                      <div class="eidtBtn" @click="edit(item)">
                        <i class="el-icon-edit"></i>
                        编辑病例
                      </div>
                      <div class="copyBtn" @click="copy(item)">
                        <i class="el-icon-copy-document"> </i>
                        复制病例
                      </div>

                      <div class="delBtn" @click="del(item)">
                        <i class="el-icon-delete"></i>
                        删除病例
                      </div>
                      <span class="operateBtn" slot="reference">
                        <i class="el-icon-more"></i>
                      </span>
                    </el-popover>

                    <div class="switch">
                      <el-switch v-model="item.isEnable" :width="38" :active-value="1" :inactive-value="0" active-color="#182331" inactive-color="#c6c6c6" @change="switchChange($event, item)"> </el-switch>
                    </div>
                  </div>
                  <div class="topBox">
                    <div class="avatarBox">
                      <Avatar :age="item.age" :sex="item.sex" :showSex="false" />
                    </div>
                    <div class="case-type">
                      <div :class="`caseType${item.caseType}`">{{ item.caseType === 1 ? '学习' : '考核' }}病例</div>
                      <img v-if="item.createType === 2" class="aiLogo" src="@/assets/case/aiLogo.png" alt="" />
                    </div>

                    <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
                      <div class="caseName">{{ item.name }}</div>
                    </el-tooltip>

                    <div class="userInfo">
                      <span>{{ item.realName }}</span>
                      <span>{{ item.age }}岁</span>
                    </div>
                  </div>
                </li>
              </ul>
            </el-tab-pane>
            <el-tab-pane label="考核病例" name="exam">
              <ul class="exam-case">
                <li v-for="item in list" :key="item.caseId" :class="{ disable: !item.isEnable }">
                  <div class="operateBox">
                    <el-popover popper-class="caseOperatePopover" placement="bottom" width="113px" trigger="click">
                      <div class="eidtBtn" @click="edit(item)">
                        <i class="el-icon-edit"></i>
                        编辑病例
                      </div>
                      <div class="copyBtn" @click="copy(item)">
                        <i class="el-icon-copy-document"> </i>
                        复制病例
                      </div>

                      <div class="delBtn" @click="del(item)">
                        <i class="el-icon-delete"></i>
                        删除病例
                      </div>
                      <span class="operateBtn" slot="reference">
                        <i class="el-icon-more"></i>
                      </span>
                    </el-popover>
                    <span class="testCodeBtn" @click="startTest(item)">
                      <svg-icon icon-class="testCode"></svg-icon>
                    </span>

                    <div class="switch">
                      <el-switch v-model="item.isEnable" :width="38" :active-value="1" :inactive-value="0" active-color="#182331" inactive-color="#c6c6c6" @change="switchChange($event, item)"> </el-switch>
                    </div>
                  </div>
                  <div class="topBox">
                    <div class="avatarBox">
                      <Avatar :age="item.age" :sex="item.sex" :showSex="false" />
                    </div>
                    <div class="case-type">
                      <div :class="`caseType${item.caseType}`">{{ item.caseType === 1 ? '学习' : '考核' }}病例</div>
                      <img v-if="item.createType === 2" class="aiLogo" src="@/assets/case/aiLogo.png" alt="" />
                    </div>

                    <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
                      <div class="caseName">{{ item.name }}</div>
                    </el-tooltip>

                    <div class="userInfo">
                      <span>{{ item.realName }}</span>
                      <span>{{ item.age }}岁</span>
                    </div>
                    <div class="caseHistory" @click="historyCollect(item)">
                      病史采集
                      <i class="el-icon-right"></i>
                    </div>
                  </div>
                  <div class="bottomBox">
                    <div>
                      <span>{{ item.questionCount }}</span>
                      <span>问题数量</span>
                    </div>
                    <div>
                      <span>{{ item.allScore }}</span>
                      <span>总分</span>
                    </div>
                  </div>
                </li>
              </ul>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="pagination" style="width: 100%; margin: 15px; text-align: center">
          <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total, prev, pager, next" :total="total"> </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增病例 -->
    <AddCase ref="AddCase" :addDialog.sync="addDialog" @success="getList" />
    <!-- 病例权限 -->
    <CasePermission :casePermissionDialog.sync="casePermissionDialog" />
    <!-- 准备测试的弹窗 -->
    <PrepareTestDialog ref="PrepareTestDialogRef" />
  </div>
</template>
<script>
import { caseList, caseCopy, caseRemove, caseUpdate } from '@/api/case'
import AddCase from './add'
import CasePermission from '@/views/case/casePermission'
import Avatar from '@/views/case/Avatar'
import PrepareTestDialog from './testDialogue/prepareTestDialog.vue'
export default {
  name: 'Case',
  components: {
    AddCase,
    CasePermission,
    Avatar,
    PrepareTestDialog
  },
  data() {
    return {
      activeName: 'learn',
      queryInfo: {
        name: null,
        caseType: 1,
        createType: null,
        pageNum: 1,
        pageSize: 12
      },
      list: [],
      total: 0,
      addDialog: false,
      casePermissionDialog: false
    }
  },
  created() {
    this.getList()
  },
  watch: {
    activeName(val) {
      if (val) {
        this.queryInfo.caseType = val === 'learn' ? 1 : 2
        this.getList()
      }
    }
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null,
        caseType: null,
        pageNum: 1,
        pageSize: 12
      }
      this.getList()
    },
    historyCollect(item) {
      this.$router.push(`/case/historyCollect/${item.caseId}`)
    },
    copy(item) {
      this.$confirm('确定要复制该病例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseCopy({ ...item })
          this.$message({
            type: 'success',
            message: '复制成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消复制'
          })
        })
    },
    del(item) {
      this.$confirm('确定要删除该病例吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseRemove({ id: item.caseId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    edit(item) {
      this.$refs['AddCase'].form = { ...item }
      this.addDialog = true
    },
    switchChange(val, item) {
      const typeLable = val ? '开启' : '禁用'
      this.$confirm(`确定要${typeLable}该病例吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          caseUpdate({ caseId: item.caseId, isEnable: val }).then(() => {
            this.$message.success(val ? '开启成功!' : '禁用成功!')
            this.getList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: `已取消${typeLable}`
          })
          this.getList()
        })
    },
    aiCreateCase() {
      this.$router.push('/case/aiCrate')
    },
    // 开始测试
    startTest(item) {
      this.$refs['PrepareTestDialogRef'].open(item)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  padding: 0;
  background: #d5dee8;
  .caseBody {
    height: 100%;
    width: 100%;
    padding: 25px 48px;
    margin: 0 auto;
    .goBackButton {
      position: absolute;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .moduleTitle {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      line-height: 35px;
      text-align: center;
    }
  }
}
::v-deep {
  .caseDataContent {
    .el-tabs {
      width: 100%;
      .el-tabs__nav-wrap::after {
        background-color: #61859f;
      }
      .el-tabs__item {
        padding-left: 30px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #b1c8d6;
      }
      .el-tabs__item.is-active {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #e7f0ff;
      }
      .el-tabs__active-bar {
        height: 4px;
        background: #e7f0ff;
        border-radius: 80px 80px 80px 80px;
      }
      .el-tabs__content {
        padding-top: 15px;
      }
    }
    .el-form {
      display: flex;
      align-items: center;
      // padding-bottom: 20px;
      background: transparent;

      .el-form-item {
        display: flex;
        align-items: center;
        margin-bottom: 0;
        .el-form-item__label {
          display: flex;
          align-items: center;
          height: 50px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #b1b1b1;
        }
        .el-input {
          .el-input__inner {
            width: 249px;
            height: 50px;
            padding-left: 40px;
            background: #394555;
            border-radius: 74px;
            border: none;
            color: #fff;
            font-weight: 500;
            font-size: 20px;
            font-family: PingFang SC;
            &::placeholder {
              color: #a9a9a9;
            }
          }
          .el-input__prefix {
            left: 10px;
            top: 1px;
            font-size: 18px;
          }
          .el-input__suffix {
            top: 3px;
            .el-input__suffix-inner {
              left: 10px;
              .el-icon-circle-close {
                font-size: 24px;
              }
            }
          }
        }

        .el-radio-group {
          display: flex;
          .el-radio {
            display: flex;
            align-items: center;
            .el-radio__inner {
              width: 18px;
              height: 18px;
              background: #242e3b;
              border-color: #e5e5e5;
            }
            .el-radio__label {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              font-size: 20px;
              color: #b1b1b1;
            }
          }

          .el-radio__input.is-checked .el-radio__inner {
            &::after {
              background: #e5e5e5;
              width: 12px;
              height: 12px;
            }
          }
          .el-radio__input.is-checked + .el-radio__label {
            color: #ffffff;
          }
        }
        .btnBox {
          display: flex;
          align-items: center;
          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 24px;
            margin-left: 11px;
            height: 50px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #ffffff;
            border-radius: 63px 63px 63px 63px;
            cursor: pointer;
            transition: all 0.3s;
          }
          .searchButton {
            background: #394555;
            &:hover {
              background: #6287a1;
            }
          }
          .resetButton {
            border: 1px solid #fff;
            &:hover {
              background: #6287a1;
              border-color: #6287a1;
            }
          }
          .casePermissionButton,
          .caseAddButton {
            background: #6990ab;
            &:hover {
              background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
            }
          }
          .aiAddButton {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 18px;
            background: url('~@/assets/case/aiAddBtBg.png') no-repeat;
            background-size: cover;
            img {
              margin-right: 6px;
            }
            &:hover {
              background: url('~@/assets/case/aiAddBtBg_hover.png') no-repeat;
              background-size: cover;
            }
          }
        }
      }
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #9ab0be;
      color: #394555;
    }
    .el-pagination.is-background .el-pager li {
      min-width: 40px;
      height: 40px;
      line-height: 40px;
      border-radius: 10px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      background-color: #394555;
      color: #fff;
    }
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .btn-next {
      min-width: 40px;
      height: 40px;
      line-height: 40px;
      border-radius: 10px;
      background-color: #394555;
      color: #fff;
      font-size: 16px;
    }
    .el-pagination.is-background .btn-prev:disabled {
      color: rgba(255, 255, 255, 0.3);
    }
    .el-pagination__total {
      height: 40px;
      line-height: 40px;
      color: #fff;
      font-size: 15px;
    }
  }
}
.caseDataContent {
  position: relative;
  height: 812px;
  background: linear-gradient(180deg, #26303c 0%, #0b1627 100%);
  border-radius: 30px 30px 30px 30px;
  padding: 30px 0;
  padding-left: 50px;
  margin-top: 20px;
  .caseContent {
    display: flex;
    margin: 0 auto;
    margin-top: 10px;
    .exam-case,
    .learn-case {
      display: flex;
      flex-wrap: wrap;
      padding-right: 45px;
      margin: 0;
      list-style: none;
      li {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 262px;
        height: 275px;
        margin-right: 30px;
        margin-bottom: 30px;
        background: #b1c8d6;
        border-radius: 20px;
        text-align: center;
        transition: all 0.2s ease-in-out;
        overflow: hidden;
        &:nth-of-type(6n) {
          margin-right: 0;
        }
        &:hover {
          transform: translateY(-10px);
        }
        .topBox {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          z-index: 1;
          .avatarBox {
            position: relative;
            width: 70px;
            height: 70px;
            margin-top: 10px;

            .Avatar {
              width: 70px;
              height: 70px;
              ::v-deep {
                img {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }

          .case-type {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 4px;

            .caseType1,
            .caseType2 {
              padding: 4px 5px;
              background: #49df41;
              color: #222d39;
              border-radius: 4px;
              font-size: 12px;
            }
            .caseType2 {
              background: #ff7846;
              color: #fff;
            }
            .aiLogo {
              margin-left: 5px;
              width: 28px;
              height: 18px;
            }
          }

          .caseName {
            width: 95%;
            margin-top: 4px;
            font-family: PingFang SC;
            font-size: 22px;
            color: #222d39;
            white-space: nowrap; /* 确保文本在同一行内显示 */
            overflow: hidden; /* 隐藏溢出容器的文本 */
            text-overflow: ellipsis; /* 使用省略号表示文本溢出部分 */
          }
          .userInfo {
            margin-top: 3px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #4d5b68;
          }
          .caseHistory {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 4px;
            width: 118px;
            height: 40px;
            background: linear-gradient(180deg, #4d5f77 0%, #252f3c 100%);
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            border-radius: 63px;
            cursor: pointer;
            transition: all 0.3s;
            i {
              margin-left: 4px;
            }
            &:hover {
              background: linear-gradient(180deg, #849dbe 0%, #4d5f77 100%);
            }
          }
        }
        .bottomBox {
          position: relative;
          display: flex;
          justify-content: space-around;
          width: 100%;
          height: 100px;
          margin-top: -20px;
          padding-top: 10px;
          background: #65849a;
          &::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 55%;
            height: 33px;
            width: 1px;
            background: #ffffff;
            transform: translate(-50%, -50%);
          }
          & > div {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: PingFang SC;

            & > span:first-of-type {
              font-weight: bold;
              font-size: 26px;
              color: #ffffff;
            }
            & > span:last-of-type {
              margin-top: 4px;
              font-weight: 500;
              font-size: 14px;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
        .operateBox {
          position: absolute;
          top: 12px;
          left: 0;
          width: 100%;
          z-index: 2;
          .operateBtn,
          .testCodeBtn {
            position: absolute;
            left: 12px;
            width: 25px;
            height: 25px;
            line-height: 25px;
            background: #e7f0ff;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            i {
              color: #182331;
            }
          }
          .testCodeBtn {
            left: 43px;

            &:hover {
              background: #fff;
            }
          }
          .switch {
            position: absolute;
            right: 16px;
          }
        }
      }
      .disable {
        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          border-radius: 20px;
          background: rgba($color: #000000, $alpha: 0.5);
        }
      }
    }
    .learn-case {
      li {
        width: 262px;
        height: 220px;
        background: #b1c8d6;
        border-radius: 20px 20px 20px 20px;
        .topBox {
          .case-type {
            margin-top: 12px;
          }
          .caseName {
            margin-top: 13px;
          }
          .userInfo {
            margin-top: 18px;
          }
        }
      }
    }
  }
  .pagination {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
<style lang="scss">
.caseNameTooltip {
  font-size: 18px;
}
.caseOperatePopover {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
  min-width: 100px;
  .eidtBtn,
  .copyBtn,
  .delBtn {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #737880;
    cursor: pointer;
    i {
      font-size: 16px;
      color: #737880;
    }
    &:hover {
      color: #182331;
      i {
        color: #182331;
      }
    }
  }
  .copyBtn {
    margin: 8px 0;
  }
}
</style>
