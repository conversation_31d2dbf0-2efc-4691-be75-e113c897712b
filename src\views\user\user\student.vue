<template>
  <div class="div-user-sum">
    <div class="div-top">
      <span>学生管理</span>
    </div>
    <span class="search_label">用户名:</span>
    <el-input placeholder="请输入用户名" prefix-icon="el-icon-search" v-model="loginName" style="width: 207px; margin-left: 10px" clearable />
    <span class="search_label">姓名:</span>
    <el-input placeholder="请输入姓名" prefix-icon="el-icon-search" v-model="name" style="width: 207px; margin-left: 10px" clearable />
    <span class="search_label">所在班级:</span>
    <el-select v-model="clbumIds" multiple placeholder="请选择所属班级" style="width: 207px">
      <el-option v-for="item in clbums" :key="item.id" :label="item.clbumName" :value="item.id"> </el-option>
    </el-select>
    <el-button type="primary" @click="search" style="margin-left: 5px">搜索</el-button>
    <el-button type="primary" @click="teacherSaveDialog = true" icon="el-icon-circle-plus-outline">添加用户</el-button>
    <el-button type="success" @click="dialogImport = true" icon="el-icon-upload2">导入用户</el-button>
    <el-button type="success" @click="exportUser" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出用户' }}</el-button>
    <div style="margin: 10px">
      <el-table :data="users" border row-key="studentId" @selection-change="selectionChange">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="loginName" label="用户名" align="center"> </el-table-column>
        <el-table-column prop="name" label="姓名" align="center"> </el-table-column>
        <el-table-column prop="disabled" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag type="danger" v-if="scope.row.disabled == '0'">禁用</el-tag>
            <el-tag type="success" v-if="scope.row.disabled == '1'">正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="clbumName" label="所在组织" align="center"> </el-table-column>
        <el-table-column prop="majorName" label="所属专业" align="center"> </el-table-column>
        <el-table-column label="操作" align="center" width="350">
          <template slot-scope="scope">
            <el-button :type="scope.row.disabled == 1 ? 'info' : 'success'" @click="openState(scope.row)" size="small">{{ scope.row.disabled == 1 ? '禁用' : '启用' }}</el-button>
            <el-button type="primary" @click="openEdit(scope.row)" size="small">编辑</el-button>
            <el-button type="danger" @click="openDelete(scope.row.studentId)" size="small">删除</el-button>
            <el-button type="primary" @click="setPassword(scope.row.studentId)" size="small">重置密码</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>
    <!-- 添加用户 -->
    <el-dialog title="添加用户" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="teacherSaveDialog" width="720px">
      <el-form :model="addForm" :rules="rules" ref="addForm" :inline="true" style="width: calc(100% - 20px)">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="loginName" label-width="110px">
              <el-input placeholder="请输入用户名" v-model="addForm.loginName" maxlength="20" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="name" label-width="110px">
              <el-input placeholder="请输入姓名" v-model="addForm.name" maxlength="20" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex" label-width="110px">
              <el-select v-model="addForm.sex" placeholder="请选择性别" style="width: 207px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="mobile" label-width="110px">
              <el-input placeholder="请输入电话号码" v-model="addForm.mobile" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在班级" prop="clbumId" label-width="110px">
              <el-select v-model="addForm.clbumId" placeholder="请选择所属班级" style="width: 207px">
                <el-option v-for="item in clbums" :key="item.id" :label="item.clbumName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属专业" prop="majorId" label-width="110px">
              <el-select v-model="addForm.majorId" placeholder="请选择所属专业" style="width: 207px">
                <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="头像" prop="icon" label-width="110px">
              <div class="avataruploads">
                <div class="avatarupcover avatarupcover-add" @click="$refs.icon_add.click()">
                  <i class="el-icon-plus"></i>
                </div>
                <img :src="addForm.icon" v-if="addForm.icon" />
                <input ref="icon_add" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="iconAdd($event)" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetAdd">取 消</el-button>
        <el-button type="primary" @click="addStudent" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑用户 -->
    <el-dialog title="编辑用户" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editUserDialog" width="720px">
      <el-form :model="editForm" :rules="rules" ref="editForm" :inline="true" style="width: calc(100% - 20px)">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="loginName" label-width="110px">
              <el-input placeholder="请输入用户名" v-model="editForm.loginName" maxlength="20" style="width: 207px" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="name" label-width="110px">
              <el-input placeholder="请输入姓名" v-model="editForm.name" maxlength="20" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex" label-width="110px">
              <el-select v-model="editForm.sex" placeholder="请选择性别" style="width: 207px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="mobile" label-width="110px">
              <el-input placeholder="请输入电话号码" v-model="editForm.mobile" style="width: 207px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在班级" prop="clbumId" label-width="110px">
              <el-select v-model="editForm.clbumId" placeholder="请选择所属班级" style="width: 207px">
                <el-option v-for="item in clbums" :key="item.id" :label="item.clbumName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属专业" prop="majorId" label-width="110px">
              <el-select v-model="editForm.majorId" placeholder="请选择所属专业" style="width: 207px">
                <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="头像" prop="icon" label-width="110px">
              <div class="avataruploads">
                <div class="avatarupcover avatarupcover-add" @click="$refs.icon_edit.click()">
                  <i class="el-icon-plus"></i>
                </div>
                <img :src="editForm.icon" v-if="editForm.icon" />
                <input ref="icon_edit" type="file" accept=".jpg, .png, .JPG, .PNG" style="display: none" @change="iconEdit($event)" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetEdit">取 消</el-button>
        <el-button type="primary" @click="editUser">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入用户 -->
    <el-dialog title="导入" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="dialogImport" width="720px">
      <el-form :model="importUserForm" :rules="importUserRules" ref="importUserValidate" :inline="true">
        <el-form-item label="所在班级" prop="clbumId" label-width="110px">
          <el-select v-model="importUserForm.clbumId" placeholder="请选择所属班级" style="width: 207px">
            <el-option v-for="item in clbums" :key="item.id" :label="item.clbumName" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属专业" prop="majorId" label-width="110px">
          <el-select v-model="importUserForm.majorId" placeholder="请选择所属专业" style="width: 207px">
            <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件" prop="excelFile" label-width="110px">
          <div style="height: 80px">
            <p style="margin: auto 0; text-align: center">请下载模板，并严格按照模板格式整理数据后上传；只能上传xls/xlsx格式文件</p>
            <p style="margin: auto 0; text-align: center; color: red">*注意：请勿重复导入相同数据</p>
          </div>
          <el-upload style="text-align: center" ref="upload" action="" :limit="1" :on-change="fileChange" :before-remove="fileRemove" accept=".xls, .xlsx" :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin: 0 0 0 20px" size="small" plain @click="downloadTemplate">下载模板</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetImport">取 消</el-button>
        <el-button type="success" @click="submitUpload">导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { studentList, addStudent, updateStudent, studentPwdReset, removeStudent, importStudent, studentExport, updateState } from '@/api/student.js'
import { selectTeacherById } from '@/api/teacher.js'
import { majorList } from '@/api/major.js'
import { roleList } from '@/api/role.js'
import { putProgress } from '@/utils/oss.js'
export default {
  name: 'User',
  data() {
    var validatorphone = (rule, value, callback) => {
      let reg = /^1[3456789]\d{9}$/
      if (!reg.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }
    var validatePass1 = (rule, value, callback) => {
      if (this.addForm.passwordcheck) {
        if (value !== this.addForm.passwordcheck) {
          callback(new Error('两次输入密码不一致!'))
        } else {
          callback()
        }
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (this.addForm.password) {
        if (value !== this.addForm.password) {
          callback(new Error('两次输入密码不一致!'))
        } else {
          callback()
        }
      }
    }
    return {
      userinfo: {},
      roles: [],
      clbums: [],
      majors: [],
      users: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loginName: '',
      name: '',
      clbumIds: [],
      clbumIdsAll: [],
      dialogImport: false,
      teacherSaveDialog: false,
      editUserDialog: false,
      dateUserDialog: false,
      multipleSelect: [],
      dataloading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      props: {
        checkStrictly: true,
        value: 'id',
        label: 'name'
      },
      classProps: {
        checkStrictly: true,
        value: 'id',
        label: 'clbumName'
      },
      options: [
        {
          value: 'M',
          label: '男'
        },
        {
          value: 'F',
          label: '女'
        }
      ],
      importUserForm: {
        clbumId: '',
        excelFile: '',
        majorId: ''
      },
      importUserRules: {
        clbumId: [
          {
            required: true,
            message: '请选择班级'
          }
        ],
        excelFile: [
          {
            required: true,
            message: '请选择导入文件'
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ]
      },
      addForm: {
        icon: '',
        loginName: '',
        name: '',
        mobile: '',
        sex: '',
        password: '',
        passwordcheck: '',
        majorId: '',
        clbumId: ''
      },
      editForm: {
        userId: '',
        icon: '',
        loginName: '',
        name: '',
        mobile: '',
        sex: '',
        password: '',
        passwordcheck: '',
        majorId: '',
        clbumId: ''
      },
      rules: {
        loginName: [
          {
            required: true,
            message: '请输入用户名'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入姓名'
          }
        ],
        icon: [
          {
            required: true,
            message: '请上传头像'
          }
        ],
        sex: [
          {
            required: true,
            message: '请选择性别'
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码'
          },
          {
            validator: validatePass1,
            trigger: 'blur'
          }
        ],
        passwordcheck: [
          {
            required: true,
            message: '请确认密码'
          },
          {
            validator: validatePass2,
            trigger: 'blur'
          }
        ],
        mobile: [
          {
            min: 11,
            max: 11,
            message: '请输入11位手机号码',
            trigger: 'blur'
          },
          {
            pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        clbumId: [
          {
            required: true,
            message: '请选择班级'
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getChildren(arrs) {
      arrs.map((item) => {
        if (item.children && item.children.length > 0) {
          this.getChildren(item.children)
        } else {
          item.children = null
        }
      })
      return arrs
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.clbums = res.data.clbums
        var clbumIdsAll = []
        res.data.clbums.map((item) => {
          clbumIdsAll.push(item.id)
        })
        this.clbumIdsAll = clbumIdsAll
        this.getRoleList()
        this.getMajorList()
        this.getUserList()
      })
    },
    downloadTemplate() {
      var downloadUrl = document.createElement('a')
      downloadUrl.href = this.baseurl + 'zxks/templates/user.xls'
      $('body').append(downloadUrl)
      downloadUrl.click()
    },
    getUserList() {
      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        name: this.name ? this.name : null,
        loginName: this.loginName ? this.loginName : null,
        clbumIds: this.clbumIds && this.clbumIds.length > 0 ? this.clbumIds : this.clbumIdsAll,
        schoolId: this.userinfo.schoolId
      }
      studentList(data).then(async (res) => {
        this.users = res.data.list
        this.total = res.data.total
      })
    },
    getRoleList() {
      roleList({
        schoolId: this.userinfo.schoolId
      }).then(async (res) => {
        this.roles = res.data
      })
    },
    getMajorList() {
      majorList({ disabled: 1 }).then(async (res) => {
        this.majors = res.data
      })
    },
    search() {
      this.pageNum = 1
      this.getUserList()
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getUserList()
    },
    sizeChange(pageSize) {
      this.pageNum = 1
      this.pageSize = pageSize
      this.getUserList()
    },
    //导入用户
    fileChange(file) {
      this.importUserForm.excelFile = file.raw
    },
    fileRemove(file) {
      this.importUserForm.excelFile = ''
    },
    resetImport() {
      this.dialogImport = false
      this.$refs.upload.clearFiles()
      this.excelFile = ''
      this.$refs.importUserValidate.clearValidate()
      this.$refs.importUserValidate.resetFields()
    },
    submitUpload() {
      this.$refs.importUserValidate.validate((valid) => {
        if (valid) {
          if (this.importUserForm.excelFile == '') {
            this.$message({
              message: '文件不能为空',
              type: 'error'
            })
            return
          } else {
            const loading = this.$loading({
              lock: true,
              text: '正在导入请稍等···',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            var formData = new FormData()
            formData.append('file', this.importUserForm.excelFile)
            formData.append('clbumId', this.importUserForm.clbumId)
            formData.append('majorId', this.importUserForm.majorId)
            formData.append('schoolId', this.userinfo.schoolId)
            importStudent(formData).then(async (res) => {
              if (res.code == '200') {
                this.$message({
                  message: '导入成功!',
                  type: 'success'
                })
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
              loading.close()
              this.dialogImport = false
              this.importUserForm.excelFile = ''
              this.$refs.upload.clearFiles()
              this.$refs.importUserValidate.clearValidate()
              this.$refs.importUserValidate.resetFields()
              this.getUserList()
            })
          }
        }
      })
    },
    exportUser() {
      var multipleSelect = this.multipleSelect
      var studentIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          studentIds.push(item.studentId)
        })
      }
      studentExport({
        studentIds: studentIds && studentIds.length > 0 ? studentIds : null,
        clbumIds: this.clbumIds && this.clbumIds.length > 0 ? this.clbumIds : this.clbumIdsAll
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    iconAdd(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/icon/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.addForm.icon = res.data.data
        this.$refs.icon_add.value = ''
      })
    },
    addStudent() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm)
          data.schoolId = this.userinfo.schoolId
          addStudent(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getUserList()
              this.resetAdd()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    resetAdd() {
      this.teacherSaveDialog = false
      this.addForm.majorIds = []
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
    },
    openEdit(item) {
      this.editUserDialog = true
      this.editForm = Object.assign({}, item)
    },
    iconEdit(event) {
      const files = event.target.files
      if (!files || files.length <= 0) {
        return
      }
      const file = files[0]
      const key = `zxks/icon/${this.getUUID()}/${file.name}`
      putProgress(key, file).then((res) => {
        this.editForm = Object.assign({}, this.editForm, {
          icon: res.data.data
        })
        this.$refs.icon_edit.value = ''
      })
    },
    editUser() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.editForm)
          updateStudent(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getUserList()
              this.resetEdit()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    resetEdit() {
      this.editUserDialog = false
      this.editForm.majorIds = []
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    openState(item) {
      var message = item.disabled == '1' ? '是否禁用该用户?' : '是否启用该用户?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          updateState({
            studentId: item.studentId,
            state: item.disabled == '1' ? '0' : '1'
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.getUserList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    openDelete(studentId) {
      this.$confirm('是否删除该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeStudent({
            studentId: studentId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getUserList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    setPassword(studentId) {
      this.$confirm('是否重置密码?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          studentPwdReset({
            studentId: studentId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    getUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>
