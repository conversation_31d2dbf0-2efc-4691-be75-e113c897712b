import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters

import permission from '@/directive/permission' //权限自定义指令
Vue.directive('permission', permission)

import '@/utils/editor.js' //富文本    注意：vue.config.js同步添加quill模块

if (window.config.VUE_IS_LOCAL) {
  Vue.prototype.baseurl = window.config.VUE_FILE_BASE_PATH
} else {
  Vue.prototype.baseurl = window.config.VUE_FILE_OSS_PATH
}

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

// register global utility filters
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})
