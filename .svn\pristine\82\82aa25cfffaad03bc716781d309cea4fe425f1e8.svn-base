<template>
  <div>
    <tsingle :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'SINGLE'"></tsingle>
    <tmultiple :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'MULTIPLE'"></tmultiple>
    <tjudge :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'JUDGE'"></tjudge>
    <tcompletion :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'COMPLETION'"></tcompletion>
    <tshort :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'SHORT'" @beforeCorrect="beforeCorrect"></tshort>
    <tcompatiblity :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'COMPATIBILITY'" :splited="splited"></tcompatiblity>
    <tcomprehensive :form="form" :edit="edit" :letters="letters" :difficultys="difficultys" v-if="form.questionType == 'COMPREHENSIVE'" :splited="splited"></tcomprehensive>
  </div>
</template>

<script>
import tsingle from '@/views/volume/correct/view/tsingle'
import tmultiple from '@/views/volume/correct/view/tmultiple'
import tjudge from '@/views/volume/correct/view/tjudge'
import tcompletion from '@/views/volume/correct/view/tcompletion'
import tshort from '@/views/volume/correct/view/tshort'
import tcomprehensive from '@/views/volume/correct/view/tcomprehensive'
import tcompatiblity from '@/views/volume/correct/view/tcompatiblity'
export default {
  components: {
    tsingle,
    tmultiple,
    tjudge,
    tcompletion,
    tshort,
    tcomprehensive,
    tcompatiblity
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    difficultys: {
      type: Array,
      required: true
    },
    letters: {
      type: Array,
      required: true
    },
    splited: {
      type: Boolean,
      required: false,
      default: false
    },
    edit: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    beforeCorrect(data) {
      this.$emit('beforeCorrect', data)
    }
  }
}
</script>
