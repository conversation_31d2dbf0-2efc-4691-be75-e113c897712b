import request from '@/utils/request'
//考核管理

/**考核列表 */
export function caseExamList(params) {
  return request({
    url: '/caseExam/list',
    method: 'get',
    params
  })
}

/** 添加考核 */
export function caseExamAdd(data) {
  return request({
    url: '/caseExam/add',
    method: 'POST',
    data
  })
}

/** 修改考核 */
export function caseExamUpdate(data) {
  return request({
    url: '/caseExam/update',
    method: 'POST',
    data
  })
}

/** 修改考核 */
export function caseExamCopy(data) {
  return request({
    url: '/caseExam/copy',
    method: 'POST',
    data
  })
}

/**考核删除 */
export function caseExamRemove(params) {
  return request({
    url: '/caseExam/remove',
    method: 'DELETE',
    params
  })
}

/**考核详情 */
export function caseExamDetail(params) {
  return request({
    url: '/caseExam/detail',
    method: 'GET',
    params
  })
}

/**判断当前时间下是否有考试 */
export function caseExamTimeJudge(data) {
  return request({
    url: '/caseExam/timeJudge',
    method: 'POST',
    data
  })
}
/**病例考核成绩列表导出 */
export function caseExamListExport(params) {
  return request({
    url: '/caseExam/listExport',
    method: 'GET',
    params
  })
}

/**病例考核成绩 - 考生成绩列表 */
export function caseExamExamStudentList(params) {
  return request({
    url: '/caseExam/examStudentList',
    method: 'GET',
    params
  })
}

/**考生状态修改 */
export function caseExamStudentUpdate(data) {
  return request({
    url: '/caseExam/studentUpdate',
    method: 'POST',
    data
  })
}

/**根据考核病例id查询病例信息及问诊问题 */
export function caseExamSelectExamCaseDetailById(params) {
  return request({
    url: '/caseExam/selectExamCaseDetailById',
    method: 'GET',
    params
  })
}
/**病例考核成绩 - 考核详情统计 */
export function caseExamStudentStatis(params) {
  return request({
    url: '/caseExam/examStudentStatis',
    method: 'GET',
    params
  })
}
/**查询考核班级 */
export function caseExamClbumList(params) {
  return request({
    url: '/caseExam/examClbumList',
    method: 'GET',
    params
  })
}
/**病例考核成绩 - 考生成绩列表(导出) */
export function caseExamExportStudentList(params) {
  return request({
    url: '/caseExam/examExportStudentList',
    method: 'GET',
    params
  })
}
/** 病例考核成绩 - 考生成绩详情 */
export function caseExamStudentDetail(params) {
  return request({
    url: '/caseExam/examStudentDetail',
    method: 'GET',
    params
  })
}
