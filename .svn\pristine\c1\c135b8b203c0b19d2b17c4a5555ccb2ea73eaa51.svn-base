<template>
  <div>
    <el-form :model="form" :rules="rules" ref="theForm">
      <el-form-item :label-width="labelwidth">
        <el-col :span="14" class="small_input">
          <el-alert title="提示：题干输入时，请在需要填空的位置插入‘@’字符，最多6个，超出输入无效" type="error" effect="dark" :closable="false"> </el-alert>
        </el-col>
      </el-form-item>
      <el-form-item label="题干:" :label-width="labelwidth" prop="question">
        <el-col :span="14" class="small_input">
          <editor-more
            :content="form.question"
            @change="
              (data) => {
                contentChange(data, 'question')
              }
            "
          ></editor-more>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="primary" @click="contentChange({ html: '' }, 'question')">清空</el-button>
        </el-col>
      </el-form-item>
      <el-form-item label="答案:" :label-width="labelwidth" prop="answer">
        <el-col :span="14" class="small_input">
          <el-row :gutter="10">
            <el-col :span="8" v-for="(item, index) in form.answers">
              <el-input @input="answerChange(index)" v-model="form.answers[index]" placeholder="请输入答案" style="margin: 0 0 15px 0"></el-input>
            </el-col>
          </el-row>
        </el-col>
      </el-form-item>
      <el-form-item label="顺序答题:" :label-width="labelwidth" prop="sortState">
        <el-radio-group v-model="form.sortState">
          <el-radio :label="item.value" v-for="(item, index) in sortStates">{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="试题分值：" :label-width="labelwidth" prop="scoreType" v-if="getScore">
        <el-radio-group v-model="form.scoreType" @change="socreTypeChange">
          <el-radio :label="1" name="1">自动分值</el-radio>
          <el-radio :label="2" name="2">手动分值</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分数：" :label-width="labelwidth" prop="score" v-if="getScore">
        <el-col :span="22">
          <el-input-number v-model="form.score" :min="0" :step="1" step-strictly label="请输入分数" :disabled="form.scoreType != 2"></el-input-number>
        </el-col>
      </el-form-item>
      <el-form-item label="解析:" :label-width="labelwidth" prop="analysis">
        <el-col :span="14" class="small_input">
          <editor-more
            :content="form.analysis"
            @change="
              (data) => {
                contentChange(data, 'analysis')
              }
            "
          ></editor-more>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="primary" @click="contentChange({ html: '' }, 'analysis')">清空</el-button>
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import EditorMore from '@/components/Editor/index.vue'
export default {
  components: {
    EditorMore
  },
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({})
    },
    letters: {
      type: Array,
      required: true
    },
    getScore: {
      type: Boolean,
      required: false,
      default: false
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    return {
      labelwidth: '110px',
      sortStates: [
        {
          name: '是',
          value: '1'
        },
        {
          name: '否',
          value: '0'
        }
      ],
      rules: {
        question: [
          {
            required: true,
            message: '请输入题干'
          }
        ],
        answer: [
          {
            required: true,
            message: '请填写答案'
          }
        ],
        scoreType: [
          {
            required: true,
            message: '请选择试题分值'
          }
        ],
        score: [
          {
            required: true,
            message: '请输入分数'
          }
        ]
      }
    }
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      var form = this.form
      this.form.questionText = form.question
      if (form.list && form.list.length > 0) {
        form.list.map((item) => {
          if (item.options) {
            var optionObject = JSON.parse(item.options)
            this.form.optionsArr = Object.values(optionObject)
          } else {
            this.form.optionsArr = ['', '', '', '']
          }
          this.form.sortState = item.sortState ? item.sortState : '0'
          this.form.answers = item.answer ? item.answer.split(',') : []
          this.form.scoreType = item.scoreType ? item.scoreType : 1
          this.form.score = item.score ? item.score : 0
        })
      } else {
        this.form.sortState = '0'
        this.form.answers = []
        this.form.scoreType = 1
        this.form.score = this.socorRegular[this.form.questionType]
      }
    },
    answerChange(index) {
      var flag = this.form.answers.filter((item) => {
        return !item
      })
      if (flag.length > 0) {
        this.form.answer = ''
      } else {
        this.form.answer = this.form.answers.join(',')
      }
    },
    contentChange(data, label) {
      this.form[label] = data.html
      if (label == 'question') {
        this.questionChange(data)
      }
    },
    questionChange(data) {
      if (data.html) {
        this.questionText = data.text
        var answerlength = data.text.split('@').length - 1
        answerlength = answerlength >= 6 ? 6 : answerlength
        if (answerlength > 0) {
          if (answerlength != this.form.answers.length) {
            var answers = this.form.answers
            var newanswers = []
            for (var i = 0; i < answerlength; i++) {
              if (answers[i]) {
                newanswers[i] = answers[i]
              } else {
                newanswers[i] = ''
              }
            }
            this.form.answers = newanswers
            this.form.answer = ''
          }
        } else {
          this.form.answers = []
          this.form.answer = ''
        }
      } else {
        this.form.answers = []
        this.form.answer = ''
      }
    },
    socreTypeChange() {
      var socorRegular = this.socorRegular
      this.form.score = this.form.scoreType == 1 ? socorRegular[this.form.questionType] : this.form.score
    },
    beforeSubmit() {
      return new Promise((resolve, reject) => {
        this.$refs.theForm.validate((valid) => {
          if (!valid) {
            reject()
          } else {
            var thetopic = {
              question: this.form.question,
              list: [
                {
                  sortState: this.form.sortState,
                  answer: this.form.answer,
                  analysis: this.form.analysis,
                  score: this.form.score,
                  scoreType: this.form.scoreType,
                  questionExerciseAnswerId: this.form.list.length && this.form.list[0].questionExerciseAnswerId ? this.form.list[0].questionExerciseAnswerId : null,
                  questionExerciseId: this.form.list.length && this.form.list[0].questionExerciseId ? this.form.list[0].questionExerciseId : null,
                  paperQuestionAnswerId: this.form.list.length && this.form.list[0].paperQuestionAnswerId ? this.form.list[0].paperQuestionAnswerId : null,
                  paperQuestionId: this.form.list.length && this.form.list[0].paperQuestionId ? this.form.list[0].paperQuestionId : null
                }
              ]
            }
            resolve(thetopic)
          }
        })
      })
    }
  }
}
</script>
