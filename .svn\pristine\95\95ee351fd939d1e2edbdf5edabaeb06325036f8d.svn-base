<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
        <div class="title">问答纠错库</div>
      </div>
      <div class="content">
        <el-form ref="form" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="问题:">
            <el-input v-model="queryInfo.problem" maxlength="40" placeholder="请输入问题" clearable @clear="getList" @keydown.native.enter="getList"></el-input>
          </el-form-item>
          <el-form-item label="回答:">
            <el-input v-model="queryInfo.answer" maxlength="40" placeholder="请输入回答" clearable @clear="getList" @keydown.native.enter="getList"></el-input>
          </el-form-item>
          <el-form-item label="病例类型:">
            <el-radio-group v-model="queryInfo.caseType" @change="getList">
              <el-radio :label="1">学习病例</el-radio>
              <el-radio :label="2">考核病例</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="训练时间:">
            <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange" clearable> </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="getList">查询</el-button>
            <el-button type="primary" size="small" @click="reset">重置</el-button>
            <el-button type="primary" size="small" @click="ignores">批量忽略</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="list" style="width: 100%" border @selection-change="selectionChange">
          <el-table-column align="center" width="width" type="selection"> </el-table-column>
          <el-table-column align="center" label="序号" width="width" type="index"> </el-table-column>
          <el-table-column align="center" prop="problem" label="问题" width="width"> </el-table-column>
          <el-table-column align="center" prop="answer" label="回答" width="width"> </el-table-column>
          <el-table-column align="center" prop="name" label="病例名称" width="width"> </el-table-column>
          <el-table-column align="center" prop="caseType" label="病例类型" width="width">
            <template v-slot="{ row }">
              <span>{{ row.caseType === 1 ? '学习病例' : '考核病例' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="studentInfor" label="学生信息" width="width"> </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column>
          <el-table-column align="center" prop="createUserName" label="提交人" width="width"> </el-table-column>
          <el-table-column align="center" prop="rectifyUserName" label="纠错人" width="width"> </el-table-column>
          <el-table-column align="center" prop="isRectify" label="是否已纠错" width="width">
            <template v-slot="{ row }">
              <el-tag>{{ row.isRectify ? '是' : '否' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="width">
            <template v-slot="{ row }">
              <el-button v-if="!row.isRectify" type="primary" size="small" @click="ignore(row)">忽略</el-button>
              <el-button v-if="!row.isRectify" type="warning" size="small" @click="errorCorrection(row)">纠错</el-button>
              <el-tag v-if="row.isRectify" type="success">已纠错完成</el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div style="width: 100%; margin: 15px; text-align: center">
          <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
        </div>
      </div>
    </el-card>
    <!-- 纠错弹窗 -->
    <ErrorCorrection ref="ErrorCorrection" :dialog.sync="errorCorrectionDialog" @success="getList" />
  </div>
</template>
<script>
import { caseRectifyList, caseRectifyRemoveBatch } from '@/api/caseRectify'
import { formatDate } from '@/filters'
import ErrorCorrection from '@/views/caseRectify/ErrorCorrection'
export default {
  name: 'caseRectify',
  components: {
    ErrorCorrection
  },
  data() {
    return {
      queryInfo: {
        problem: null,
        answer: null,
        caseType: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      },
      time: null,
      list: [],
      total: 0,
      checkedList: [], //被选中的数据
      errorCorrectionDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseRectifyList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    selectionChange(val) {
      this.checkedList = val
    },
    reset() {
      this.queryInfo = {
        problem: null,
        answer: null,
        caseType: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    // 批量忽略
    ignores() {
      if (!this.checkedList.length) return this.$message.warning('未选中问题')
      this.$confirm('确定要忽略被选中的问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const ids = this.checkedList.map((item) => item.rectifyId)
          await caseRectifyRemoveBatch({ ids })
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    ignore(row) {
      this.$confirm('确定要忽略问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseRectifyRemoveBatch({ caseId: row.caseId, ids: [row.rectifyId] })
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    errorCorrection(row) {
      this.$refs['ErrorCorrection'].caseInfo = { ...row }
      this.$refs['ErrorCorrection'].searchInfo()
      this.errorCorrectionDialog = true
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  .el-card {
    height: 100%;
    width: 100%;
    .goBack {
      position: absolute;
    }

    .title {
      font-size: 25px;
      text-align: center;
    }
  }
}
</style>
