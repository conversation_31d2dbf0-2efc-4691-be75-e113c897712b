<template>
  <div class="error-question-container">
    <div class="major-select">
      <div class="major-select__title">选择专业(多选)</div>
      <div class="major-select__list">
        <div class="major-select__item" v-for="item in majors" :key="item.majorName" :class="{ active: majorIds.includes(item.majorId) }" @click="selectItem(item)">
          {{ item.majorName }}
        </div>
      </div>
    </div>
    <div class="topic-manage">
      <div class="topic-manage__search">
        <div class="search-field">
          <span class="search-field__label">题干名称：</span>
          <el-input class="search-field__input" placeholder="请输入题干名称" v-model="question" clearable />
        </div>
        <div class="search-field">
          <span class="search-field__label">题目类型：</span>
          <el-radio-group class="search-field__radio" v-model="questionType" @change="handleCurrentChange(1)">
            <el-radio v-for="item in questionTypes" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </div>
        <div class="search-field">
          <el-button @click="handleCurrentChange(1)">查询</el-button>
          <el-button class="reset-button" @click="research">重置</el-button>
          <el-button type="primary" @click="handleCurrentChange(1)" style="margin-left: 5px">搜索</el-button>
          <el-button type="success" @click="exportTopics" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出题目' }}</el-button>
        </div>
      </div>
      <div class="topic-manage__table">
        <el-table :data="topics" row-key="questionErrorId" cell-class-name="tableCellClassName" header-cell-class-name="tableHeaderClassName" @selection-change="selectionChange">
          <el-table-column type="selection" width="60" align="center" :selectable="selectableItem"> </el-table-column>
          <el-table-column prop="question" label="题干" width="220" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.questionType != 'COMPATIBILITY'">
                <span class="editor_box" v-html="scope.row.question"></span>
              </span>
              <div v-else>
                <div class="list_question" v-for="(item, index) in getQuestion(scope.row)" :key="index">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="questionType" label="题型" align="center" width="90px">
            <template v-slot="{ row }">
              <span> {{ row.questionType | questionTypeFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度" align="center" width="90px">
            <template v-slot="{ row }">
              <span>{{ difficultyFilter(row.difficulty) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="majorName" label="所属专业" align="center" width="130px"> </el-table-column>
          <el-table-column prop="questionUse" label="所属题库" align="center">
            <template slot-scope="scope">
              <span>{{ questionUsesFilter(scope.row.questionUse) }}</span>
              <!-- <span v-for="item in questionUses" v-if="item.value == scope.row.questionUse">{{ item.name }}</span> -->
            </template>
          </el-table-column>
          <el-table-column prop="errorReason" label="错误原因" align="center" width="220px" :show-overflow-tooltip="true"> </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" width="210"> </el-table-column>
          <el-table-column prop="createUserName" label="创建人" align="center"> </el-table-column>
          <el-table-column label="操作" align="center" width="170">
            <template slot-scope="scope">
              <div class="button-group">
                <el-button type="primary" icon="el-icon-document" style="margin-right: 10px" @click="openView(scope.row)" size="small">查看</el-button>
                <el-button type="primary" icon="el-icon-delete" @click="openDelete(scope.row)" size="small">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="topic-manage__pagination">
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-size="pageSize" layout="total,  prev, pager, next" :total="total"> </el-pagination>
      </div>
    </div>

    <el-dialog :title="getType(form)" custom-class="view_dialog" top="70px" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="questionView">
      <questionview :form="form" :letters="letters" :difficultys="difficultys" v-if="questionView"></questionview>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="questionView = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { questionErrorList, selectQuestionErrorDetail, removeQuestionError, questionErrorExport } from '@/api/questionerror.js'
import { selectTeacherById } from '@/api/teacher.js'
import { putProgress } from '@/utils/oss.js'
import questionview from '@/views/question/view/index'
import { questionTypes } from '@/filters'

export default {
  components: {
    questionview
  },
  data() {
    return {
      userinfo: {},
      majors: [],
      majorIds: [],
      questionType: '',
      question: '',
      questionUse: 0,
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      dialogImport: false,
      questionView: false,
      form: {},
      multipleSelect: [],
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,

      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    difficultyFilter() {
      return (val) => {
        if (!val) {
          return ''
        }
        return this.difficultys.filter((item) => item.value == val)[0].name
      }
    },
    questionUsesFilter() {
      return (val) => {
        if (!val) {
          return ''
        }
        return this.questionUses.filter((item) => item.value == val)[0].name
      }
    }
  },
  methods: {
    selectableItem(row, index) {
      if (row.questionType == 'COMPATIBILITY' || row.questionType == 'COMPREHENSIVE') {
        return false
      } else {
        return true
      }
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    getType(form) {
      var questionTypes = this.questionTypes
      var questionTypeName = ''
      if (form && form.questionType) {
        questionTypes.map((item) => {
          if (form.questionType == item.value) {
            questionTypeName = item.name
          }
        })
      }
      return questionTypeName || '题目详情'
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (res.data.majors && res.data.majors.length > 0) {
          this.getList()
        }
      })
    },
    selectItem(item) {
      if (this.majorIds.includes(item.majorId)) {
        this.majorIds = this.majorIds.filter((i) => {
          return i != item.majorId
        })
        this.handleCurrentChange(1)
      } else {
        this.majorIds.push(item.majorId)
        this.handleCurrentChange(1)
      }
    },
    research() {
      this.questionType = ''
      this.question = ''
      this.questionUse = '0'
      this.pageNum = 1
      this.majorIds = []
      this.getList()
    },

    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getList()
    },
    getList() {
      var majorIds = this.majorIds

      if (!majorIds || majorIds.length <= 0) {
        majorIds = this.majors.map((item) => {
          return item.majorId
        })
      }

      var data = {
        majorIds: majorIds,
        questionUse: this.questionUse,
        question: this.question ? this.question : null,
        questionType: this.questionType ? this.questionType : null,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionErrorList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    exportTopics() {
      var multipleSelect = this.multipleSelect
      var questionErrorIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          questionErrorIds.push(item.questionErrorId)
        })
      }
      questionErrorExport({
        questionErrorIds: questionErrorIds && questionErrorIds.length > 0 ? questionErrorIds : null,
        majorIds: this.majorIds
      })
    },
    downloadTemplate() {
      var download = document.createElement('a')
      download.href = this.baseurl + 'topicTemplate.xls'
      $('body').append(download)
      download.click()
    },
    openView(item) {
      selectQuestionErrorDetail({
        questionErrorId: item.questionErrorId
      }).then((res) => {
        this.form = Object.assign({}, item, res.data)
        this.questionView = true
      })
    },
    openDelete(item) {
      this.$confirm('此操作将永久删除此试题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'generalConfirm'
      })
        .then(() => {
          removeQuestionError({
            questionErrorId: item.questionErrorId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getList()
            }
          })
        })
        .catch((err) => {
          console.log(err)
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.error-question-container {
  width: 100%;
  height: 100%;
  display: flex;
  .major-select {
    width: 300px;
    height: 100%;
    border-right: 1px solid #e4e4e4;
    &__title {
      padding: 20px 0 30px 38px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #303030;
    }
    &__item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 60px;
      padding-left: 38px;
      cursor: pointer;
      &:hover {
        background: #f6f8fa;
      }
    }
    &__item.active {
      background: #f6f8fa;
      color: #274e6a;
      font-weight: 600;
    }
  }
  .topic-manage {
    position: relative;
    flex: 1;
    height: 100%;
    padding-left: 20px;
    padding-top: 30px;
    padding-right: 30px;
    overflow: auto;
    &__search {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      .search-field {
        display: flex;
        align-items: center;
        margin-right: 30px;
        &__label {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #666666;
        }
        &__input {
          width: 300px;
          height: 45px;
          ::v-deep {
            .el-input__inner {
              width: 100%;
              height: 100%;
              border-radius: 74px;
              background: #eef0f2;
              border: none;
              color: #333333;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              &::placeholder {
                color: #cccccc;
              }
            }
            .el-input__suffix {
              top: 3px;
              right: 10px;
              .el-input__suffix-inner {
                .el-icon-circle-close {
                  font-size: 20px;
                }
              }
            }
          }
        }
        &__radio {
          display: flex;
          ::v-deep {
            .el-radio {
              display: flex;
              align-items: center;
              margin-right: 40px;
              &:last-of-type {
                margin-right: 0;
              }
              .el-radio__inner {
                width: 16px;
                height: 16px;
                background: #fff;
                border-color: #b1b1b1;
              }
              .el-radio__label {
                padding-left: 6px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 20px;
                color: #b1b1b1;
              }
            }

            .el-radio__input.is-checked .el-radio__inner {
              &::after {
                background: #274e6a;
                width: 10px;
                height: 10px;
              }
            }
            .el-radio__input.is-checked + .el-radio__label {
              color: #274e6a;
            }
          }
        }
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 45px;
          padding: 0 18px;
          margin-right: 20px;
          background: #65849a;
          border-radius: 63px 63px 63px 63px;
          border: none;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          &:first-of-type {
            margin-right: 10px;
          }
        }
        .reset-button {
          background: #fff;
          border: 1px solid #65849a;
          color: #65849a;
        }
      }
    }
    &__table {
      .el-table {
        &::before {
          display: none;
        }
        ::v-deep {
          .tableCellClassName {
            height: 55px;
            background: #f6f8fa;
            border-bottom: 4px solid #fff;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            &:first-of-type {
              border-radius: 8px 0 0 8px;
            }
            &:nth-of-type(9) {
              border-radius: 0 8px 8px 0;
            }

            .el-checkbox {
              &__input.is-checked {
                .el-checkbox__inner {
                  background-color: #274e6a;
                  border-color: #274e6a;
                }
              }
              &__inner {
                width: 28px;
                height: 28px;
                border-radius: 4px 4px 4px 4px;
                &::after {
                  width: 8px;
                  height: 14px;
                  left: 8px;
                  top: 2px;
                  border-width: 2px;
                }
              }
            }
          }
          .tableHeaderClassName {
            height: 55px;
            border-bottom: 4px solid #fff;
            background: #f6f8fa;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #999999;
            &:first-of-type {
              border-radius: 8px 0 0 8px;
            }
            &:nth-of-type(9) {
              border-radius: 0 8px 8px 0;
            }
            .el-checkbox {
              &__input.is-checked,
              &__input.is-indeterminate {
                .el-checkbox__inner {
                  background-color: #274e6a;
                  border-color: #274e6a;
                }
              }
              &__inner {
                width: 28px;
                height: 28px;
                border-radius: 4px 4px 4px 4px;
                &::after {
                  width: 8px;
                  height: 14px;
                  left: 8px;
                  top: 2px;
                  border-width: 2px;
                }
                &::before {
                  width: 14px;
                  top: 12px;
                  left: 50%;
                  transform: translateX(-50%) scale(1);
                }
              }
            }
          }

          .button-group {
            display: flex;
            align-items: center;
            justify-content: space-around;
          }
          .el-button {
            display: flex;
            align-items: center;
            justify-content: center;

            width: 78px;
            height: 38px;
            padding: 0;
            margin: 0;
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #e2e2e2;

            font-family: PingFang SC;
            font-weight: 500;
            font-size: 18px;
            i {
              font-size: 16px;
              margin-right: 4px;
            }
            span {
              margin-left: 0;
            }

            &--primary {
              color: #3381b9;
            }
          }
        }
      }
    }
    &__pagination {
      position: absolute;
      left: 50%;
      bottom: 30px;
      transform: translateX(-50%);
      ::v-deep {
        .el-pagination.is-background .el-pager li:not(.disabled).active {
          background-color: #65849a;
          color: #eef0f2;
        }
        .el-pagination.is-background .el-pager li {
          min-width: 40px;
          height: 40px;
          line-height: 40px;
          border-radius: 10px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          background-color: #eef0f2;
          color: #65849a;
        }
        .el-pagination.is-background .btn-prev,
        .el-pagination.is-background .btn-next {
          min-width: 40px;
          height: 40px;
          line-height: 40px;
          border-radius: 10px;
          background-color: #eef0f2;
          color: #65849a;
          font-size: 16px;
        }
        .el-pagination.is-background .btn-prev:disabled {
          color: rgba($color: #65849a, $alpha: 0.3);
        }
        .el-pagination__total {
          height: 40px;
          line-height: 40px;
          color: #fff;
          font-size: 15px;
        }
      }
    }
  }
}
</style>
