<template>
  <el-select ref="selecteltree" v-model="value" @focus="getMenuTree" clearable @clear="clearData">
    <el-option v-for="item in menu" :key="item.id" :label="item.topic" :value="item.id" style="display: none" />
    <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick">
      <template v-slot="{ node, data }">
        <span :class="[{ checkedTreeItem: data.topic === value }, 'treeItem']">{{ data.topic }}</span>
      </template>
    </el-tree>
  </el-select>
</template>

<script>
import { caseQuestionTree } from '@/api/case'
export default {
  name: 'TreeSelect',
  props: ['caseId'], // 这个只是纠错库使用的
  data() {
    return {
      value: null,
      menu: [],
      defaultProps: {
        children: 'children',
        label: 'topic'
      }
    }
  },
  methods: {
    async getMenuTree(echo = false, parentId = 0) {
      const caseId = this.$route.params.id ? this.$route.params.id : this.caseId ? this.caseId : 0
      const { data } = await caseQuestionTree({ caseId })
      this.menu = data
      if (echo === true) {
        this.value = this.findNodeById(this.menu, parentId).topic
      }
    },
    handleNodeClick(data, node) {
      if (node.level > 2) return this.$message.error('禁止选择超过二级的问题')
      this.$nextTick(() => {
        this.value = data.topic
      })
      this.$refs['selecteltree'].blur()
      this.$emit('selectChange', data.id)
    },
    reset() {
      this.value = null
      this.menu = []
    },
    showData(parentId) {
      if (parentId) {
        this.getMenuTree(true, parentId)
      }
    },
    findNodeById(tree, id) {
      let result = null
      tree.some((node) => {
        if (node.id === id) {
          result = node
          return true // 找到后停止循环
        }
        // 检查是否有子节点并进行递归搜索
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, id)
          if (found) {
            result = found // 如果在子节点中找到了结果，就将该结果赋给 result
            return true // 并且停止进一步搜索
          }
        }
        // 如果当前节点不是目标节点，并且在它的子节点中也没有找到目标节点，继续循环
        return false
      })
      return result
    },
    clearData() {
      this.value = null
      this.$emit('selectChange', 0)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
.el-select {
  width: 100%;
}
.el-tree {
  padding-left: 0 !important;
}
.treeItem {
  font-size: 14px;
}
.checkedTreeItem {
  font-weight: bold;
  color: #1890ff;
}
</style>
