<template>
  <div class="Avatar">
    <div>
      <template v-if="sex === 'M'">
        <img v-if="age < 17" src="@/assets/case/avatar_man_0.png" alt="" />
        <img v-else-if="age < 68" src="@/assets/case/avatar_man_1.png" alt="" />
        <img v-else src="@/assets/case/avatar_man_2.png" alt="" />
        <img class="sexIcon" src="@/assets/case/manIcon.png" alt="" />
      </template>
      <template v-else>
        <img v-if="age < 17" src="@/assets/case/avatar_woman_0.png" alt="" />
        <img v-else-if="age < 68" src="@/assets/case/avatar_woman_1.png" alt="" />
        <img v-else src="@/assets/case/avatar_woman_2.png" alt="" />
        <img class="sexIcon" src="@/assets/case/womanIcon.png" alt="" />
      </template>
    </div>
  </div>
</template>
<script>
export default {
  name: '',
  props: {
    age: {
      type: Number || String,
      require: true
    },
    sex: {
      type: String,
      default: 'F' // 默认女
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>
<style scoped lang="scss">
.Avatar {
  position: relative;
  width: 49px;
  height: 49px;
  img {
    width: 49px;
    height: 49px;
  }
  .sexIcon {
    position: absolute;
    right: -3px;
    bottom: 0px;
    width: 16px;
    height: 16px;
  }
}
</style>
