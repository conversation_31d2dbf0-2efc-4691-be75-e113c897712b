<template>
  <div class="clearfix questionPaper-add">
    <div class="questionPaper-header">
      <div class="header_back" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="header-title">添加试卷</div>
      <el-button class="save-button" type="primary" @click="checkAll" :disabled="addLoading">保存试卷</el-button>
    </div>
    <div class="questionPaper-body" ref="questionPaperBodyRef">
      <div class="topform_cont clearfix">
        <el-form :model="topform" :rules="rules" ref="topform" class="clearfix">
          <div class="topicinfo_top">
            <el-row>
              <el-col :span="6">
                <el-form-item label="所属专业：" :label-width="labelwidth" prop="majorId">
                  <el-select v-model="topform.majorId" placeholder="请选择所属专业" @change="clearAll">
                    <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="试卷名称：" :label-width="labelwidth" prop="paperName">
                  <el-input v-model="topform.paperName" placeholder="请输入试卷名称" maxlength="20"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="试卷时长：" :label-width="labelwidth" prop="duration">
                  <el-input v-model="topform.duration" placeholder="请输入时长（分钟）"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="及格分数：" :label-width="labelwidth" prop="passScore">
                  <el-input v-model="topform.passScore" placeholder="请输入及格分数"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <el-row class="menuinfo_botttom">
            <el-col :span="12">
              <el-form-item label="试题添加：" :label-width="labelwidth" prop="examinePaperQuestionReqs">
                <el-button v-for="item in addtypes" :key="item.value" @click="changeType(item)" :type="viewIndex == -1 && addtype == item.value ? 'primary' : ''">
                  {{ item.name }}
                </el-button>
                <el-button @click="regettopics" type="primary"> 题库选择 </el-button>
                <el-tooltip v-if="addtype == '1' || addtype == '2'" class="item" effect="dark" content="您在自定义添加界面与复制粘贴界面添加的试题，将会自动存入正式题库中" placement="bottom">
                  <div class="tip_btn"><i class="el-icon-question"></i></div>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="menuinfo">
          <el-form :model="form" :rules="rules" ref="addForm">
            <el-row>
              <template v-if="addtype == '1'">
                <el-col :span="14" class="question-property">
                  <el-form-item label="难度：" :label-width="labelwidth" prop="difficulty">
                    <el-radio-group v-model="form.difficulty">
                      <el-radio-button :label="item.value" v-for="item in difficultys" :key="item.value">{{ item.name }}</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="题型：" :label-width="'90px'" prop="questionType">
                    <el-radio-group v-model="form.questionType" @change="topicChange">
                      <el-radio-button :label="item.value" v-for="item in questionTypes" :key="item.value">{{ item.name }}</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="14">
                  <div v-if="questionType">
                    <questioninput ref="questioninput" class="questioninput" :getScore="true" :socorRegular="socorRegular" :letters="letters" :form="form" :questionType="questionType"></questioninput>
                  </div>
                  <div style="text-align: center" class="save-operate">
                    <el-button @click="jumpQuestion(-1)" :disabled="!beforeBtn" type="primary" v-if="viewIndex >= 0">上一题</el-button>
                    <el-button @click="beforeCheck" type="primary" v-if="form.questionType">保存</el-button>
                    <el-button @click="jumpQuestion(1)" :disabled="!afterBtn" type="primary" v-if="viewIndex >= 0">下一题</el-button>
                  </div>
                </el-col>
              </template>

              <el-col :span="14" v-if="addtype == '2'">
                <paste class="pasteComponent" :getScore="true" :socorRegular="socorRegular" @beforePasteCheck="beforePasteCheck"></paste>
              </el-col>
              <el-col :span="14" v-if="addtype == '3'">
                <simulation ref="simulation" class="simulationComponent" :socorRegular="socorRegular" @beforeSelectCheck="beforeSelectCheck" :majorId="topform.majorId"></simulation>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div v-if="topform.examinePaperQuestionReqs.length" class="menuinfo_list" ref="menuinfoListRef">
        <div class="menuinfo_top">
          <span class="menuinfo-title">试卷题</span>
          <div class="menuinfo-total">
            <span>题目总数：{{ topform.examinePaperQuestionReqs.length }}</span>
            <span>总分：{{ topform.totalScore }}</span>
          </div>
        </div>
        <div class="menuinfo_info" v-for="questionType in questionTypes" :key="questionType.value" v-show="getArray(questionType.value).length > 0">
          <div class="menuinfo_infoname">{{ questionType.name }}（共{{ getArray(questionType.value).length }}题, 共 {{ questionTypeTotalScore(getArray(questionType.value)) }}分）</div>
          <div class="menuinfo_infolist clearfix" v-show="getArray(questionType.value).length > 0">
            <div v-for="(item, index) in getArray(questionType.value)" :key="index" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
              <span class="question-type" :class="questionType.value">{{ questionType.name }}</span>
              <el-tooltip effect="dark" :content="stripHtmlTags(item.question)" placement="top-start">
                <span class="question-content"> {{ stripHtmlTags(item.question) }}</span>
              </el-tooltip>

              <div class="question-operate">
                <span class="question-operate__edit" @click.stop="openList(item, index)">
                  <i class="el-icon-edit-outline"></i>
                  编辑
                </span>
                <span class="question-operate__delete" @click.stop="deleteList(item, index)">
                  <i class="el-icon-delete"></i>
                  删除</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog custom-class="selectQuestion-dialog" title="选择试题" center top="70px" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="tableshow">
      <div class="selectQuestion-num">当前选择题数：{{ selections.length }}</div>
      <div class="selectQuestion-search">
        <!--<span class="search_label">所属专业：</span>
				<el-select v-model="questionMajorId" placeholder="请选择所属专业" @change='handleCurrentChange(1)' clearable>
					<el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId">
					</el-option>
				</el-select>-->
        <div class="search-field">
          <span class="search-field__label">题干名称：</span>
          <el-input class="search-field__input" placeholder="请输入题干名称" width="30px" v-model="questionSele" @change="handleCurrentChange(1)" clearable />
        </div>
        <div class="search-field">
          <span class="search-field__label">题目类型：</span>
          <el-radio-group class="search-field__radio" v-model="questionType" @change="handleCurrentChange(1)">
            <el-radio v-for="item in questionTypes" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </div>
        <el-button type="primary" @click="handleCurrentChange(1)" style="margin-left: 5px">搜索</el-button>
      </div>
      <div class="selectQuestion-table">
        <el-table :data="topics" row-key="questionId" cell-class-name="tableCellClassName" header-cell-class-name="tableHeaderClassName" @selection-change="selectionChange" ref="topics">
          <el-table-column type="selection" width="60" align="center"> </el-table-column>
          <el-table-column prop="question" width="width" show-overflow-tooltip label="题干" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.questionType != 'COMPATIBILITY'">
                <span class="editor_box" v-html="scope.row.question"></span>
              </span>
              <div v-else>
                <div class="list_question" v-for="(item, index) in getQuestion(scope.row)" :key="index">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="questionType" label="题型" width="90" align="center">
            <template v-slot="{ row }">
              <span> {{ row.questionType | questionTypeFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度" width="90" align="center">
            <template v-slot="{ row }">
              <span>{{ difficultyFilter(row.difficulty) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="majorName" label="所属专业" width="130" align="center"> </el-table-column>
          <el-table-column prop="questionUse" label="所属题库" width="130" align="center">
            <template v-slot="{ row }">
              <span>{{ questionUseFilter(row.questionUse) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="210" align="center"> </el-table-column>
          <el-table-column prop="createUserName" label="创建人" width="130" align="center"> </el-table-column>
        </el-table>
      </div>

      <div class="selectQuestion-pagination">
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-size="pageSize" layout="total, prev, pager, next" :total="total"> </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer save-operate">
        <el-button :disabled="addLoading" type="info" @click="calcelSelection">取 消</el-button>
        <el-button :disabled="addLoading" type="primary" @click="addSelection()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { examinePaperList, saveExaminePaper, updateExaminePaper, removeExaminePaper, selectExaminePaperDetailById, releaseExaminePaper, saveAutoExaminePaper } from '@/api/paper.js'
import { questionList, selectQuestionDetail } from '@/api/question.js'
import { selectSimulateParamList } from '@/api/param.js'
import { selectTeacherById } from '@/api/teacher.js'
import questioninput from '@/views/question/input/index'
import paste from '@/views/question/input/paste'
import simulation from '@/views/volume/exam/simulation'
import { questionTypes } from '@/filters'
import { stripHtmlTags } from '@/utils'

export default {
  components: {
    questioninput,
    paste,
    simulation
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else if (value <= 0) {
        return callback(new Error('不能小于或等于0'))
      } else {
        return callback()
      }
    }
    var checkPass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else if (value > this.topform.totalScore) {
        return callback(new Error('不能超过总分'))
      } else if (value <= 0) {
        return callback(new Error('不能小于或等于0'))
      } else {
        return callback()
      }
    }
    var checkArray = (rule, value, callback) => {
      if (!value || value.length <= 0) {
        return callback(new Error('请添加试题'))
      } else {
        return callback()
      }
    }
    return {
      paperId: this.$route.query.paperId,
      labelwidth: '130px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: null,
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        },
        {
          name: '智能组卷',
          value: '3'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      SINGLE: [],
      MULTIPLE: [],
      JUDGE: [],
      COMPLETION: [],
      SHORT: [],
      COMPATIBILITY: [],
      COMPREHENSIVE: [],
      viewIndex: -1,
      viewType: -1,
      beforeBtn: true,
      afterBtn: true,
      topform: {
        paperName: '',
        totalScore: 0,
        smallCount: 0,
        examinePaperQuestionReqs: [],
        duration: '',
        passScore: '',
        majorId: '',
        paperQuestionIds: []
      },
      socorRegular: {},
      form: {
        majorId: '',
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        scoreType: 1,
        score: 0,
        sortState: '0'
      },
      rules: {
        paperName: [
          {
            required: true,
            message: '请输入试卷名称'
          }
        ],
        duration: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入时长'
          }
        ],
        passScore: [
          {
            validator: checkPass
          },
          {
            required: true,
            message: '请输入及格分'
          }
        ],
        examinePaperQuestionReqs: [
          {
            validator: checkArray
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      //试题选择
      questionMajorId: '',
      questionSeleType: '',
      questionSele: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      selections: [],
      selectionIndex: 0,
      tableshow: false,
      addLoading: false,
      emptyScores: []
    }
  },
  computed: {
    questionTypeTotalScore() {
      return (data) => {
        let totalScore = 0
        data.forEach((question) => {
          totalScore += question.list.reduce((pre, next) => {
            return pre + next.score
          }, 0)
        })
        return totalScore
      }
    },
    difficultyFilter() {
      return (val) => {
        return this.difficultys.filter((item) => item.value == val)[0].name
      }
    },
    questionUseFilter() {
      return (val) => {
        return this.questionUses.filter((item) => item.value == val)[0].name
      }
    }
  },
  mounted() {
    this.getUserInfo()
    this.$refs['questionPaperBodyRef'].addEventListener('scroll', this.listenScollTop)
  },
  methods: {
    stripHtmlTags,
    goBack() {
      this.$router.push('/volume')
    },
    clearAll() {
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value] = []
      })
      this.sumSelection()
      this.clearCheck()
    },
    listenScollTop(e) {
      if (!this.topform.examinePaperQuestionReqs.length) return
      // 拿到被卷曲的高度
      var scrollTop = e.target.scrollTop
      if (scrollTop >= 100) {
        this.$refs['menuinfoListRef'].style.top = '130px'
        this.$refs['menuinfoListRef'].style.height = '740px'
      } else {
        this.$refs['menuinfoListRef'].style.top = '240px'
        this.$refs['menuinfoListRef'].style.height = '650px'
      }
    },
    getScoreList() {
      var teacherId = this.userinfo.teacherId
      selectSimulateParamList({
        teacherId: teacherId,
        schoolId: this.userinfo.schoolId,
        code: 'score_rule'
      }).then((res) => {
        if (res.data) {
          res.data.map((item) => {
            this.socorRegular[item.code] = item.value
          })
        }
      })
    },
    checkAll() {
      if (this.emptyScores && this.emptyScores.length != 0) {
        this.$message({
          type: 'error',
          message: '还有未输入分数的试题！'
        })
        return
      }
      this.$refs.topform.validate((valid) => {
        if (valid) {
          this.addLoading = true
          var examinePaperSaveReq = {
            schoolId: this.userinfo.schoolId,
            majorId: this.topform.majorId,
            paperName: this.topform.paperName,
            duration: this.topform.duration,
            totalScore: this.topform.totalScore,
            smallCount: this.topform.smallCount,
            passScore: this.topform.passScore,
            questionCount: this.topform.questionCount
          }
          var examinePaperQuestionReqs = this.topform.examinePaperQuestionReqs
          examinePaperQuestionReqs.map((item) => {
            var topicScore = 0
            if (item.list && item.list.length > 0) {
              item.list.map((it) => {
                var score = it.score
                score = Number(score)
                if (!isNaN(score)) {
                  topicScore += score
                }
              })
            }
            item.score = topicScore
            item.paperQuestionAnswerList = item.list
          })
          if (this.paperId) {
            examinePaperSaveReq.paperId = this.paperId
            updateExaminePaper({
              paperEditReq: examinePaperSaveReq,
              paperQuestionReqList: examinePaperQuestionReqs,
              paperQuestionIds: this.topform.paperQuestionIds
            }).then((res) => {
              this.addLoading = false
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.closeCheck()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          } else {
            saveExaminePaper({
              examinePaperSaveReq,
              examinePaperQuestionReqs
            }).then((res) => {
              this.addLoading = false
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.closeCheck()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          }
        }
      })
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    regettopics() {
      if (!this.topform.majorId) {
        this.$message({
          type: 'error',
          message: '请先选择专业！'
        })
        return
      } else {
        this.questionMajorId = this.topform.majorId
      }
      this.questionSeleType = ''
      this.questionSele = ''
      this.pageNum = 1
      this.selections = []
      this.topics = []
      this.gettopics()
      this.tableshow = true
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.gettopics()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.gettopics()
    },
    gettopics() {
      var questionIds = []
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value].map((item) => {
          if (item.questionId) {
            questionIds.push(item.questionId)
          }
        })
      })
      var data = {
        majorId: this.questionMajorId ? this.questionMajorId : null,
        question: this.questionSele ? this.questionSele : null,
        questionType: this.questionSeleType ? this.questionSeleType : null,
        questionIds: questionIds,
        questionUse: 0,
        disabled: 1,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    selectionChange(val) {
      this.selections = val
    },
    calcelSelection() {
      this.selections = []
      this.tableshow = false
      this.$refs.topics.clearSelection()
    },
    addSelection() {
      var selections = this.selections
      if (selections && selections.length > 0) {
        this.addLoading = true
        this.selectionIndex = 0
        this.getAndAdd()
      } else {
        this.$message({
          type: 'error',
          message: '请选择试题'
        })
      }
    },
    getAndAdd() {
      var selections = this.selections
      var selectionIndex = this.selectionIndex
      var socorRegular = this.socorRegular
      selectQuestionDetail({
        questionId: selections[selectionIndex].questionId
      }).then((res) => {
        var item = Object.assign({}, selections[selectionIndex], res.data)
        item.isNew = true
        item.sortState = item.sortState || '0'
        item.list.map((it) => {
          it.score = socorRegular[item.questionType] || 0
          it.scoreType = 1
        })
        var inSetArr = this[item.questionType]
        var flagList = inSetArr.filter((it) => {
          return it.questionId == item.questionId
        })
        if (flagList.length == 0) {
          this[item.questionType].push(item)
        }
        this.nextAdd()
      })
    },
    nextAdd() {
      if (this.selectionIndex < this.selections.length - 1) {
        this.selectionIndex++
        this.getAndAdd()
      } else {
        this.selections = []
        this.selectionIndex = 0
        this.$refs.topics.clearSelection()
        this.tableshow = false
        this.addLoading = false
        this.sumSelection()
      }
    },
    sumSelection() {
      var totalScore = 0
      var smallCount = 0
      var noempty = 1
      var examinePaperQuestionReqs = []
      var emptyScores = []
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value + 'NUM'] = 0
        var paperQuestions = this[questionType.value]
        paperQuestions.map((item, index) => {
          if (item.list && item.list.length > 0) {
            var topicScore = 0
            item.list.map((it) => {
              smallCount++
              var score = it.score
              score = Number(score)
              if (!isNaN(score)) {
                totalScore += score
                topicScore += score
                if (score <= 0) {
                  noempty = -1
                  if (!emptyScores.includes(questionType.value + index)) {
                    emptyScores.push(questionType.value + index)
                  }
                }
              } else {
                noempty = -1
                if (!emptyScores.includes(questionType.value + index)) {
                  emptyScores.push(questionType.value + index)
                }
              }
            })
            this[item.questionType + 'NUM'] += topicScore
          }
          examinePaperQuestionReqs.push(item)
        })
      })
      this.topform.examinePaperQuestionReqs = examinePaperQuestionReqs
      this.topform.questionCount = examinePaperQuestionReqs.length
      this.topform.totalScore = totalScore
      this.topform.smallCount = smallCount
      this.topform.noempty = noempty
      this.emptyScores = emptyScores
    },
    deleteList(item, index) {
      this[item.questionType].splice(index, 1)
      if (index == this.viewIndex && item.questionType == this.viewType) {
        this.clearCheck()
      }
      if (item.paperQuestionId) {
        this.topform.paperQuestionIds.push(item.paperQuestionId)
      }
      this.sumSelection()
    },
    openList(item, index) {
      this.clearCheck()
      this.addtype = '1'
      this.questionType = null
      this.viewIndex = index
      this.beforeBtn = true
      this.afterBtn = true
      this.viewType = item.questionType
      var questionArr = ['', '', '', '']
      var optionsArr = ['', '', '', '']
      var answers = []
      if (item.questionType == 'COMPATIBILITY') {
        try {
          var questionObject = JSON.parse(item.question)
          questionArr = Object.values(questionObject)
        } catch (err) {}
      }
      if (item.options) {
        try {
          var optionObject = JSON.parse(item.options)
          optionsArr = Object.values(optionObject)
        } catch (err) {}
      }
      if (item.list) {
        item.list.map((it) => {
          it.scoreType = it.scoreType ? it.scoreType : 1
          it.score = it.score ? it.score : 0
        })
      }
      var newitem = JSON.parse(JSON.stringify(item))
      this.form = Object.assign({}, this.form, newitem, {
        questionArr,
        optionsArr,
        answers: item.answer ? item.answer.split('') : [],
        answer: item.answer ? item.answer : '',
        score: 0,
        scoreType: 1,
        sortState: item.sortState || '0'
      })

      this.$nextTick(() => {
        this.questionType = this.form.questionType

        var typeIndex = ''
        this.questionTypes.map((item, index) => {
          if (item.value == this.viewType) {
            typeIndex = index
          }
        })
        if (this.viewIndex == 0) {
          var newInfo = this.getBefore(typeIndex, -1)
          if (!newInfo || !newInfo.hasitem) {
            this.beforeBtn = false
          }
        } else if (this.viewIndex == this[this.viewType].length - 1) {
          var newInfo = this.getBefore(typeIndex, 1)
          if (!newInfo || !newInfo.hasitem) {
            this.afterBtn = false
          }
        }
        // this.jumpQuestion(-1)
        // this.jumpQuestion(1)
      })
    },
    pushList(item) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      if (viewType == -1 || (viewType != -1 && item.questionType != viewType)) {
        this[item.questionType].push(item)
        if (viewType != -1) {
          this[viewType].splice(viewIndex, 1)
        }
      } else {
        this[item.questionType][viewIndex] = item
      }
      this.sumSelection()
      this.clearCheck()
    },
    getArray(questionType) {
      return this[questionType]
    },
    jumpQuestion(type) {
      this.beforeBtn = true
      this.afterBtn = true
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      var questionTypes = this.questionTypes
      var typeIndex = ''
      questionTypes.map((item, index) => {
        if (item.value == viewType) {
          typeIndex = index
        }
      })
      if (type < 0) {
        if (viewIndex > 0) {
          viewIndex--
          this.openList(this[viewType][viewIndex], viewIndex)

          if (viewIndex == 0) {
            var newInfo = this.getBefore(typeIndex, type)
            if (!newInfo || !newInfo.hasitem) {
              this.beforeBtn = false
            }
          }
        } else {
          var newInfo = this.getBefore(typeIndex, type)

          if (newInfo && newInfo.hasitem) {
            viewIndex = newInfo.theArray.length - 1
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.beforeBtn = false
          }
        }
      }
      if (type > 0) {
        if (viewIndex < this[viewType].length - 1) {
          viewIndex++
          this.openList(this[viewType][viewIndex], viewIndex)

          if (viewIndex == this[viewType].length - 1) {
            var newInfo = this.getBefore(typeIndex, type)
            if (!newInfo || !newInfo.hasitem) {
              this.afterBtn = false
            }
          }
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = 0
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.afterBtn = false
          }
        }
      }
    },
    getBefore(typeIndex, type) {
      var questionTypes = this.questionTypes

      if (type < 0) {
        if (typeIndex <= 0) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex--
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
      if (type > 0) {
        if (typeIndex >= questionTypes.length - 1) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex++
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
    },
    beforeCheck() {
      this.theCheck()
        .then((data) => {
          data.isEdited = true
          data.isNew = this.viewIndex == -1
          console.log(data)

          this.pushList(data)
        })
        .catch((err) => {})
    },
    theCheck() {
      return new Promise((resolve, reject) => {
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            this.$refs.questioninput
              .beforeCheck()
              .then((resdata) => {
                var data = Object.assign({}, this.form, resdata)
                resolve(data)
              })
              .catch((err) => {
                reject()
              })
          } else {
            reject()
          }
        })
      })
    },
    beforePasteCheck(item) {
      var data = Object.assign({}, item, {
        majorId: this.topform.majorId,
        isNew: true
      })
      this.pushList(data)
    },
    beforeSelectCheck(data) {
      this.$confirm('此操作将清空当前已添加的试题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          saveAutoExaminePaper(data).then((res) => {
            if (res.code == '200') {
              var questionTypes = this.questionTypes
              questionTypes.map((questionType) => {
                this[questionType.value] = []
              })
              var selections = res.data
              selections.map((item) => {
                item.isNew = true
                item.sortState = item.sortState || '0'
                item.list.map((it) => {
                  if (item.questionType == 'SINGLE') {
                    it.score = data.singleScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'MULTIPLE') {
                    it.score = data.multipleScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'JUDGE') {
                    it.score = data.judgeScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'COMPLETION') {
                    it.score = data.completionScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'SHORT') {
                    it.score = data.shortScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'COMPATIBILITY') {
                    it.score = data.compatibilityScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'COMPREHENSIVE') {
                    it.score = data.comprehensiveScore || socorRegular[item.questionType]
                  }
                  it.scoreType = 0
                })
                this[item.questionType].push(item)
                this.sumSelection()
              })
              this.$refs.simulation.clearCheck()
              this.$nextTick(() => {
                this.changeType({
                  name: '自定义添加',
                  value: '1'
                })
              })
            }
          })
        })
        .catch(() => {})
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.topform.majorId,
          score: 0,
          scoreType: 1,
          questionUse: 0,
          difficulty: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: '',
          sortState: '0'
        }
      )
      this.viewIndex = -1
      this.viewType = -1
      this.questionType = null
      this.$refs.addForm.resetFields()
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    topicChange() {
      var socorRegular = this.socorRegular
      this.questionType = null
      var form = {
        majorId: this.topform.majorId,
        score: socorRegular[this.form.questionType] || 0,
        scoreType: 1,
        questionUse: this.form.questionUse,
        difficulty: this.form.difficulty,
        questionType: this.form.questionType,
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        sortState: '0'
      }
      this.$set(this, 'form', form)
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    changeType(item) {
      if (this.addtype != item.value) {
        this.addtype = item.value
      } else {
        if (item.value == '1') {
          this.clearCheck()
        }
      }
    },
    getInfo() {
      selectExaminePaperDetailById({
        paperId: this.paperId
      }).then((res) => {
        this.topform = Object.assign({}, this.topform, res.data)
        var examinePaperQuestionReqs = res.data.paperQuestionDetailDtoList
        examinePaperQuestionReqs.map((item) => {
          item.list = item.paperQuestionAnswerList
          item.isNew = false
          this.pushList(item)
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        this.getScoreList()
        if (this.paperId) {
          this.topform.paperName = this.paperName
          this.getInfo()
        }
      })
    },
    closeCheck() {
      // var view = this.$route
      this.$router.push('/volume')
      // this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
      //   if (this.isActive(view)) {
      //     this.toLastView(visitedViews, view)
      //   }
      // })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.questionPaper-add {
  width: 100%;
  height: 100%;
  padding: 0 50px 20px;
  background: linear-gradient(180deg, #d0dae4 0%, #f4f4f4 100%);
  .questionPaper-header {
    position: relative;
    padding-top: 34px;
    text-align: center;
    .header_back {
      position: absolute;
      left: 50px;
      top: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .header-title {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 35px;
      color: #293543;
      line-height: 35px;
    }
    .save-button {
      position: absolute;
      right: 0;
      top: 36px;
      width: 200px;
      height: 50px;
      background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
      border-radius: 63px 63px 63px 63px;
      border: none;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #ffffff;
    }
  }
  .questionPaper-body {
    position: relative;
    width: 100%;
    margin-top: 30px;
    height: 800px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px 30px 30px 30px;
    overflow: auto;
    &::-webkit-scrollbar {
      background: transparent;
    }
    .topform_cont {
      width: 100%;
      .topicinfo_top {
        width: 100%;
        padding: 26px 26px 20px;
        border-bottom: 1px solid #e4e4e4;
        ::v-deep {
          .el-form-item {
            margin-bottom: 0;
            &__label {
              position: relative;
              top: 5px;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              color: #666666;
            }
            .el-input {
              width: 300px;
              height: 45px;
              &__inner {
                width: 100%;
                height: 100%;
                background: #eef0f2;
                border-radius: 74px;
                border: none;
                color: #333333;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 20px;
                &::placeholder {
                  color: #cccccc;
                }
              }
              .el-input__suffix {
                top: 0px;
                right: 10px;
                .el-input__suffix-inner {
                  .el-icon-circle-close {
                    font-size: 20px;
                  }
                }
              }
            }
          }
        }
      }
      .menuinfo_botttom {
        .tip_btn {
          position: relative;
          top: 12px;
          margin-left: 15px;
        }
        ::v-deep {
          .el-form-item {
            margin-bottom: 0;
            &__label {
              position: relative;
              top: 12px;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              color: #666666;
            }
            .el-button {
              padding: 12px 32px;
              border-radius: 10px 10px 10px 10px;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 18px;
              &--medium {
                color: #555555;
                border-color: #dddee3;
                &:hover {
                  background: rgba($color: #274e6a, $alpha: 0.1);
                }
                &:active {
                  background: rgba($color: #274e6a, $alpha: 0.1);
                }
              }
              &--primary {
                color: #fff;
                background: #274e6a;
                border: none;
                &:hover {
                  background: #274e6a;
                }
              }
            }
          }
        }
      }
      .menuinfo {
        .question-property {
          display: flex;
          align-items: center;
          margin-bottom: 25px;
          ::v-deep {
            .el-form-item {
              margin-bottom: 0;
              &__label {
                position: relative;
                top: 7px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #666666;
              }

              .el-radio-group {
                .el-radio-button {
                  width: 68px;
                  height: 50px;
                  border-radius: 0px 0px 0px 0px;
                  // border: 1px solid #dadada;
                  &:first-of-type {
                    .el-radio-button__inner {
                      border-radius: 10px 0px 0px 10px;
                    }
                  }
                  &:last-of-type {
                    .el-radio-button__inner {
                      border-radius: 0px 10px 10px 0px;
                    }
                  }
                  &__inner {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #000;
                  }
                  &.is-active {
                    .el-radio-button__inner {
                      background: #274e6a;
                      color: #fff;
                      border-color: #274e6a;
                      box-shadow: -1px 0 0 0 #274e6a;
                    }
                  }
                }
              }
            }
          }
        }
        ::v-deep {
          // 通用
          .score-form-item,
          .num-form-item {
            .el-form-item__label {
              position: relative;
              top: 7px;
            }
            .score-input,
            .num-input {
              width: 294px;
              height: 50px;
              background: #dadde2;
              border-radius: 11px;
              overflow: hidden;
              &.is-disabled {
                opacity: 0.5;
              }
              .el-input-number__decrease,
              .el-input-number__increase {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 53px;
                height: 100%;
                background: #274e6a;
                i {
                  font-size: 20px;
                  color: rgba($color: #fff, $alpha: 0.9);
                }
              }
              .el-input-number__decrease {
                border-radius: 10px 0px 0px 10px;
              }
              .el-input-number__increase {
                border-radius: 0px 10px 10px 0px;
              }
              .el-input {
                height: 100%;
                .el-input__inner {
                  height: 100%;
                  background: #dadde2;
                  border: none;
                  color: #274e6a;
                  font-size: 30px;
                }
              }
            }
          }
          .select-difficulty-questionType {
            display: flex;
            align-items: center;
            .select-difficulty,
            .select-questionType {
              .el-form-item__label {
                position: relative;
                top: 5px;
              }
              .el-radio-group {
                .el-radio-button {
                  width: 68px;
                  height: 50px;
                  border-radius: 0px 0px 0px 0px;
                  // border: 1px solid #dadada;
                  &:first-of-type {
                    .el-radio-button__inner {
                      border-radius: 10px 0px 0px 10px;
                    }
                  }
                  &:last-of-type {
                    .el-radio-button__inner {
                      border-radius: 0px 10px 10px 0px;
                    }
                  }
                  &__inner {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #000;
                  }
                  &.is-active {
                    .el-radio-button__inner {
                      background: #274e6a;
                      color: #fff;
                      border-color: #274e6a;
                      box-shadow: -1px 0 0 0 #274e6a;
                    }
                  }
                }
              }
            }
            .select-questionType {
              padding-left: 50px;
            }
          }

          // 自定义添加
          .questioninput {
            width: 900px;
            padding-left: 10px;
            .el-form-item {
              align-items: flex-start;
              &__label {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #666666;
              }
              .small_inputbtn {
                display: flex;
                align-items: center;
                padding-top: 20px;
                padding-left: 10px;
              }
              .clear__button,
              .remove__button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 59px;
                height: 32px;
                padding: auto;
                background: #274e6a;
                border: none;
                border-radius: 4px 4px 4px 4px;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
              }
              .remove__button {
                background: #d94444;
              }
              .small_input {
                .el-form-item {
                  padding-left: 0;
                  margin-left: -30px;
                  &__label {
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 16px;
                    color: #666666;
                  }
                }
              }
              .add_input {
                .el-button {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 120px;
                  height: 40px;
                  padding: 0;
                  background: #ffffff;
                  border-radius: 60px 60px 60px 60px;
                  border: 1px solid #274e6a;
                  font-family: PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #274e6a;
                }
              }
              // 单选答案样式
              .answer-group,
              .scoreType-group {
                display: flex;
                align-items: center;
                margin-top: 9px;
                .answer-button,
                .socreType-button {
                  display: flex;
                  align-items: center;
                  .el-radio__input {
                    .el-radio__inner {
                      width: 18px;
                      height: 18px;
                      &:hover {
                        border-color: #274e6a;
                      }
                    }
                    &.is-checked {
                      .el-radio__inner {
                        background: #274e6a;
                        border-color: #274e6a;
                      }
                      &::after {
                        background: #fff;
                        width: 12px;
                        height: 12px;
                      }
                    }
                  }
                  .el-radio__label {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #000000;
                  }
                }
              }
              // 多选答案样式
              .anser-checkbox-group {
                .anser-checkbox {
                  .el-checkbox__input {
                    .el-checkbox__inner {
                      width: 18px;
                      height: 18px;
                      &:hover {
                        border-color: #274e6a;
                      }
                    }
                    &.is-checked {
                      .el-checkbox__inner {
                        background: #274e6a;
                        border-color: #274e6a;
                        &::after {
                          top: 3px;
                          left: 6px;
                        }
                      }
                    }
                  }
                  .el-checkbox__label {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #000000;
                  }
                }
              }
            }

            .ql-toolbar {
              background: #f0f0f0;
              border-color: #dddee3;
              border-radius: 4px 4px 0 0;
            }
            .ql-container {
              border-radius: 0px 0px 4px 4px;
              border-color: #dddee3;
            }
          }
          // 粘贴组件相关样式
          .pasteComponent {
            position: relative;
            left: -25px;
            width: 100%;

            .el-form-item__label {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 16px;
              color: #666666;
            }
            .paste-form-item {
              align-items: flex-start;

              .paste-input {
                .el-textarea__inner {
                  width: 883px;
                  height: 249px;
                  background: #fafafa;
                  border-radius: 8px 8px 8px 8px;
                  border: 1px solid #d7d7dc;
                  color: #333333;
                  &::placeholder {
                    color: #888888;
                  }
                }
              }
            }
            .create-col {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding-right: 50px;
              .create-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 102px;
                height: 48px;
                padding: auto;
                background: #274e6a;
                border: none;
                border-radius: 8px;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 18px;
                color: #ffffff;
              }
            }
          }
          // 智能组卷
          .simulationComponent {
            position: relative;
            left: -17px;
            .el-form {
              .el-form-item {
                margin-bottom: 30px;
                &__label {
                  font-family: PingFang SC;
                  font-weight: 500;
                  font-size: 16px;
                  color: #666666;
                }
              }
              .score-form-item,
              .num-form-item {
                margin-bottom: 20px;
              }
            }
            .simulation_all {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding-right: 83px;
              text-align: right;
              color: #666666;
              font-size: 20px;
              font-weight: 400;
              .maincolor {
                color: #274e6a;
              }
              .el-button {
                margin-left: 28px;
                padding: 12px 25px;
                background: #274e6a;
                border-radius: 10px 10px 10px 10px;
                border: none;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 16px;
                color: #ffffff;
                text-align: center;
              }
            }
          }
        }
      }
    }
    .menuinfo_list {
      position: fixed;
      top: 240px;
      right: 80px;
      width: 752px;
      height: 630px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #dddee3;
      transition: all 0.3s ease;
      overflow: auto;
      .menuinfo_top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 60px;
        padding: 0 30px;
        background: #fafafa;
        border-bottom: 1px solid #dddee3;
        .menuinfo-title {
          position: relative;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 24px;
          color: #000000;
          &::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: #274e6a;
          }
        }
        .menuinfo-total {
          display: flex;
          align-items: center;
          justify-self: center;

          & > span {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #000000;
          }
          & > span:last-of-type {
            margin-left: 35px;
          }
        }
      }
      .menuinfo_info {
        padding: 20px;
        .menuinfo_infoname {
          margin-bottom: 20px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #333333;
        }
        .menuinfo_infolist {
          .question_answer {
            position: relative;
            display: flex;
            align-items: center;

            width: 100%;
            height: 60px;
            padding: 0 20px;
            margin-bottom: 16px;

            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #dddee3;
            &.doing {
              background: rgba($color: #3d8aff, $alpha: 0.1);
            }
            .question-type {
              width: 60px;
              height: 26px;
              line-height: 26px;
              border-radius: 4px 4px 4px 4px;

              font-family: PingFang SC;
              font-weight: 400;
              font-size: 12px;
              color: #ffffff;
              text-align: center;
              &.SINGLE {
                background: #3d8aff;
              }
              &.MULTIPLE {
                background: #2eb2ff;
              }
              &.JUDGE {
                background: #009900;
              }
            }
            .question-content {
              margin-left: 20px;
              max-width: 65%;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 18px;
              color: #000000;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .question-operate {
              position: absolute;
              right: 20px;
              top: 50%;
              transform: translateY(-50%);
              display: flex;
              align-items: center;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 16px;
              .question-operate__edit {
                color: #5487ff;
                cursor: pointer;
              }
              .question-operate__delete {
                margin-left: 20px;
                color: #ff0808;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.el-scrollbar__wrap {
  overflow-x: hidden;
}
.save-operate {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 54px;
  .el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 93px;
    height: 50px;
    background: #274e6a;
    border-color: #274e6a;
    border-radius: 63px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 20px;
    color: #ffffff;
    &--info {
      color: #274e6a;
      background: #fff;
    }
    &.is-disabled {
      opacity: 0.6;
    }
  }
}
.selectQuestion-dialog {
  width: 1200px;
  height: 800px;
  background: #ffffff;
  border-radius: 30px 30px 30px 30px;
  overflow: hidden;
  .el-dialog__header {
    padding: 30px 0 0 0;
    .el-dialog__title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
    }
    .el-dialog__headerbtn {
      top: 36px;
      right: 30px;
      .el-dialog__close {
        font-size: 24px;
        color: #666666;
        font-weight: 600;
      }
    }
  }
  .el-dialog__body {
    overflow: auto;
    .selectQuestion-num {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #333333;
    }

    .selectQuestion-search {
      display: flex;
      align-items: center;
      margin: 20px 0;
      .search-field {
        display: flex;
        align-items: center;
        margin-right: 30px;
        &__label {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #666666;
        }
        &__input {
          width: 350px;
          height: 45px;

          .el-input__inner {
            width: 100%;
            height: 100%;
            border-radius: 74px;
            background: #eef0f2;
            border: none;
            color: #333333;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            &::placeholder {
              color: #cccccc;
            }
          }
          .el-input__suffix {
            top: 3px;
            right: 10px;
            .el-input__suffix-inner {
              .el-icon-circle-close {
                font-size: 20px;
              }
            }
          }
        }
        &__radio {
          display: flex;
          .el-radio {
            display: flex;
            align-items: center;
            margin-right: 40px;
            &:last-of-type {
              margin-right: 0;
            }
            .el-radio__inner {
              width: 16px;
              height: 16px;
              background: #fff;
              border-color: #b1b1b1;
            }
            .el-radio__label {
              padding-left: 6px;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              color: #b1b1b1;
            }
          }

          .el-radio__input.is-checked .el-radio__inner {
            &::after {
              background: #274e6a;
              width: 10px;
              height: 10px;
            }
          }
          .el-radio__input.is-checked + .el-radio__label {
            color: #274e6a;
          }
        }
      }
      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 45px;
        padding: 0 18px;
        margin-right: 20px;
        background: #65849a;
        border-radius: 63px 63px 63px 63px;
        border: none;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
      }
    }
    .selectQuestion-table {
      .el-table {
        &::before {
          display: none;
        }
        .tableCellClassName {
          height: 55px;
          background: #f6f8fa;
          border-bottom: 4px solid #fff;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #333333;
          &:first-of-type {
            border-radius: 8px 0 0 8px;
          }
          &:nth-of-type(8) {
            border-radius: 0 8px 8px 0;
          }

          .el-checkbox {
            &__input.is-checked {
              .el-checkbox__inner {
                background-color: #274e6a;
                border-color: #274e6a;
              }
            }
            &__inner {
              width: 28px;
              height: 28px;
              border-radius: 4px 4px 4px 4px;
              &::after {
                width: 8px;
                height: 14px;
                left: 8px;
                top: 2px;
                border-width: 2px;
              }
            }
          }
        }
        .tableHeaderClassName {
          height: 55px;
          border-bottom: 4px solid #fff;
          background: #f6f8fa;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #999999;
          &:first-of-type {
            border-radius: 8px 0 0 8px;
          }
          &:nth-of-type(8) {
            border-radius: 0 8px 8px 0;
          }
          .el-checkbox {
            &__input.is-checked,
            &__input.is-indeterminate {
              .el-checkbox__inner {
                background-color: #274e6a;
                border-color: #274e6a;
              }
            }
            &__inner {
              width: 28px;
              height: 28px;
              border-radius: 4px 4px 4px 4px;
              &::after {
                width: 8px;
                height: 14px;
                left: 8px;
                top: 2px;
                border-width: 2px;
              }
              &::before {
                width: 14px;
                top: 12px;
                left: 50%;
                transform: translateX(-50%) scale(1);
              }
            }
          }
        }
      }
    }
    .selectQuestion-pagination {
      position: absolute;
      left: 50%;
      bottom: 50px;
      transform: translateX(-50%);
      .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #65849a;
        color: #eef0f2;
      }
      .el-pagination.is-background .el-pager li {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        background-color: #eef0f2;
        color: #65849a;
      }
      .el-pagination.is-background .btn-prev,
      .el-pagination.is-background .btn-next {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        background-color: #eef0f2;
        color: #65849a;
        font-size: 16px;
      }
      .el-pagination.is-background .btn-prev:disabled {
        color: rgba($color: #65849a, $alpha: 0.3);
      }
      .el-pagination__total {
        height: 40px;
        line-height: 40px;
        color: #fff;
        font-size: 15px;
      }
    }
  }
  .el-dialog__footer {
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
.el-tooltip__popper {
  font-size: 15px;
  line-height: 26px;
}
</style>
