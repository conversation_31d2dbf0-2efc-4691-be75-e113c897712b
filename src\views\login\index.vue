<template>
  <div class="login-container">
    <div class="login-logo">
      <img src="@/assets/images/logo.png" alt="" />
    </div>
    <div class="login-form">
      <div class="login-form_banner">
        <img src="@/assets/images/login_banner.png" alt="" />
      </div>
      <div class="login-form_content">
        <div class="login_title">SP开放性人机对话系统</div>
        <div class="login_subtitle">管理端登录</div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="submit-form" autocomplete="on" label-position="left">
          <el-form-item prop="username">
            <el-input ref="username" v-model="loginForm.username" size="large" placeholder="请输入用户名" name="username" type="text" tabindex="1" autocomplete="on">
              <i slot="prefix" class="el-icon-s-custom login_formicon"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input ref="password" v-model="loginForm.password" size="large" placeholder="请输入密码" name="password" tabindex="2" autocomplete="on" show-password @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="lock2" class="login_formicon"></svg-icon>
            </el-input>
          </el-form-item>
          <el-button :loading="loading" class="submit_button" @click.native.prevent="handleLogin">登 录</el-button>
        </el-form>
      </div>
    </div>
    <div class="login-corporation">开发单位：山东中飞科技有限公司</div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能少于三位!'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入用户名'
          }
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePassword,
            message: '请输入密码'
          }
        ]
      },
      passwordType: 'password',
      loading: false,
      redirect: undefined,
      otherQuery: {}
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {},
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          var loginform = {
            username: this.loginForm.username,
            password: this.loginForm.password
          }
          this.$store
            .dispatch('user/login', loginform)
            .then((res) => {
              if (res.code == '200') {
                this.$router.push({
                  path: this.redirect || '/',
                  query: this.otherQuery
                })
                this.loading = false
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
                this.loading = false
              }
            })
            .catch((err) => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>
<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  padding-top: 150px;
  background: url('~@/assets/images/bg.png') no-repeat;
  background-size: cover;
  .login-logo {
    position: absolute;
    left: 77px;
    top: 65px;
  }
  .login-form {
    display: flex;
    align-items: center;
    width: 1300px;
    height: 668px;
    margin: 0 auto;
    // background: #fff;
    box-shadow:
      0px 11px 40px 0px rgba(7, 26, 40, 0.5),
      2px -6px 40px 0px rgba(17, 31, 44, 0.2);
    border-radius: 40px 40px 40px 40px;
    overflow: hidden;

    .login-form_banner {
      height: 100%;
      width: 654px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .login-form_content {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;
      padding-top: 80px;
      background: #fff;
      .login_title {
        font-family: PingFang SC;
        font-size: 40px;
        color: #333333;
      }
      .login_subtitle {
        position: relative;
        margin-top: 20px;
        font-family: PingFang SC;
        font-size: 32px;
        color: #517289;
        &::before {
          content: '';
          position: absolute;
          left: 50%;
          bottom: -8px;
          transform: translateX(-50%);
          width: 60px;
          height: 4px;
          background: #65849a;
          border-radius: 70px 70px 70px 70px;
        }
      }
      ::v-deep {
        .submit-form {
          margin-top: 55px;
          .el-form-item {
            margin-bottom: 45px;
          }
          .el-form-item__error {
            margin-top: 5px;
            font-size: 15px;
          }
          .el-input {
            position: relative;
          }
          .el-input__inner {
            width: 480px;
            height: 67px;
            padding-left: 60px;
            padding-right: 60px;
            background: #f7f7f7;
            border-radius: 14px 14px 14px 14px;
            border: 1px solid #65849a;
            color: #000;
            font-size: 18px;
          }
          .el-input__prefix {
            position: absolute;
            left: 16px;
            top: 18px;
            .login_formicon {
              font-size: 30px;
              color: #65849a;
            }
          }
          .el-input__suffix {
            .el-input__icon {
              position: absolute;
              right: 20px;
              top: 20px;
              width: 26px;
              height: 26px;
              line-height: 26px;
              font-size: 26px;
            }
          }
          .submit_button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 480px;
            height: 66px;
            margin-top: 80px;
            padding: 0;
            background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
            border: none;
            border-radius: 63px 63px 63px 63px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
          }
        }
      }
    }
  }
  .login-corporation {
    position: absolute;
    left: 50%;
    bottom: 35px;
    transform: translateX(-50%);
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #ffffff;

    // 媒体查询 高度小于800隐藏
    @media screen and (max-height: 900px) {
      display: none;
    }
  }
}
</style>
