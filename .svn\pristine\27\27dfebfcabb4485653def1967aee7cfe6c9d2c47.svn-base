<template>
  <div class="login_container">
    <img src="@/assets/images/login_containerback.png" class="login_containerback" />
    <img src="@/assets/images/login_containericon.png" class="login_containericon" />
    <div class="login_title">
      <div class="login_title1">SP开放性人机对话系统</div>
      <div class="login_title2">SP open man-machine dialogue system</div>
    </div>
    <div class="login_form">
      <img src="@/assets/images/login_formback.png" class="login_formback" />
      <div class="login_formcont">
        <div class="login_formtitle">用户登录</div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">
          <el-form-item prop="username">
            <el-input ref="username" v-model="loginForm.username" size="large" placeholder="请输入用户名" name="username" type="text" tabindex="1" autocomplete="on">
              <i slot="prefix" class="el-icon-s-custom login_formicon"></i>
            </el-input>
          </el-form-item>
          <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
            <el-form-item prop="password">
              <el-input ref="password" v-model="loginForm.password" size="large" placeholder="请输入密码" name="password" tabindex="2" autocomplete="on" show-password @keyup.enter.native="handleLogin">
                <i slot="prefix" class="el-icon-lock login_formicon"></i>
              </el-input>
            </el-form-item>
          </el-tooltip>
          <el-button :loading="loading" type="primary" class="login_formbtn" @click.native.prevent="handleLogin">登录</el-button>
        </el-form>
      </div>
    </div>
    <div class="login_bottom">开发单位 | 山东中飞科技有限公司</div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能少于三位!'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入用户名'
          }
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePassword,
            message: '请输入密码'
          }
        ]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {}
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {},
  methods: {
    checkCapslock({ shiftKey, key } = {}) {
      if (key && key.length === 1) {
        if ((shiftKey && key >= 'a' && key <= 'z') || (!shiftKey && key >= 'A' && key <= 'Z')) {
          this.capsTooltip = true
        } else {
          this.capsTooltip = false
        }
      }
      if (key === 'CapsLock' && this.capsTooltip === true) {
        this.capsTooltip = false
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          var loginform = {
            username: this.loginForm.username,
            password: this.loginForm.password
          }
          this.$store
            .dispatch('user/login', loginform)
            .then((res) => {
              if (res.code == '200') {
                this.$router.push({
                  path: this.redirect || '/',
                  query: this.otherQuery
                })
                this.loading = false
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
                this.loading = false
              }
            })
            .catch((err) => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>
