<template>
  <div class="div-user-sum">
    <div class="div-top">
      <span>班级管理</span>
    </div>
    <el-button type="primary" style="margin: 0 0 10px 10px" @click="openAddDept(0)" icon="el-icon-circle-plus-outline">添加班级</el-button>
    <div style="margin: 10px">
      <el-table :data="tableDate" row-key="id" border :tree-props="treeProps">
        <el-table-column prop="clbumName" label="班级名称"> </el-table-column>
        <el-table-column prop="num" label="序号"> </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="primary" @click="openAddDept(scope.row.id)" size="small">添加下级</el-button>
            <el-button type="primary" @click="openEditDept(scope.row)" plain size="small">编辑</el-button>
            <el-button type="danger" @click="removeRouter(scope.row.id)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="添加班级" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="saveClbumDialog" width="350px">
      <el-form :model="addForm" :rules="rules" ref="addForm">
        <el-form-item label="班级名称" prop="clbumName" :label-width="labelWidth">
          <el-input v-model="addForm.clbumName" placeholder="请输入班级名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="班级序号" prop="num" :label-width="labelWidth">
          <el-input v-model="addForm.num" placeholder="请输入班级序号" maxlength="20" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="saveClbumReset">取 消</el-button>
        <el-button type="primary" @click="toAddDept" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑班级" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="editDeptDialog" width="350px">
      <el-form :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="班级名称" prop="clbumName" :label-width="labelWidth">
          <el-input v-model="editForm.clbumName" placeholder="请输入班级名称" maxlength="20" clearable> </el-input>
        </el-form-item>
        <el-form-item label="班级序号" prop="num" :label-width="labelWidth">
          <el-input v-model="editForm.num" placeholder="请输入班级序号" maxlength="20" clearable> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editRouterReset">取 消</el-button>
        <el-button type="primary" @click="editDept">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { clbumTree, saveClbum, updateClbum, removeClbum } from '@/api/clbum.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else if (Number(value) < 0) {
        return callback(new Error('请输入大于零的数'))
      } else {
        return callback()
      }
    }
    return {
      tableDate: [],
      dataloading: false,
      saveClbumDialog: false,
      editDeptDialog: false,
      labelWidth: '90px',
      treeProps: {
        children: 'children',
        hasChildren: null
      },
      addForm: {
        parentId: '',
        clbumName: '',
        num: ''
      },
      editForm: {
        clbumId: '',
        parentId: '',
        clbumName: '',
        num: ''
      },
      rules: {
        clbumName: [
          {
            required: true,
            message: '请输入班级名称'
          }
        ],
        num: [
          {
            validator: checkNumber,
            trigger: 'blur'
          },
          {
            required: true,
            message: '请输入班级序号'
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.getDeptList()
      })
    },
    getDeptList() {
      clbumTree({
        schoolId: this.userinfo.schoolId
      }).then(async (res) => {
        this.tableDate = res.data
      })
    },
    openAddDept(parentId) {
      this.saveClbumDialog = true
      this.addForm.parentId = parentId
    },
    toAddDept() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm)
          data.schoolId = this.userinfo.schoolId
          saveClbum(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getDeptList()
              this.saveClbumReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    saveClbumReset() {
      this.saveClbumDialog = false
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
    },
    openEditDept(item) {
      this.editDeptDialog = true
      this.editForm = Object.assign({}, item)
    },
    editDept() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.editForm)
          data.clbumId = data.id
          updateClbum(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.getDeptList()
              this.editRouterReset()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    editRouterReset() {
      this.editDeptDialog = false
      this.$refs.editForm.resetFields()
      this.$refs.editForm.clearValidate()
    },
    removeRouter(clbumId) {
      this.$confirm('是否删除该班级及其下级班级?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeClbum({
            clbumId: clbumId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getDeptList()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
