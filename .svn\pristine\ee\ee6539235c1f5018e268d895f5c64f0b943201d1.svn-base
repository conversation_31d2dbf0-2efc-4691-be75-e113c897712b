import axios from 'axios'
/** 通过axios调用模型 */
export function aiRequest(system, messages, stream) {
  return new Promise((resolve, reject) => {
    const token = 'sk-dcyfbjpinydpwjcxrachjietkvouoggjvpkoxcfjpelzumiu'
    const model = 'Pro/deepseek-ai/DeepSeek-V3'
    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
    const options = {
      method: 'POST',
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: messages
          },
          {
            role: 'system',
            content: system
          }
        ],
        stream: stream,
        max_tokens: 1500
      })
    }

    // headers放到请求头中
    axios
      .post('https://api.siliconflow.cn/v1/chat/completions', options.body, { headers })
      .then((response) => resolve(response.data))
      .catch((err) => reject(err))
  })
}
