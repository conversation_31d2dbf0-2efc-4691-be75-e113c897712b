<template>
  <div>
    <div class="topic_des">
      难度：<span v-for="item in difficultys" v-if="item.value == form.difficulty">{{ item.name }}</span>
    </div>
    <div class="topic_question editor_box" v-html="form.question"></div>
    <div class="topic_selection" v-for="item in form.list">
      <div class="topic_answermr topic_answer">学生答案：<span class="editor_box" v-html="item.userAnswer"></span></div>
      <div class="topic_answermr topic_answer">正确答案：<span class="editor_box" v-html="item.answer"></span></div>
      <div class="topic_answermr topic_answer">结果：{{ item.isError=='1'?'错':'对' }}</div>
      <div class="topic_answermr topic_answer" v-if="!edit">得分：{{ item.questionScore }}</div>
      <div class="topic_answermr topic_answer">解析：<span class="editor_box" v-html="item.analysis"></span></div>
      <div class="topic_answermr topic_answer" v-if="edit">
        得分：
        <el-input-number v-model="editform.questionScore" @change="handleChange" :min="0" :max="item.score" label="得分"></el-input-number>
      </div>
      <div class="topic_answermr topic_answer" v-if="edit">批阅：</div>
      <div class="topic_answermr topic_answer" v-if="edit">
        <el-input type="textarea" :rows="2" @change="handleChange" placeholder="请填写批阅内容" v-model="editform.questionCorrectContent"> </el-input>
      </div>
      <div class="topic_answermr topic_answer" v-if="!edit">批阅内容：{{ editform.questionCorrectContent }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      required: true
    },
    letters: {
      type: Array,
      required: true
    },
    difficultys: {
      type: Array,
      required: true
    },
    edit: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      editform: {
        questionScore: 0,
        questionCorrectContent: ''
      },
      rules: {
        questionScore: [
          {
            required: true,
            message: '请输入得分'
          }
        ],
        questionScore: [
          {
            required: true,
            message: '请填写批阅内容'
          }
        ]
      }
    }
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      var form = this.form
      if (form.list && form.list.length > 0) {
        form.list.map((item) => {
          if (item.options) {
            var optionObject = JSON.parse(item.options)
            item.optionObject = optionObject
            item.optionsArr = Object.values(optionObject)
          } else {
            item.optionObject = {}
            item.optionsArr = []
          }
          if (item.questionScore) {
            this.editform.questionScore = item.questionScore
          }
          if (item.questionCorrectContent) {
            this.editform.questionCorrectContent = item.questionCorrectContent
          }
        })
      } else {
        form.list = []
      }
    },
    handleChange() {
      this.$emit('beforeCorrect', {
        questionScore: this.editform.questionScore,
        questionCorrectContent: this.editform.questionCorrectContent
      })
    }
  }
}
</script>
