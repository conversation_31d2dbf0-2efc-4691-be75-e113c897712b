import axios from 'axios'
import { Message } from 'element-ui'

// create an axios instance
const service = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  baseURL: window.config.VUE_AI_BASE_API // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  // timeout: 10000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const res = response.data
    if (res.code && res.code === 500) {
      return Promise.reject(new Error(res.message || 'Error'))
    }
    // if the custom code is not 20000, it is judged as an error.
    // if (res.code !== 200 && res.status !== 200 && res !== '注销成功') {
    //   Message({
    //     message: res.message || 'Error',
    //     type: 'error',
    //     duration: 5 * 1000
    //   })
    //   return Promise.reject(new Error(res.message || 'Error'))
    // } else {
    // }
    return res
  },
  (error) => {
    if (error.message === 'Network Error') {
      Message({
        message: '网络出错，请稍后重试',
        type: 'error',
        duration: 5 * 1000
      })
    } else if (error.message === 'timeout of 10000ms exceeded') {
      Message({
        message: '网络连接超时，请稍后再试',
        type: 'error',
        duration: 5 * 1000
      })
    }
    return Promise.reject(error)
  }
)
export default service
