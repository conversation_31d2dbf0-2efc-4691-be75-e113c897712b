<template>
  <div class="">
    <el-dialog custom-class="prepareTestDialog" :visible.sync="dialogVisible" :show-close="false" @close="close">
      <div v-if="info">
        <div class="content">
          <div class="leftBox">
            <span>Hi，我是模拟人:{{ info.realName }}</span>
            <img :src="personImg" alt="" />
          </div>
          <div class="rightBox">
            <div class="title">模拟人【{{ info.realName }}】初始化中...</div>
            <div class="info">
              <div class="info_title">病例档案</div>
              <div class="info_content">
                <div class="info_content_baseInfo">
                  <span><i>姓名：</i>{{ info.realName }}</span>
                  <span><i>性别：</i>{{ info.sex === 'F' ? '女' : '男' }}</span>
                  <span><i>年龄：</i>{{ info.age }}</span>
                </div>
                <div class="info_content_mainDemands">
                  <span>病例简介:</span>
                  <p>{{ info.mainDemands }}</p>
                </div>
              </div>
            </div>
            <div class="startBtn" @click="start">
              点击开始对话吧 <i>( {{ num }}s )</i>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      info: null,
      timer: null,
      num: 5
    }
  },
  created() {},
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  computed: {
    personImg() {
      const images = require.context('@/assets/case/ai', false, /\.png$/)
      let imgUrl = 'testCode-'
      const age = this.info.age
      if (this.info.sex === 'M') {
        imgUrl += age < 17 ? 'man1.png' : age < 68 ? 'man2.png' : 'man3.png'
      } else {
        imgUrl += age < 17 ? 'woman1.png' : age < 68 ? 'woman2.png' : 'woman3.png'
      }
      return images(`./${imgUrl}`)
    }
  },
  methods: {
    open(info) {
      this.info = info
      this.dialogVisible = true
      this.countDown()
    },
    close() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    countDown() {
      this.timer = setInterval(() => {
        this.num -= 1
        if (this.num <= 0) {
          clearInterval(this.timer)
          this.timer = null
          this.start()
        }
      }, 1000)
    },
    start() {
      this.$router.push(`/case/testCaseDialogue/${this.info.caseId}`)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .prepareTestDialog {
    width: 1424px;
    height: 705px;
    padding-top: 30px;
    background: url('~@/assets/case/ai/prepareTestDialogBg.png') no-repeat;
    background-size: cover;
    border-radius: 30px;
    .content {
      display: flex;
      align-items: center;
      justify-content: center;
      .leftBox {
        position: relative;
        width: 410px;
        height: 500px;
        background: url('~@/assets/case/ai/personBg.png') no-repeat;
        background-size: contain;
        & > span {
          position: absolute;
          left: 28px;
          top: 7px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #ffffff;
        }
        img {
          position: absolute;
          left: -10px;
          top: -40px;
          width: 410px;
          height: 500px;
          transform: scale(0.75);
        }
      }
      .rightBox {
        margin-left: 100px;
        .title {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 35px;
          color: #ffffff;
        }
        .info {
          width: 623px;
          margin-top: 36px;
          .info_title {
            width: 144px;
            height: 45px;
            background: rgba(40, 74, 95, 0.8);
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            clip-path: polygon(0% 0%, 80% 0%, 100% 30%, 100% 100%, 0% 100%); /* 定义梯形的形状 */
          }
          .info_content {
            width: 623px;
            height: 258px;
            padding: 30px;
            background: rgba(18, 40, 53, 0.3);
            .info_content_baseInfo {
              display: flex;
              align-items: center;
              & > span {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #ffffff;
                &:nth-of-type(2) {
                  margin: 0 70px;
                }
                i {
                  color: rgba(255, 255, 255, 0.5);
                  font-style: normal;
                }
              }
            }
            .info_content_mainDemands {
              width: 100%;
              min-height: 168px;
              max-height: 200px;
              margin-top: 20px;
              padding: 20px 23px;
              background: #bfd6e3;
              border-radius: 10px;
              overflow: auto;
              &::-webkit-scrollbar {
                background: #fff;
              }
              &::-webkit-scrollbar-thumb {
                background: #b0b0b0;
                border-radius: 5px;
              }
              & > span {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #666666;
              }
              p {
                margin-bottom: 0;
                margin-top: 7px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #000000;
                line-height: 30px;
              }
            }
          }
        }
        .startBtn {
          width: 266px;
          height: 52px;
          margin-left: auto;
          margin-top: 50px;
          background: linear-gradient(360deg, #ff6a38 0%, #ff9c49 100%);
          box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.5);
          border-radius: 10px 10px 10px 10px;
          border: 2px solid #ffbb4f;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          line-height: 48px;
          text-align: center;
          cursor: pointer;
          i {
            font-style: normal;
            font-size: 18px;
          }
        }
      }
    }
  }
}
</style>
