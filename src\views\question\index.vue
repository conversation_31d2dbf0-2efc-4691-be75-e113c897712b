<template>
  <div class="questionLayout-container">
    <div class="questionLayout-header">
      <div class="header_back" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="header_selectModule">
        <i class="active-bar" :style="activeBarStyle"></i>
        <span :class="{ checked: isCheckedQuestion }" @click="checkedModule = 'question'">题目管理</span>
        <span :class="{ checked: !isCheckedQuestion }" @click="checkedModule = 'errorQuestion'">出错题目</span>
      </div>
    </div>
    <div class="questionLayout-body">
      <Question v-if="isCheckedQuestion" />
      <Error v-else />
    </div>
  </div>
</template>
<script>
import question from './question.vue'
import error from './error.vue'
export default {
  name: '',
  components: {
    Question: question,
    Error: error
  },
  data() {
    return {
      checkedModule: 'question'
    }
  },
  computed: {
    activeBarStyle() {
      return `transform: translateX(${this.checkedModule === 'question' ? 0 : 140}px)`
    },
    isCheckedQuestion() {
      return this.checkedModule === 'question'
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped lang="scss">
.questionLayout-container {
  width: 100%;
  height: 100%;
  padding: 0 50px;
  background: linear-gradient(180deg, #d0dae4 0%, #f4f4f4 100%);
  .questionLayout-header {
    position: relative;
    height: 105px;
    .header_back {
      position: absolute;
      left: 50px;
      top: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .header_selectModule {
      position: absolute;
      left: 50%;
      top: 30px;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 280px;
      height: 48px;
      background: #ffffff;
      border-radius: 60px 60px 60px 60px;
      border: 1px solid #d6d6d6;
      span {
        width: 140px;
        text-align: center;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #274e6a;
        cursor: pointer;
      }
      .checked {
        color: #ffffff;
      }
      .active-bar {
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
        width: 140px;
        height: 48px;
        transition: all 0.2s;
        background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
        border-radius: 63px 63px 63px 63px;
      }
    }
  }
  .questionLayout-body {
    width: 100%;
    height: 790px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px 30px 30px 30px;
    overflow: hidden;
  }
}
</style>
