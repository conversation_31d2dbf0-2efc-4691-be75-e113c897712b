<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="addDialog" width="650px" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="85px" inline>
        <el-form-item label="病例名称:" prop="name">
          <el-input v-model="form.name" size="small" maxlength="40" placeholder="请输入病例名称"></el-input>
        </el-form-item>
        <el-form-item label="病例类型:" prop="caseType">
          <el-radio-group v-model="form.caseType">
            <el-radio :label="1">学习病例</el-radio>
            <el-radio :label="2">考核病例</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="系统分类:" prop="form">
          <el-select v-model="form.form" placeholder="请选择系统分类" size="small">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名:" prop="realName">
          <el-input v-model="form.realName" size="small" maxlength="40" placeholder="请输入病例名称"></el-input>
        </el-form-item>
        <el-form-item label="年龄:">
          <el-input-number v-model="form.age" :precision="0" size="small" :min="1" :max="120" label="请输入年龄"></el-input-number>
        </el-form-item>
        <el-form-item label="性别:">
          <el-radio-group v-model="form.sex">
            <el-radio label="F">女</el-radio>
            <el-radio label="M">男</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="病例简介:" class="mainDemands">
          <el-input v-model="form.mainDemands" type="textarea" size="small" maxlength="1000" placeholder="请输入病例简介" resize="none" rows="8"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseFormList } from '@/filters'
import { caseAdd, caseUpdate } from '@/api/case'
export default {
  name: '',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    }
  },
  computed: {
    dialogTitle() {
      return this.form.caseId ? '编辑病例' : '添加病例'
    }
  },
  data() {
    return {
      form: {
        name: null,
        caseType: 1,
        form: null,
        realName: null,
        age: null,
        sex: 'F',
        mainDemands: null
      },
      options: caseFormList,
      rules: {
        name: [{ required: true, message: '请输入病例名称', trigger: 'blur' }],
        caseType: [{ required: true, message: '请选择病例类型', trigger: 'change', type: 'number' }],
        form: [{ required: true, message: '请选择系统分类', trigger: 'change', type: 'number' }],
        realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    close() {
      this.form = {
        name: null,
        caseType: 1,
        form: null,
        realName: null,
        age: null,
        sex: 'F',
        mainDemands: null
      }
      this.$refs['form'].resetFields()
      this.$emit('update:addDialog', false)
    },
    confirm() {
      this.$refs['form'].validate((val) => {
        if (val) {
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.form.caseId) {
            caseUpdate(this.form)
              .then(() => {
                this.$message.success('修改病例成功！')
                loading.close()
                this.close()
                this.$emit('success')
              })
              .catch((err) => {
                console.log(err)
                loading.close()
              })
          } else {
            caseAdd({ ...this.form, allScore: 0 })
              .then(() => {
                this.$message.success('新增病例成功！')
                loading.close()
                this.close()
                this.$emit('success')
              })
              .catch((err) => {
                console.log(err)
                loading.close()
              })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding-top: 0;
  }
  .el-input__inner {
    width: 200px;
  }
  .el-input-number {
    width: 200px;
  }
  .mainDemands {
    width: 100%;
    .el-form-item__content {
      width: calc(100% - 85px);
    }
  }
}
</style>
