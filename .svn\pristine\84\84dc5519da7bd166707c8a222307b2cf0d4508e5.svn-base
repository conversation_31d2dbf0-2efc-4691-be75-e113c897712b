<template>
  <div class="app-container">
    <el-row class="pageTop">
      <el-button class="goBack" type="primary" size="small" icon="el-icon-back" @click="goBack">返回</el-button>
      <div class="title">理论成绩</div>
    </el-row>
    <div class="content">
      <ul class="examInfo">
        <li>
          <span class="infoLabel">考试名称：</span>
          <span class="info_text">{{ paperInfo.paperName }}</span>
        </li>
        <li>
          <span class="infoLabel">考试班级：</span>
          <span class="info_text">{{ paperInfo.paperName }}</span>
        </li>
        <li>
          <span class="infoLabel">考试限时：</span>
          <span class="info_text">{{ paperInfo.duration }}分</span>
        </li>
        <li>
          <span class="infoLabel">总分：</span>
          <span class="info_text"> {{ paperInfo.totalScore }}分</span>
        </li>
        <li>
          <span class="infoLabel">及格分：</span>
          <span class="info_text">{{ paperInfo.passScore }}分</span>
        </li>
        <li>
          <span class="infoLabel">应考人数：</span>
          <span class="info_text" :style="{ color: '#1890FF', fontWeight: 'bold' }">{{ paperInfo.totalCount }}分</span>
        </li>
        <li>
          <span class="infoLabel">实考人数：</span>
          <span class="info_text" :style="{ color: '#1890FF', fontWeight: 'bold' }">{{ paperInfo.realExamineUserCount }}分</span>
        </li>
        <li>
          <span class="infoLabel">及格人数：</span>
          <span class="info_text" :style="{ color: '#1890FF', fontWeight: 'bold' }">{{ paperInfo.isPassUserCount }}分</span>
        </li>
      </ul>
      <div class="tableBox">
        <div class="title">全部考核成绩</div>
        <div class="search">
          <span class="infoLabel">姓名：</span>
          <el-select v-model="isFinish" placeholder="请选择提交状态" style="width: 200px" clearabled>
            <el-option v-for="item in isFinishs" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <span class="infoLabel" :style="{ marginLeft: '30px' }">提交状态：</span>
          <el-select v-model="isFinish" placeholder="请选择提交状态" style="width: 200px" clearabled>
            <el-option v-for="item in isFinishs" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <!-- <span class="infoLabel">批改状态：</span>
          <el-select v-model="isReview" placeholder="请选择批改状态" style="width: 200px" clearabled>
            <el-option v-for="item in isReviews" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select> -->
          <div class="searchButton">
            <el-button type="primary" @click="currentChange(1)" size="medium">查询</el-button>
            <el-button type="primary" plain @click="exportUser" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出全部' }}</el-button>
          </div>
        </div>
        <el-table :data="exams" row-key="correctId" header-cell-class-name="tableHeader" cell-class-name="tableCell" @selection-change="selectionChange">
          <el-table-column type="selection" width="55" align="center"> </el-table-column>
          <el-table-column prop="loginName" align="center" label="学号"> </el-table-column>
          <el-table-column prop="name" align="center" label="姓名"> </el-table-column>
          <el-table-column prop="sex" align="center" label="性别">
            <template slot-scope="{ row }">
              <span>{{ row.sex === 'M' ? '男' : '女' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="clbumName" align="center" label="班级"> </el-table-column>
          <el-table-column prop="isFinish" align="center" label="提交状态">
            <template slot-scope="{ row }">
              <span :class="`isFinish${row.isFinish}`">{{ row.isFinish ? '已提交' : '未提交' }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="isReview" align="center" label="批改状态">
            <template slot-scope="scope">
              <span v-if="scope.row.isFinish == 1">
                <span v-if="scope.row.isComplain == 2">成绩申诉</span>
                <span v-else>
                  <span v-for="item in isReviews" v-if="scope.row.isReview == item.value">{{ item.label }}</span>
                </span>
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="paperScore" align="center" label="得分">
            <template slot-scope="scope">
              <span v-if="scope.row.isFinish == 1">
                <span v-if="scope.row.lastPaperScore">
                  <span style="text-decoration: line-through">{{ scope.row.paperScore || 0 }}</span>
                  &nbsp;
                  <span>{{ scope.row.lastPaperScore || 0 }}分</span>
                </span>
                <span v-else>{{ scope.row.paperScore || 0 }}</span>
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="duration" align="center" label="耗时（分钟）">
            <template slot-scope="scope">
              <span v-if="scope.row.isFinish == 1">
                {{ scope.row.duration }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="paperEndTime" align="center" label="提交时间">
            <template slot-scope="scope">
              <span v-if="scope.row.isFinish == 1">
                {{ scope.row.paperEndTime }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template slot-scope="scope">
              <div v-if="scope.row.isFinish == 1">
                <el-button type="primary" @click="openViewInfo(scope.row, 'edit')" size="mini" v-if="scope.row.isReview == 3">重新批改</el-button>
                <el-button type="primary" @click="openViewInfo(scope.row, 'edit')" size="mini" v-if="scope.row.isReview == 2 && getInTime(scope.row)">批改</el-button>
                <span v-if="scope.row.isReview == 2 && !getInTime(scope.row)">批改时间已过</span>
                <el-button type="primary" @click="openViewInfo(scope.row, 'view')" size="mini" v-if="scope.row.isReview == 1">查看</el-button>
                <!--<el-button type="success" @click="exportUserItem(scope.row)" size="mini">导出</el-button>-->
                <el-button type="danger" @click="openCorrigendum(scope.row)" size="mini" v-if="scope.row.isComplain == 2">成绩勘误</el-button>
              </div>
              <div v-else>未提交</div>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin: 10px; text-align: center">
          <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
        </div>
      </div>
    </div>
    <!-- <div class="top_info">
      <span class="info_label">试题数量：</span>
      <span>{{ paperInfo.questionCount }}</span>
      <span class="info_label">实考人数：</span>
      <span>{{ paperInfo.realExamineUserCount }}</span>
      <span class="info_label">总人数：</span>
      <span>{{ paperInfo.totalCount }}</span>
      <br />
      <span class="info_label">试卷分数：</span>
      <span>{{ paperInfo.totalScore }}</span>
      <span class="info_label">及格分数：</span>
      <span>{{ paperInfo.passScore }}</span>
      <span class="info_label">考试时长：</span>
      <span>{{ paperInfo.duration }}分钟</span>
      <span class="info_label">待批改：</span>
      <span>{{ paperInfo.waitCorrectCount }}</span>
      <br />
      <span class="info_label">考试时间：</span>
      <span>{{ paperInfo.startTime }} 至 {{ paperInfo.endTime }}</span>
      <span class="info_label">批阅时间：</span>
      <span>{{ paperInfo.correctStartTime }} 至 {{ paperInfo.correctEndTime }}</span>
      <span class="info_label">成绩发布：</span>
      <span>{{ paperInfo.scorePublishTime }}</span>
      <span class="info_label">申诉截止：</span>
      <span>{{ paperInfo.complainEndTime }}</span>
    </div> -->

    <el-dialog title="成绩勘误" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="corrigendumDialog" width="550px" class="correct_complain">
      <el-form :model="addForm" ref="addForm">
        <el-form-item label="勘误原因" prop="complainReason">
          {{ addForm.complainReason }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="corrigendumReset">取 消</el-button>
        <el-button type="danger" @click="corrigendumReturn">勘误驳回</el-button>
        <el-button type="primary" @click="corrigendumReTry">重新批阅</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { paperRecordDetail, paperUserRecordList, rejectComplain, anewComplain, paperCorrectExport } from '@/api/correct.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    return {
      userinfo: {},
      paperId: this.$route.query.paperId,
      isFinish: null,
      isReview: null,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      exams: [],
      paperInfo: {},
      multipleSelect: [],
      corrigendumDialog: false,
      addForm: {},
      isReviews: [
        {
          label: '全部',
          value: null
        },
        {
          label: '批阅中',
          value: 2
        },
        {
          label: '已批阅',
          value: 1
        },
        {
          label: '勘误中',
          value: 3
        }
      ],
      isFinishs: [
        {
          label: '全部',
          value: null
        },
        {
          label: '未提交',
          value: 0
        },
        {
          label: '已提交',
          value: 1
        }
      ]
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    goBack() {
      this.$router.push('/volume/correct')
    },
    getInTime(item) {
      var nowDate = new Date()
      var endTime = new Date(this.paperInfo.correctEndTime)
      if (nowDate < endTime) {
        return true
      } else {
        return false
      }
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        paperId: this.paperId,
        isFinish: this.isFinish,
        isReview: this.isReview
      }
      paperUserRecordList(data).then(async (res) => {
        this.exams = res.data.list
        this.total = res.data.total
      })
    },
    getPaperInfo() {
      paperRecordDetail({
        paperId: this.paperId
      }).then((res) => {
        this.paperInfo = res.data
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.getGroups()
        this.getPaperInfo()
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    exportUser() {
      var multipleSelect = this.multipleSelect
      var paperUserRecordIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          paperUserRecordIds.push(item.paperUserRecordId)
        })
      }
      paperCorrectExport({
        paperId: this.paperId,
        paperUserRecordIds: paperUserRecordIds && paperUserRecordIds.length > 0 ? paperUserRecordIds : null
      })
    },
    exportUserItem(item) {
      paperCorrectExport({
        paperId: this.paperId,
        paperUserRecordIds: [item.paperUserRecordId]
      })
    },
    openViewInfo(item, type) {
      this.$router.replace({
        name: 'correctinfo',
        query: {
          paperUserRecordId: item.paperUserRecordId,
          type: type
        }
      })
    },
    openCorrigendum(item) {
      this.addForm = Object.assign({}, item)
      this.corrigendumDialog = true
    },
    corrigendumReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.corrigendumDialog = false
    },
    corrigendumReTry() {
      anewComplain({
        paperUserRecordId: this.addForm.paperUserRecordId
      }).then((res) => {
        this.corrigendumReset()
        if (res.code == '200') {
          this.openViewInfo(this.addForm, 'edit')
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    corrigendumReturn() {
      rejectComplain({
        paperUserRecordId: this.addForm.paperUserRecordId
      }).then((res) => {
        this.corrigendumReset()
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: '已驳回！'
          })
          this.currentChange(1)
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  padding: 0 22px;
  padding-bottom: 20px;
  overflow: auto;
  .pageTop {
    position: relative;
    margin-top: 30px;
    margin-bottom: 22px;
    .goBack {
      position: absolute;
    }
    .title {
      font-size: 25px;
      text-align: center;
    }
  }
  .content {
    width: 100%;
    padding-top: 30px;
    border: 1px solid #c7cbd0;
    .examInfo {
      display: flex;
      padding-left: 20px;
      padding-bottom: 30px;
      border-bottom: 1px solid #c7cbd0;
      li {
        margin-right: 60px;
        &:last-of-type {
          margin-right: 0;
        }
        .info_text {
          font-size: 16px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #666666;
        }
      }
    }
    .tableBox {
      padding: 20px 20px;
      padding-bottom: 10px;
      .title {
        margin-bottom: 20px;
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        color: #333333;
      }
      .search {
        position: relative;
        .searchButton {
          position: absolute;
          right: 0;
          bottom: 0;
        }
      }
      ::v-deep {
        .el-table {
          margin-top: 13px;
          border: 1px solid #dfe6ec;
          border-bottom: none;
          .tableHeader {
            height: 40px;
            background: #f4f7ff;
            font-size: 16px;
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 400;
            color: #333333;
          }
          .tableCell {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #6e6f6d;
          }
          .isFinish0,
          .isFinish1 {
            display: inline-block;
            width: 48px;
            height: 24px;
            line-height: 24px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
          }
          .isFinish1 {
            background: rgba(14, 188, 168, 0.2);
            color: #0dbda8;
          }
          .isFinish0 {
            background: rgba(255, 63, 26, 0.2);
            color: #ff3f1a;
          }
        }
      }
    }

    // 通用样式
    .infoLabel {
      font-size: 16px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      color: #000000;
    }
  }
}
</style>
