<template>
  <div class="simulation_cont">
    <el-form :model="addform" :rules="rules" ref="addform" class="clearfix">
      <el-row>
        <el-col :span="24" class="select-difficulty-questionType">
          <el-form-item label="难度：" class="select-difficulty" :label-width="labelwidth" prop="difficulty">
            <el-radio-group v-model="addform.difficulty" @change="diffChange">
              <el-radio-button :label="item.value" v-for="item in difficultys" :key="item.value">{{ item.name }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="单选题数量：" class="num-form-item" :label-width="labelwidth" prop="singleNum">
            <el-input-number class="num-input" v-model="addform.singleNum" placeholder="" :min="0" :max="numbers.singleNum" :step="1" step-strictly></el-input-number>
            <!-- &nbsp;&nbsp;共<span class="maincolor">{{ numbers.singleNum }}</span
            >道题 -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="每题分数：" class="score-form-item" :label-width="labelwidth" prop="singleScore">
            <el-input-number class="score-input" v-model="addform.singleScore" placeholder="" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="多选题数量：" class="num-form-item" :label-width="labelwidth" prop="multipleNum">
            <el-input-number class="num-input" v-model="addform.multipleNum" placeholder="" :min="0" :max="numbers.multipleNum" :step="1" step-strictly></el-input-number>
            <!-- &nbsp;&nbsp;共<span class="maincolor">{{ numbers.multipleNum }}</span
            >道题 -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="每题分数：" class="score-form-item" :label-width="labelwidth" prop="multipleScore">
            <el-input-number class="score-input" v-model="addform.multipleScore" placeholder="" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="8">
          <el-form-item label="判断题数量：" :label-width="labelwidth" prop="judgeNum">
            <el-input-number v-model="addform.judgeNum" placeholder="请输入判断题数量" :min="0" :max="numbers.judgeNum" :step="1" step-strictly></el-input-number>
            &nbsp;&nbsp;共<span class="maincolor">{{ numbers.judgeNum }}</span
            >道题
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="每题分数：" :label-width="labelwidth" prop="judgeScore">
            <el-input-number v-model="addform.judgeScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="填空题数量：" :label-width="labelwidth" prop="completionNum">
            <el-input-number v-model="addform.completionNum" placeholder="请输入填空题数量" :min="0" :max="numbers.completionNum" :step="1" step-strictly></el-input-number>
            &nbsp;&nbsp;共<span class="maincolor">{{ numbers.completionNum }}</span
            >道题
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="每题分数：" :label-width="labelwidth" prop="completionScore">
            <el-input-number v-model="addform.completionScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="简答题数量：" :label-width="labelwidth" prop="shortNum">
            <el-input-number v-model="addform.shortNum" placeholder="请输入配伍题数量" :min="0" :max="numbers.shortNum" :step="1" step-strictly></el-input-number>
            &nbsp;&nbsp;共<span class="maincolor">{{ numbers.shortNum }}</span
            >道题
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="每题分数：" :label-width="labelwidth" prop="shortScore">
            <el-input-number v-model="addform.shortScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="配伍题数量：" :label-width="labelwidth" prop="compatibilityNum">
            <el-input-number v-model="addform.compatibilityNum" placeholder="请输入配伍题数量" :min="0" :max="numbers.compatibilityNum" :step="1" step-strictly></el-input-number>
            &nbsp;&nbsp;共<span class="maincolor">{{ numbers.compatibilityNum }}</span
            >道题
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="每小题分数：" :label-width="labelwidth" prop="compatibilityScore">
            <el-input-number v-model="addform.compatibilityScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="组合题数量：" :label-width="labelwidth" prop="comprehensiveNum">
            <el-input-number v-model="addform.comprehensiveNum" placeholder="请输入组合题数量" :min="0" :max="numbers.comprehensiveNum" :step="1" step-strictly></el-input-number>
            &nbsp;&nbsp;共<span class="maincolor">{{ numbers.comprehensiveNum }}</span
            >道题
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="每小题分数：" :label-width="labelwidth" prop="comprehensiveScore">
            <el-input-number v-model="addform.comprehensiveScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
          </el-form-item>
        </el-col>
      </el-row> -->
      <div class="simulation_all">
        总分：<span class="maincolor">{{ getAllScore }}</span
        >，总数量：<span class="maincolor">{{ getAllNumber }}</span>
        <el-button type="primary" @click="checkForm">智能抽题</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { selectQuestionCountByType } from '@/api/question.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  props: {
    majorId: {
      required: true
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    var checkScoreNum = (rule, value, callback) => {
      var theScoreName = rule.fullField.split('Score').join('Num')
      if (this.addform[theScoreName] == 0) {
        return callback()
      } else {
        if (!value) {
          return callback(new Error('请输入分数,题目数不为0时,分数不能为0'))
        } else {
          return callback()
        }
      }
    }
    return {
      code: 'paper_rules',
      userinfo: {},
      labelwidth: '130px',
      numbers: {},
      difficultys: [
        {
          name: '随机',
          value: 'STOCHASTIC'
        },
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes: [
        {
          name: '单选题',
          value: 'SINGLE'
        },
        {
          name: '多选题',
          value: 'MULTIPLE'
        },
        {
          name: '判断题',
          value: 'JUDGE'
        },
        {
          name: '填空题',
          value: 'COMPLETION'
        },
        {
          name: '简答题',
          value: 'SHORT'
        },
        {
          name: '配伍题',
          value: 'COMPATIBILITY'
        },
        {
          name: '组合题',
          value: 'COMPREHENSIVE'
        }
      ],
      addform: {
        difficulty: 'STOCHASTIC',
        singleNum: '',
        singleScore: '',
        multipleNum: '',
        multipleScore: '',
        judgeNum: '',
        judgeScore: '',
        completionNum: '',
        completionScore: '',
        shortNum: '',
        shortScore: '',
        compatibilityNum: '',
        compatibilityScore: '',
        comprehensiveNum: '',
        comprehensiveScore: ''
      },
      rules: {
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        singleNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        singleScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        multipleNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        multipleScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        judgeNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        judgeScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        completionNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        completionScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        shortNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        shortScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        compatibilityNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        compatibilityScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        comprehensiveNum: [
          {
            required: true,
            message: '请输入数字'
          }
        ],
        comprehensiveScore: [
          {
            validator: checkScoreNum
          },
          {
            required: true,
            message: '请输入数字'
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    getAllScore() {
      var allScore = this.getSum(this.addform.singleNum, this.addform.singleScore) + this.getSum(this.addform.multipleNum, this.addform.multipleScore) + this.getSum(this.addform.judgeNum, this.addform.judgeScore) + this.getSum(this.addform.completionNum, this.addform.completionScore) + this.getSum(this.addform.shortNum, this.addform.shortScore) + this.getSum(this.addform.compatibilityNum, this.addform.compatibilityScore) + this.getSum(this.addform.comprehensiveNum, this.addform.comprehensiveScore)
      return allScore
    },
    getAllNumber() {
      return this.getNum(this.addform.singleNum) + this.getNum(this.addform.multipleNum) + this.getNum(this.addform.judgeNum) + this.getNum(this.addform.completionNum) + this.getNum(this.addform.shortNum) + this.getNum(this.addform.compatibilityNum) + this.getNum(this.addform.comprehensiveNum)
    }
  },
  watch: {
    majorId() {
      if (this.majorId) {
        this.getTopicNum()
      }
    }
  },
  methods: {
    checkForm() {
      if (!this.getAllNumber) {
        this.$message({
          type: 'error',
          message: '题目数不能为空！'
        })
        return
      }
      this.$refs.addform.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addform, {
            majorId: this.majorId,
            schoolId: this.userinfo.schoolId,
            difficulty: this.addform.difficulty
          })
          this.$emit('beforeSelectCheck', data)
        }
      })
    },
    clearCheck() {
      this.$refs.addform.resetFields()
      this.$refs.addform.clearValidate()
    },
    getNum(num) {
      var getNum = num
      if (!num) {
        getNum = 0
      } else if (isNaN(Number(num))) {
        getNum = 0
      } else if (!Number.isInteger(Number(num))) {
        getNum = 0
      }
      return Number(getNum)
    },
    getSum(num, score) {
      var getNum = num
      var getScore = score
      if (!num) {
        getNum = 0
      } else if (isNaN(Number(num))) {
        getNum = 0
      } else if (!Number.isInteger(Number(num))) {
        getNum = 0
      }
      if (!score) {
        getScore = 0
      } else if (isNaN(Number(score))) {
        getScore = 0
      } else if (!Number.isInteger(Number(score))) {
        getScore = 0
      }
      return Number(getNum * score)
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.getTopicNum()
      })
    },
    diffChange() {
      this.getTopicNum()
    },
    getTopicNum() {
      this.addform.singleScore = this.socorRegular.SINGLE || 0
      this.addform.multipleScore = this.socorRegular.MULTIPLE || 0
      this.addform.judgeScore = this.socorRegular.JUDGE || 0
      this.addform.completionScore = this.socorRegular.COMPLETION || 0
      this.addform.shortScore = this.socorRegular.SHORT || 0
      this.addform.compatibilityScore = this.socorRegular.COMPATIBILITY || 0
      this.addform.comprehensiveScore = this.socorRegular.COMPREHENSIVE || 0
      if (!this.majorId) {
        this.$message({
          type: 'error',
          message: '请先选择专业！'
        })
        return
      }
      selectQuestionCountByType({
        majorId: this.majorId,
        schoolId: this.userinfo.schoolId,
        difficulty: this.addform.difficulty,
        questionUse: 0
      }).then((res) => {
        this.numbers = res.data
        this.addform.singleNum = this.addform.singleNum && this.addform.singleNum > this.numbers.singleNum ? this.numbers.singleNum : this.addform.singleNum
        this.addform.multipleNum = this.addform.multipleNum && this.addform.multipleNum > this.numbers.multipleNum ? this.numbers.multipleNum : this.addform.multipleNum
        this.addform.judgeNum = this.addform.judgeNum && this.addform.judgeNum > this.numbers.judgeNum ? this.numbers.judgeNum : this.addform.judgeNum
        this.addform.completionNum = this.addform.completionNum && this.addform.completionNum > this.numbers.completionNum ? this.numbers.completionNum : this.addform.completionNum
        this.addform.shortNum = this.addform.shortNum && this.addform.shortNum > this.numbers.shortNum ? this.numbers.shortNum : this.addform.shortNum
        this.addform.compatibilityNum = this.addform.compatibilityNum && this.addform.compatibilityNum > this.numbers.compatibilityNum ? this.numbers.compatibilityNum : this.addform.compatibilityNum
        this.addform.comprehensiveNum = this.addform.comprehensiveNum && this.addform.comprehensiveNum > this.numbers.comprehensiveNum ? this.numbers.comprehensiveNum : this.addform.comprehensiveNum
      })
    }
  }
}
</script>
