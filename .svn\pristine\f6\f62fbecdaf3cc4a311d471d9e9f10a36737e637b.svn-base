<template>
  <div>
    <el-dialog title="考核详情" :visible="detailsDialog" top="70px" width="960px" @close="close">
      <el-descriptions border :column="1">
        <el-descriptions-item label="考试名称">{{ examInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="考试时间">{{ examInfo.startTime }} ~ {{ examInfo.endTime }}</el-descriptions-item>
        <el-descriptions-item label="考试限时">{{ examInfo.time }}分钟</el-descriptions-item>
        <el-descriptions-item label="及格分">{{ examInfo.passScore }}</el-descriptions-item>
        <el-descriptions-item label="考核病例">
          <div class="caseBox">
            <div v-for="item in caseList" :key="item.caseId" class="caseItem">
              <div class="caseItem_top">
                <div class="caseItem_top_left">
                  <div class="score">
                    总分: <span>{{ item.allScore }}</span> 分
                  </div>
                  <casePhoto :height="'186px'" :width="'136px'" :sex="item.sex" :age="item.age" :type="'case'" />
                </div>
                <div class="caseItem_top_right">
                  <div>
                    <span>
                      {{ item.name }}
                      <svg-icon :icon-class="item.sex === 'M' ? 'nan' : 'nv'"></svg-icon>
                    </span>
                    <span> {{ item.age }} 岁</span>
                    <span>{{ item.form | caseForm }}</span>
                  </div>
                  <div>
                    <span> {{ item.mainDemands }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="班级学生">
          <div class="checkedStudents">
            <div v-for="item in studentList" :key="item.id">
              <div class="checkedClass">{{ item.className }}</div>
              <ul class="students">
                <li v-for="li in item.children" :key="li.id">
                  <el-tag type="success" closable @close="tagClose(li, item)">{{ li.name }}</el-tag>
                </li>
              </ul>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="primary" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import casePhoto from '@/components/casePhoto'

export default {
  name: '',
  props: {
    detailsDialog: {
      type: Boolean,
      require: true
    }
  },
  components: {
    casePhoto
  },
  data() {
    return {
      examInfo: {},
      caseList: [],
      studentList: []
    }
  },
  created() {},
  methods: {
    getInfo(data) {
      this.examInfo = { ...data }
      this.examInfo.cases.forEach((item) => {
        this.caseList.push({
          caseId: item.case_id,
          allScore: item.all_score,
          sex: item.sex,
          name: item.name,
          age: item.age,
          form: item.form,
          mainDemands: item.main_demands
        })
      })
      const parentIds = data.clbumIds.split(',')
      const studentList = []
      parentIds.forEach((item) => {
        studentList.push({
          classId: parseInt(item),
          className: null,
          children: []
        })
      })
      studentList.forEach((li) => {
        data.students.forEach((item) => {
          if (li.classId === item.clbum_id) {
            li.className = item.clbum_name
            li.children.push({
              id: item.student_id,
              name: item.name
            })
          }
        })
      })
      this.studentList = studentList
    },

    close() {
      this.examInfo = {}
      this.caseList = []
      this.studentList = []
      this.$emit('update:detailsDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    height: 700px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 3px;
    }

    // 里面的滑块
    &::-webkit-scrollbar-thumb {
      background: #d2d2d2;
    }

    // 外面的背景
    &::-webkit-scrollbar-track-piece {
      background: transparent;
    }
  }
}
.caseBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .caseItem {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 385px;
    margin-right: 15px;
    margin-bottom: 23px;
    height: 188px;
    line-height: initial;
    border-radius: 8px;
    border: 1px solid #eee;
    background: #fff;
    cursor: pointer;
    // overflow: hidden;
    box-sizing: border-box;
    &:nth-of-type(2n) {
      margin-right: 0;
    }
    .el-icon-success {
      position: absolute;
      right: -10px;
      top: -10px;
      font-size: 35px;
      color: #409eff;
    }
    .caseItem_top {
      position: relative;
      display: flex;
      justify-content: space-between;
      .caseItem_top_left {
        width: 136px;
        .score {
          position: absolute;
          padding: 4px 9px;
          height: 24px;
          background: rgba($color: #ff9f1e, $alpha: 0.6);
          text-align: center;
          border-radius: 8px 0 8px 0;
          font-size: 12px;
          font-family:
            Microsoft YaHei-Bold,
            Microsoft YaHei;
          color: #ffffff;
          span {
            font-weight: bold;
          }
        }
      }
      .caseItem_top_right {
        flex: 1;
        position: relative;
        padding-top: 12px;
        padding-right: 8px;
        padding-left: 13px;
        & > div:first-of-type {
          & > span {
            font-size: 16px;
            font-weight: bold;
            color: #1a1a1a;
            &:nth-of-type(2) {
              margin: 0 18px;
            }
          }
        }
        & > div:nth-of-type(2) {
          margin-top: 14px;
          padding: 8px 12px;
          min-height: 110px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 400;
          color: #737373;
          line-height: 24px;
          background: rgba($color: #f1f1f1, $alpha: 0.5);
          span {
            display: inline-block;
            width: 100%;
            // 两行显示，超出隐藏
            display: -webkit-box;
            overflow: hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: (3);
          }
        }
      }
    }
  }
}
.checkedStudents {
  .checkedClass {
    padding-left: 10px;
    margin-top: 15px;
    font-size: 18px;
    font-weight: bold;
  }
  .students {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    padding-left: 10px;
    margin: 0;
    margin-top: 10px;
    list-style: none;
    li {
      margin-right: 10px;
    }
  }
}
</style>
