<template>
  <div class="Chart_Casehistory" ref="ChartCasehistory"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
export default {
  name: 'ChartCasehistory',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  created() {},
  mounted() {
    // 基于准备好的dom，初始化echarts实例并应用配置
    this.chart = echarts.init(this.$refs['ChartCasehistory'])
  },
  methods: {
    init(data) {
      var option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            var data = params.data
            return `${data.name}<br/>数量：${data.value}人<br/>占比：${params.percent}%`
          },
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // 提示框背景颜色为黑色的50%透明度
          borderColor: 'transparent',
          borderRadius: 10,
          textStyle: {
            color: '#FFFFFF', // 设置 tooltip 文字颜色为白色
            fontSize: 16
          }
        },
        legend: {
          left: 'center',
          top: 40,
          textStyle: {
            color: '#666666',
            fontSize: 14
          }
        },
        series: [
          {
            name: '及格率',
            type: 'pie',
            radius: '121px',
            center: ['50%', '58%'],
            left: 'center',
            color: ['#0091FF', '#13CABC', '#FFC02C', '#FF8D72'],
            data: [
              { name: '重要问题', value: data.importantCount },
              { name: '常规问题', value: data.generalCount },
              { name: '无效问题', value: data.invalidCount },
              { name: '未采集', value: data.notCollectCount }
            ],
            label: {
              formatter: function (params) {
                // 这里使用ECharts的富文本进行配置
                return `${params.name}:${params.value}个(${params.percent}%)`
              },
              color: '#999999',
              fontSize: 14
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.Chart_Casehistory {
  width: 100%;
  height: 100%;
}
</style>
