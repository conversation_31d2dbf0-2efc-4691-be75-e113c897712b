import request from '@/utils/request'

//菜单树（根据学校id查询）
export function menuTree(data) {
  return request({
    url: '/system/menu/menuTree',
    method: 'get',
    params: data
  })
}
//添加菜单
export function saveMenu(data) {
  return request({
    url: '/system/menu/saveMenu',
    method: 'post',
    data
  })
}
//修改菜单信息
export function updateMenu(data) {
  return request({
    url: '/system/menu/updateMenu',
    method: 'post',
    data
  })
}
//删除菜单
export function removeMenu(data) {
  return request({
    url: '/system/menu/removeMenu',
    method: 'delete',
    params: data
  })
}

//题组列表
export function menuList(data) {
  return request({
    url: '/system/menu/menuList',
    method: 'get',
    params: data
  })
}
//日常练习，添加日常练习题组以及试题
export function saveQuestionExercise(data) {
  return request({
    url: '/study/questionExercise/saveQuestionExercise',
    method: 'post',
    data
  })
}
//修改日常练习以及试题
export function updateQuestionExercise(data) {
  return request({
    url: '/study/questionExercise/updateQuestionExercise',
    method: 'post',
    data
  })
}
//根据题组id查询日常练习试题
export function questionExerciseList(data) {
  return request({
    url: '/study/questionExercise/questionExerciseList',
    method: 'get',
    params: data
  })
}
