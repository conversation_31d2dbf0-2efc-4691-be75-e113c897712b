import request from '@/utils/request'
import download from '@/utils/downloader.js'

//试题分页列表
export function questionList(data) {
  return request({
    url: '/study/question/questionList',
    method: 'post',
    data
  })
}

//根据试题id查询试题详情
export function selectQuestionDetail(data) {
  return request({
    url: '/study/question/selectQuestionDetail',
    method: 'get',
    params: data
  })
}
//添加试题
export function saveQuestion(data) {
  return request({
    url: '/study/question/saveQuestion',
    method: 'post',
    data
  })
}
//修改试题信息
export function updateQuestion(data) {
  return request({
    url: '/study/question/updateQuestion',
    method: 'post',
    data
  })
}
//删除试题
export function removeQuestion(data) {
  return request({
    url: '/study/question/removeQuestion',
    method: 'delete',
    params: data
  })
}
//修改试题启用/禁用状态
export function updateState(data) {
  return request({
    url: '/study/question/updateState',
    method: 'post',
    params: data
  })
}
//批量导入普通试题excel
export function importQuestion(data) {
  return request({
    url: '/study/question/importQuestion',
    method: 'post',
    data
  })
}
//批量导入普通试题word
export function importWordQuestion(data) {
  return request({
    url: '/study/question/importWordQuestion',
    method: 'post',
    data
  })
}

//导出普通试题
export function questionExport(data) {
  return download({
    url: '/study/question/questionExport',
    method: 'post',
    data
  })
}

//查询试题数量
export function selectQuestionCountByType(data) {
  return request({
    url: '/study/question/selectQuestionCountByType',
    method: 'post',
    data
  })
}
