<template>
  <div>
    <el-form :model="form" :rules="rules" ref="theForm">
      <el-form-item label="选项:" :label-width="labelwidth" prop="question">
        <el-row v-for="(item, index) in form.questionArr">
          <el-col :span="14" class="small_input">
            <el-form-item :label="letters[index] + '、'" :label-width="labelwidth">
              <editor-more
                :content="item"
                @change="
                  (data) => {
                    questionChange(data, index)
                  }
                "
              ></editor-more>
            </el-form-item>
          </el-col>
          <el-col :span="10" class="small_inputbtn">
            <el-button type="danger" @click="questionDelete(index)" v-if="form.questionArr && form.questionArr.length > 3">删除</el-button>
            <el-button type="primary" @click="questionChange({ html: '' }, index)">清空</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14" class="small_input">
            <div style="height: 1px"></div>
          </el-col>
          <el-col :span="10" class="small_inputbtn">
            <el-button type="success" @click="questionAdd()">添加选项</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="问题:" :label-width="labelwidth" prop="list">
        <el-col :span="14" class="small_input">
          <el-tabs v-model="activeName" :closable="form.list && form.list.length > 1" @edit="handleTabsEdit">
            <el-tab-pane :label="'问题' + (index + 1)" :name="'' + (index + 1)" v-for="(item, index) in form.list"> </el-tab-pane>
          </el-tabs>
        </el-col>
        <el-col :span="10" class="small_inputbtn">
          <el-button type="success" @click="handleTabsEdit(form.list.length + 1, 'add')">添加</el-button>
        </el-col>
      </el-form-item>
      <div v-for="(item, index) in form.list" v-show="activeName == '' + (index + 1)">
        <el-form-item label="题型：" :label-width="labelwidth" prop="questionType">
          <el-radio-group v-model="form.list[index].questionType">
            <el-radio :label="item.value" v-for="item in questionTypes">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <tcompatiblityitem ref="tcompatiblityitem" :getScore="getScore" :socorRegular="socorRegular" :form="form.list[index]" :questionType="form.list[index].questionType" :letters="letters" :questionArr="form.questionArr"></tcompatiblityitem>
      </div>
    </el-form>
  </div>
</template>

<script>
import EditorMore from '@/components/Editor/index.vue'
import tcompatiblityitem from '@/views/question/input/components/tcompatiblityitem'
export default {
  components: {
    EditorMore,
    tcompatiblityitem
  },
  props: {
    form: {
      type: Object,
      required: false,
      default: null
    },
    letters: {
      type: Array,
      required: true
    },
    getScore: {
      type: Boolean,
      required: false,
      default: false
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    return {
      activeName: '1',
      labelwidth: '110px',
      questionTypes: [
        {
          name: '单选题',
          value: 'SINGLE'
        },
        {
          name: '多选题',
          value: 'MULTIPLE'
        }
      ],
      deleteIds: [],
      rules: {
        question: [
          {
            required: true,
            message: '请输入选项'
          }
        ],
        list: [
          {
            required: true,
            message: '请添加问题'
          }
        ]
      }
    }
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      console.log(this.form)
      var form = this.form
      if (form) {
        if (form.question) {
          try {
            var questionObject = JSON.parse(form.question)
            this.form.questionArr = Object.values(questionObject)
          } catch (err) {}
        }
        if (form.list.length <= 0) {
          this.form.list.push({
            questionType: 'SINGLE',
            question: '',
            answer: '',
            answers: [],
            analysis: '',
            scoreType: 1,
            score: this.socorRegular['COMPATIBILITY']
          })
          this.activeName = this.form.list.length + ''
        } else {
          this.form.list.map((item) => {
            item.scoreType = item.scoreType ? item.scoreType : 1
            item.score = item.score ? item.score : this.socorRegular['COMPATIBILITY']
          })
        }
      }
    },
    handleTabsEdit(targetName, action) {
      if (action == 'add') {
        this.form.list.push({
          questionType: 'SINGLE',
          question: '',
          answer: '',
          answers: [],
          analysis: '',
          scoreType: 1,
          score: this.socorRegular['SINGLE']
        })
        this.activeName = this.form.list.length + ''
      }
      if (action == 'remove') {
        var tabs = this.form.list
        var activeName = this.activeName * 1
        if (activeName >= this.form.list.length) {
          activeName--
        }
        var index = targetName * 1 - 1
        this.activeName = activeName + ''
        let list = this.form.list
        var topicItem = list[index]
        if (topicItem && topicItem.questionExerciseAnswerId) {
          this.deleteIds.push(topicItem.questionExerciseAnswerId)
        }
        if (topicItem && topicItem.paperQuestionAnswerId) {
          this.deleteIds.push(topicItem.paperQuestionAnswerId)
        }
        list.splice(index, 1)
        this.$set(this.form, 'list', list)
      }
    },
    questionAdd() {
      this.form.questionArr.push('')
      this.setQuestion()
    },
    questionDelete(index) {
      this.form.questionArr.splice(index, 1)
      this.form.answer = ''
      this.form.answers = []
      this.setQuestion()
    },
    questionChange(data, index) {
      this.$set(this.form.questionArr, index, data.html)
      this.setQuestion()
    },
    setQuestion() {
      var flag = this.form.questionArr.filter((item) => {
        return !item
      })
      if (flag.length > 0) {
        this.form.question = ''
      } else {
        var questionObj = {}
        this.form.questionArr.map((item, index) => {
          questionObj[this.letters[index]] = item
        })
        this.form.question = JSON.stringify(questionObj)
      }
      this.$nextTick(() => {
        this.$refs.theForm.clearValidate()
      })
    },
    contentChange(data, label) {
      this.form[label] = data.html
      this.$nextTick(() => {
        this.$refs.theForm.clearValidate()
      })
    },
    beforeSubmit() {
      return new Promise((resolve, reject) => {
        this.$refs.theForm.validate((valid) => {
          if (valid) {
            var promises = []
            for (let i = 0; i < this.form.list.length; i++) {
              var promisesitem = new Promise((resolveitem, rejectitem) => {
                this.$refs.tcompatiblityitem[i]
                  .beforeSubmItem()
                  .then((data) => {
                    var onetopic = Object.assign({}, this.form.list[i], data)
                    resolveitem(onetopic)
                  })
                  .catch(() => {
                    rejectitem()
                  })
              })
              promises.push(promisesitem)
            }
            Promise.all(promises)
              .then((list) => {
                var thetopic = {
                  question: this.form.question,
                  deleteIds: this.deleteIds,
                  list: list
                }
                resolve(thetopic)
              })
              .catch(() => {
                this.$message({
                  type: 'error',
                  message: '小题内容输入不完整！'
                })
                reject()
              })
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>
