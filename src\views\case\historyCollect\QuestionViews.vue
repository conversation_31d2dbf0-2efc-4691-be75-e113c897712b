<template>
  <div>
    <el-dialog title="问诊视图" :visible="showDialog" width="1600px" top="3vh" destroy-on-close @opened="initjsMind" @close="close">
      <div id="jsmind_container"></div>
    </el-dialog>
  </div>
</template>
<script>
import { caseQuestionTree } from '@/api/case'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      jm: null
    }
  },
  created() {},
  mounted() {
    // this.initjsMind()
  },
  methods: {
    async initjsMind() {
      const { data } = await caseQuestionTree({ caseId: this.$route.params.id })
      // jsMind 的配置项
      const options = {
        container: 'jsmind_container', // 容器的ID
        theme: 'primary', // 主题
        editable: false, // 是否允许编辑
        view: {
          node_overflow: 'wrap'
        }
        // 其他配置项...
      }

      // 思维导图的初始数据
      const mindData = {
        meta: {
          name: 'questionViews',
          author: 'cxh',
          version: '0.1'
        },
        format: 'node_tree',
        data: {
          id: 'root',
          topic: '问诊视图',
          children: data
        }
      }

      // 初始化 jsMind

      const jm = new jsMind(options)
      jm.show(mindData)

      // 如果需要，保存 jsMind 实例到组件的数据属性中，以便后续操作
      this.jm = jm
    },
    close() {
      this.$emit('update:showDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
#jsmind_container {
  width: 100%;
  height: 750px;
  border: 1px solid #ccc;
  /* 根据需要调整容器样式 */
  ::v-deep {
    .jsmind-inner {
      overflow-x: hidden;
    }
    .jmnode-overflow-hidden {
      overflow-x: hidden;
    }
  }
}
</style>
