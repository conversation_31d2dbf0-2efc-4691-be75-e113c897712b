<template>
  <div class="app-container">
    <el-card>
      <template slot="header">
        <el-row>
          <el-button style="margin-bottom: 15px" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
        </el-row>
        <el-row class="cardHeader" type="flex" justify="space-between" align="center">
          <div class="studentInfo" v-if="info">
            <div class="student">
              <el-avatar :size="40" :src="baseurl + info.icon" @error="errorHandler">
                <img src="@/assets/images/admin.jpg" />
              </el-avatar>
              <div>
                <div>
                  {{ info.studentName }} <span>{{ info.sex === 'F' ? '女' : '男' }}</span>
                </div>
                <div>
                  {{ info.loginName }}
                </div>
              </div>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ info.allScore }}</span>
              <span class="boxLabel">训练得分</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ parseInt(info.times / 60) }}分钟</span>
              <span class="boxLabel">训练用时</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue"
                >{{ info.startTime }} <br />~<br />
                {{ info.endTime }}</span
              >
              <span class="boxLabel">训练时间</span>
            </div>
          </div>
          <div class="caseInfo" v-if="caseInfo">
            <div class="case">
              <span>
                {{ caseInfo.name }}
              </span>
              <span>姓名：{{ caseInfo.realName }} 性别：{{ caseInfo.sex === 'F' ? '女' : '男' }} 年龄：{{ caseInfo.age }}</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ caseInfo.allScore }}</span>
              <span class="boxLabel">总分</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ caseInfo.questionCount ? caseInfo.questionCount : 0 }}</span>
              <span class="boxLabel">问题数量</span>
            </div>
          </div>
          <div class="accuracy" v-if="info">
            <div>病史采集正确率:</div>
            <div class="statisticsBox">
              <span class="boxValue">{{ parseInt(info.importantRate) }}%</span>
              <span class="boxLabel">重要问题</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ parseInt(info.generalRate) }}%</span>
              <span class="boxLabel">常规问题</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ parseInt(info.invalidRate) }}%</span>
              <span class="boxLabel">无效问题</span>
            </div>
            <div class="statisticsBox">
              <span class="boxValue">{{ notCollectCount }}%</span>
              <span class="boxLabel">未采集问题</span>
            </div>
          </div>
        </el-row>
      </template>
      <el-card class="questionCard">
        <div class="boxContent">
          <template v-if="info">
            <div class="left">
              <div class="title">未采集的问题({{ info.notCollectCount }})</div>
              <div class="questionItem" v-for="(item, index) in info.notCollect" :key="index">
                <div class="ask">【{{ item.typeName }}】{{ item.problem }} ({{ item.score }}分)</div>
                <div class="reply">回答：{{ item.answer }}</div>
              </div>
            </div>
            <div class="right">
              <el-row type="flex" justify="space-between">
                <div class="title" v-if="caseInfo">与[{{ caseInfo.name }}]的问诊对话</div>
                <div class="title_right">
                  <div class="level">
                    <span>
                      <img src="@/assets/grade/level1_icon.png" alt="" />
                      重要问题
                    </span>
                    <span>
                      <img src="@/assets/grade/level2_icon.png" alt="" />
                      常规问题
                    </span>
                    <span>
                      <img src="@/assets/grade/level3_icon.png" alt="" />
                      无效问题
                    </span>
                  </div>
                  <div :class="{ checked: sortType === 0 }" @click="sort(0, recordList, 'typeName')">
                    <span class="checkbox"></span>
                    <span class="text">问题重要性</span>
                    <span class="sortType"></span>
                  </div>
                  <div :class="{ checked: sortType === 1 }" style="margin-left: 15px" @click="sort(1, recordList, 'typeName')">
                    <span class="checkbox"></span>
                    <span class="text">问题类型</span>
                  </div>
                </div>
              </el-row>
              <el-row v-for="(item, index) in recordList" :key="index" type="flex" align="middle" class="list_item question_list_item">
                <div class="question_problem">
                  <img v-if="item.level === 1" src="@/assets/grade/level1_icon.png" alt="" />
                  <img v-else-if="item.level === 2" src="@/assets/grade/level2_icon.png" alt="" />
                  <img v-else src="@/assets/grade/level3_icon.png" alt="" />
                  <el-tooltip popper-class="problem_tooltip" effect="dark" :content="item.problem" placement="top">
                    <div class="list_item_content">
                      <template v-if="item.typeName"> 【{{ item.typeName }}】 </template>{{ item.userProblem }} <span v-if="item.problem" style="color: red">({{ item.problem }})</span>
                      <span class="score">（{{ item.score ? item.score : 0 }}分）</span>
                    </div>
                  </el-tooltip>
                </div>
                <div class="answer">回答: {{ item.answer }}</div>
              </el-row>
            </div>
          </template>
        </div>
      </el-card>
    </el-card>
  </div>
</template>
<script>
import { casePractiseStudentDetails } from '@/api/casePractise'
import { caseDetail } from '@/api/case'

export default {
  name: '',
  data() {
    return {
      info: null,
      caseInfo: null,
      recordList: [],
      sortType: 0 // 病史采集的排序类型
    }
  },
  created() {
    this.getDetalis()
    this.getCaseDetails()
  },
  computed: {
    notCollectCount() {
      const importantRate = parseInt(this.info.importantRate)
      const generalRate = parseInt(this.info.generalRate)
      const invalidRate = parseInt(this.info.invalidRate)
      return 100 - importantRate - generalRate - invalidRate
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    async getDetalis() {
      const { data } = await casePractiseStudentDetails({ practiseId: this.$route.params.id, type: 1 })
      this.info = data
      this.recordList = data.record ? JSON.parse(data.record) : []
    },
    async getCaseDetails() {
      const { data } = await caseDetail({ id: this.$route.params.caseId })
      this.caseInfo = data
    },
    // 根据排序类型排序
    sort(type, data, sortType) {
      this.sortType = type
      if (type) {
        data.sort((a, b) => {
          return b[sortType].localeCompare(a[sortType], 'zh-Hans-CN')
        })
      } else {
        data.sort((a, b) => {
          return a.level - b.level
        })
      }
    },
    errorHandler() {
      return true
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-card {
    .cardHeader {
      & > div {
        display: flex;
        align-items: center;
        border: 1px solid #4a73f1;
        border-radius: 8px;
        .statisticsBox {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin-left: 20px;
          padding: 10px 15px;
          border: 1px solid #0079fe;
          border-radius: 8px;
          font-weight: bold;
          .boxValue {
            font-size: 14px;
            text-align: center;
            color: #0079fe;
          }
          .boxLabel {
            margin-top: 5px;
            font-size: 15px;
            color: #494949;
          }
        }
      }
      .studentInfo {
        padding: 5px 20px;
        flex: 1;

        .student {
          display: flex;
          align-items: center;
          & > div {
            margin-left: 15px;
            & > div:first-of-type {
              margin-bottom: 10px;
              font-size: 18px;
              font-weight: bold;
              span {
                font-weight: 400;
                font-size: 16px;
              }
            }
          }
        }
      }
      .caseInfo {
        padding: 5px 20px;
        .case {
          display: flex;
          flex-direction: column;
          & > span:first-of-type {
            font-size: 18px;
            font-weight: bold;
          }
          & > span:last-of-type {
            margin-top: 10px;
            font-size: 14px;
          }
        }
      }
      .accuracy {
        flex: 1;

        padding: 5px 20px;
      }
      & > div:nth-of-type(2) {
        width: 520px;
        margin: 0 20px;
      }
    }
  }
  .questionCard {
    .el-card__body {
      padding: 0;
    }
  }
  .boxContent {
    display: flex;
    & > div {
      flex: 1;
    }
    .left {
      background: #f9f9fa;
      padding: 20px;

      border-right: 2px solid #414040;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 4px;
      }
      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #e8e7e7;
      }
      // 外面的背景
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
      .title {
        font-size: 19px;
        font-weight: bold;
        text-align: center;
      }
      .questionItem {
        margin-top: 30px;
        margin-bottom: 20px;
        .reply {
          margin-top: 10px;
          font-size: 14px;
          color: red;
        }
      }
    }
    .right {
      padding: 20px;

      background: #f2f3f4;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 4px;
      }
      // 里面的滑块
      &::-webkit-scrollbar-thumb {
        background: #e8e7e7;
      }
      // 外面的背景
      &::-webkit-scrollbar-track-piece {
        background: transparent;
      }
      .title {
        font-size: 19px;
        font-weight: bold;
        text-align: center;
      }
      .title_right {
        display: flex;
        padding-right: 34px;
        & > div {
          display: flex;
          align-items: center;
          cursor: pointer;

          &:first-of-type {
            margin-right: 24px;
          }

          .checkbox {
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 2px;
            border-radius: 1px 1px 1px 1px;
            border: 1px solid #a3a3a3;
          }
          .text {
            font-size: 14px;
            font-family:
              Microsoft YaHei-Regular,
              Microsoft YaHei;
            font-weight: 400;
            color: #1a1a1a;
          }
          .sortType {
            margin-left: 4px;
            width: 18px;
            height: 18px;
            // background: url('../../assets/grade/sort_bottom.png') no-repeat;
            background-size: cover;
          }
        }
        .level {
          display: flex;
          align-items: center;
          span {
            display: flex;
            align-items: center;
            margin-left: 24px;
            font-size: 12px;
            font-weight: 400;
            color: #a3a3a3;
            &:first-of-type {
              margin-left: 26px;
            }
            img {
              margin-right: 4px;
            }
          }
        }
        .checked {
          .text {
            font-weight: bold;
          }
          .checkbox {
            // background: url('../../assets/grade/Plan_type_checked.png') no-repeat;
            background-size: cover;
            border: none;
          }
          .sortType {
            // background: url('../../assets/grade/sort_top.png') no-repeat;
            background-size: cover;
          }
        }
      }
      .list_item {
        width: 100%;
        margin-bottom: 26px;
        padding-bottom: 10px;
        font-size: 14px;
        font-family:
          Microsoft YaHei-Regular,
          Microsoft YaHei;
        font-weight: 400;
        color: #1a1a1a;

        .list_item_content {
          position: relative;
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            left: 0;
            bottom: -10px;
            height: 1px;
            background: rgba(163, 163, 163, 0.38);
          }
          .score {
            font-size: 14px;
            font-weight: 400;
            color: #737373;
          }
          .details {
            font-size: 14px;
            font-weight: 400;
            color: #7c78c7;
          }
        }
      }
      .question_list_item {
        display: block;
        .question_problem {
          display: flex;
          align-items: center;
          margin-top: 30px;
          .list_item_content {
            &::after {
              bottom: -40px;
            }
          }
        }
        .answer {
          padding-left: 30px;
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
