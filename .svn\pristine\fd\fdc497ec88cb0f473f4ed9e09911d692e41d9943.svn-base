<template>
  <el-dialog :visible.sync="dialogVisible" top="8vh" custom-class="SaveQuestionDialog" :show-close="false" :close-on-click-modal="false">
    <el-table :data="list" style="width: 100%" header-cell-class-name="headerCellClass" cell-class-name="cellClass">
      <el-table-column align="center" label="序号" width="60" type="index"> </el-table-column>
      <el-table-column align="center" prop="type" label="问诊类型" width="120"> </el-table-column>
      <el-table-column align="center" prop="level" label="问题属性" width="120">
        <template v-slot="{ row }">
          <!-- <span>{{ row.level | caseLevel }}</span> -->
          <el-select v-model="row.level">
            <el-option label="重要" :value="1"> </el-option>
            <el-option label="常规" :value="2"> </el-option>
            <el-option label="无效" :value="3"> </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="problem" label="问题" show-overflow-tooltip width="width">
        <template v-slot="{ row }">
          <span>{{ row.problem.split('&')[0] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="answer" label="回答" show-overflow-tooltip width="width"> </el-table-column>
      <el-table-column align="center" prop="score" label="分数" width="200">
        <template v-slot="{ row }">
          <div class="scoreBox">
            <el-input-number v-model="row.score" :step="0.5" size="small" :min="0" :max="100" label="分数"></el-input-number>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveQuestion">保存编辑</el-button>
    </div>
  </el-dialog>
</template>
<script>
import _ from 'lodash'
import { caseQuestionTypeList } from '@/api/caseQuestionType'
import { addQuestionBatch } from '@/api/case'
export default {
  name: 'SaveQuestionDialog',
  data() {
    return {
      dialogVisible: false,
      typeList: [],
      list: []
    }
  },
  created() {
    this.getCaseTypeList()
  },
  methods: {
    async getCaseTypeList() {
      const { data } = await caseQuestionTypeList()
      this.typeList = data.list
    },
    setList(list) {
      this.dialogVisible = true
      this.list = _.cloneDeep(list)
      this.list.forEach((item) => {
        const typeId = this.typeList.find((type) => type.name === item.type).typeId
        this.$set(item, 'caseId', this.$route.params.id)
        this.$set(item, 'level', 2)
        this.$set(item, 'score', 1)
        this.$set(item, 'parentId', 0)
        this.$set(item, 'typeId', typeId)
      })
    },
    saveQuestion() {
      const loading = this.$loading({
        text: '数据保存中，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      addQuestionBatch(this.list)
        .then(() => {
          this.$message.success('保存成功!')
          this.$emit('success')
        })
        .finally(() => {
          loading.close()
        })
      console.log(this.list)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .SaveQuestionDialog {
    position: relative;
    width: 1432px;
    height: 773px;
    background: #ffffff;
    border-radius: 20px;
    .el-dialog__header {
      padding: 0;
    }
    .el-dialog__body {
      padding: 36px;
      padding-bottom: 0;
      height: calc(100% - 100px);
      overflow: auto;
    }
    .el-dialog__footer {
      position: absolute;
      bottom: 22px;
      right: 36px;
      padding: 0;
      & > div {
        & > .el-button {
          &:first-of-type,
          &:last-of-type {
            padding: 0;
            width: 93px;
            height: 50px;
            border-radius: 63px 63px 63px 63px;
            border: 1px solid #2a54ff;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            text-align: center;
            color: #2a54ff;
            line-height: 50px;
            cursor: pointer;
            &:hover {
              background: rgba($color: #fff, $alpha: 0.8);
            }
          }
          &:last-of-type {
            width: 132px;
            margin-left: 12px;
            background: #2a54ff;
            border: none;
            color: #ffffff;
            &:hover {
              background: rgba($color: #2a54ff, $alpha: 0.8);
            }
          }
        }
      }
    }
    .el-table::before {
      display: none;
    }
    .el-table__header-wrapper {
      margin-bottom: 4px;
      border-radius: 8px 8px 8px 8px;
      overflow: hidden;
    }

    .cellClass {
      height: 55px;
      border-bottom: solid 4px #fff;
      background: #f6f8fa;
      border-radius: 4px 4px 4px 4px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
    }
    .headerCellClass {
      height: 55px;
      border-bottom: none !important;
      background: #f6f8fa;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #999999;
    }
    .el-select {
      .el-input__inner {
        width: 100px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        border: none;
      }
    }
    .scoreBox {
      .el-input-number {
        width: 135px;
        .el-input-number__increase,
        .el-input-number__decrease {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 35px;
          background: #274e6a;
          border-radius: 10px 0px 0px 10px;
          .el-icon-minus,
          .el-icon-plus {
            font-size: 26px;
            font-weight: bold;
            color: rgba($color: #fff, $alpha: 0.7);
            transition: all 0.2s;
            &:hover {
              color: #fff;
            }
          }
        }
        .el-input-number__increase {
          border-radius: 0 10px 10px 0;
        }
        .is-disabled {
          .el-icon-minus,
          .el-icon-plus {
            color: rgba($color: #fff, $alpha: 0.3);
            &:hover {
              color: rgba($color: #fff, $alpha: 0.3);
            }
          }
        }
        .el-input {
          .el-input__inner {
            width: 135px;
            height: 35px;
            background: #e9e9e9;
            border-radius: 10px;
            border: none;
            font-family: PingFang SC;
            font-size: 26px;
            color: #274e6a;
            &::placeholder {
              color: #999999;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-select-dropdown__wrap {
  overflow-x: hidden;
  .el-select-dropdown__item {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #666666;
    &:hover {
      font-family: PingFang SC;
      color: #000000;
    }
  }
}
.el-tooltip__popper {
  font-size: 16px;
}
</style>
