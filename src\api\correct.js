import request from '@/utils/request'
import download from '@/utils/downloader.js'

//正式考试-试卷批改分页列表
export function paperCorrectList(data) {
  return request({
    url: '/study/paperCorrect/paperCorrectList',
    method: 'post',
    data
  })
}
//正式考试-试卷详情统计
export function paperRecordDetail(data) {
  return request({
    url: '/study/paperCorrect/paperRecordDetail',
    method: 'get',
    params: data
  })
}
//正式考试-考生成绩批改分页列表
export function paperUserRecordList(data) {
  return request({
    url: '/study/paperCorrect/paperUserRecordList',
    method: 'post',
    data
  })
}
//正式考试-考生成绩批改详情
export function paperUserRecordDetail(data) {
  return request({
    url: '/study/paperCorrect/paperUserRecordDetail',
    method: 'get',
    params: data
  })
}

//试卷与试题批改
export function paperUserRecordCorrect(data) {
  return request({
    url: '/study/paperCorrect/paperUserRecordCorrect',
    method: 'post',
    data
  })
}
//勘误驳回
export function rejectComplain(data) {
  return request({
    url: '/study/paperCorrect/rejectComplain',
    method: 'put',
    params: data
  })
}

//重新批阅
export function anewComplain(data) {
  return request({
    url: '/study/paperCorrect/anewComplain',
    method: 'put',
    params: data
  })
}
//导出考生成绩
export function paperCorrectExport(data) {
  return download({
    url: '/study/paperCorrect/paperCorrectExport',
    method: 'post',
    data
  })
}
