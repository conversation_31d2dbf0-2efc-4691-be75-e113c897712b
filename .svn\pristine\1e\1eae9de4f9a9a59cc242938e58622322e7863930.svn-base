import request from '@/utils/request'
//病例训练记录

/**病例训练记录列表 */
export function casePractiseList(params) {
  return request({
    url: '/casePractise/list',
    method: 'get',
    params
  })
}

/**训练记录学生列表 */
export function casePractiseStudentList(params) {
  return request({
    url: '/casePractise/practiseList',
    method: 'get',
    params
  })
}

/**训练记录学生详情 */
export function casePractiseStudentDetails(params) {
  return request({
    url: '/casePractise/practiseDetail',
    method: 'get',
    params
  })
}
