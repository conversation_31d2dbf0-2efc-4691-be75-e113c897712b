<template>
  <div>
    <ul class="question">
      <li v-for="(item, index) in list" :key="index" :class="{ notCollectLi: listType === '0' }">
        <!-- 问诊对话 -->
        <template v-if="listType === '1'">
          <div>
            <div class="trouble">
              <span class="icon">Q</span>
              <span class="text">
                <i class="type" v-if="item.level">【{{ item.typeName }}】</i>
                {{ item.userProblem }}
                <i v-if="item.level && item.level !== 3" class="score">({{ item.score }}分)</i>
              </span>
            </div>
            <div class="systemQuestion">
              <span class="icon">P</span>
              <span class="text">{{ item.problem ? item.problem : '暂无' }}</span>
            </div>
            <div class="systemQuestion">
              <span class="icon">A</span>
              <span class="text">{{ item.answer }}</span>
            </div>
          </div>
        </template>
        <!-- 未采集的问题 -->
        <template v-else>
          <div class="trouble">
            <span class="icon">Q</span>
            <span class="text">
              <i class="type">【{{ item.typeName }}】</i>
              {{ item.problem }}
              <i v-if="item.level && item.level !== 3" class="score">({{ item.score }}分)</i>
            </span>
          </div>
          <div class="systemQuestion">
            <span class="icon">A</span>
            <span class="text">{{ item.answer }}</span>
          </div>
        </template>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: '',
  props: {
    list: Array,
    listType: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>
<style scoped lang="scss">
ul {
  padding: 0 16px;
  margin: 0;
  li {
    position: relative;
    list-style: none;
    width: 880px;
    margin-bottom: 20px;
    padding: 30px;
    background: #f8f8f9;
    border-radius: 20px 20px 20px 20px;
    .levelIcon {
      position: absolute;
      left: 19px;
      top: 18px;
      width: 35px;
      height: 31px;
      img {
        width: 100%;
        height: 100%;
      }
      i {
        position: absolute;
        left: 46%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-style: normal;
        font-size: 18px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
      }
    }
    .trouble {
      display: flex;
      .icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin-right: 15px;
        background: #d3e3ee;
        font-size: 20px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #3082bd;
        border-radius: 50%;
        &::after {
          content: ':';
          position: absolute;
          right: -10px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 20px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #3082bd;
        }
      }
      .text {
        flex: 1;
        margin-top: 5px;
        font-size: 24px;
        line-height: 32px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        i {
          font-style: normal;
        }
        .type {
          color: #3082bd;
        }
        .score {
          margin-left: 4px;
          color: #999999;
        }
      }
    }
    .systemQuestion {
      display: flex;
      margin-top: 20px;
      .icon {
        position: relative;
        width: 40px;
        height: 40px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #d3e3ee;
        font-size: 20px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #3082bd;
        border-radius: 50%;
        &::after {
          content: ':';
          position: absolute;
          right: -8px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 20px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #3082bd;
        }
      }
      .text {
        flex: 1;
        margin-top: 5px;
        font-size: 24px;
        font-family: PingFang SC;
        font-weight: 500;
        line-height: 32px;
        color: #666;
      }
    }
  }
  // .notCollectLi {
  //   height: 93px;
  // }
}
</style>
