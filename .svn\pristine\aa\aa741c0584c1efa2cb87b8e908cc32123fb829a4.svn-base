<template>
  <div>
    <el-dialog custom-class="caseDetailsDialog" :visible="showDialog" width="1570px" @close="close" @open="getList">
      <div>
        <div class="caseData" v-if="caseInfo">
          <div class="caseItem">
            <div class="caseItem_header">病例{{ caseInfo.index + 1 }}</div>
            <div class="caseItem_body">
              <Avatar :age="caseInfo.age" :sex="caseInfo.sex" />
              <div class="caseName">
                <div>{{ caseInfo.name }}</div>
                <span>{{ caseInfo.real_name }}</span>
                <span>{{ caseInfo.age }}岁</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 搜索 -->
        <el-row class="searchRow" type="flex" align="middle" justify="space-between">
          <el-form label-width="90px" inline>
            <el-form-item label="姓名:" label-width="50px">
              <el-input class="studentName" v-model="queryInfo.studentName" size="small" placeholder="考生姓名"></el-input>
            </el-form-item>
            <el-form-item label="选择班级:">
              <el-select class="selectClbum" v-model="queryInfo.clbumId" placeholder="选择班级" @focus="getClbum" @change="getList">
                <el-option v-for="item in clbumList" :key="item.clbumId" :label="item.clbumName" :value="item.clbumId"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否及格:">
              <el-radio-group v-model="queryInfo.isPass" @change="getList">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="提交时间:">
              <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="primary" plain @click="reset">重置</el-button>
              <el-button type="primary" plain @click="exportExcel">导出Excel</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-table :data="list" style="width: 100%" header-cell-class-name="tableHeader" cell-class-name="tableCell" @sort-change="sortChange">
          <el-table-column align="center" label="序号" width="80" type="index"> </el-table-column>
          <el-table-column align="center" prop="name" label="学生信息" width="width">
            <template v-slot="{ row }">
              <span>{{ row.name }} ({{ row.sex === 'F' ? '女' : '男' }}) {{ row.loginName }} {{ row.clbumName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="score" label="总得分" width="width" sortable="custom"> </el-table-column>
          <el-table-column align="center" prop="time" label="总用时" width="width" sortable="custom">
            <template v-slot="{ row }">
              <span>{{ parseFloat(parseFloat(row.time / 60).toFixed(1)) }}分钟</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="state" label="状态" width="width">
            <template v-slot="{ row }">
              <el-tag :type="rowStateStyle(row)">{{ row.state === 1 ? '未参考' : row.state === 2 ? '考试中' : row.state === 3 ? '已结束' : '异常' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否及格" width="width">
            <template v-slot="{ row }">
              <span>{{ row.isPass ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="提交时间" width="width">
            <template v-slot="{ row }">
              <span>{{ row.endTime | formatDate('yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="time" label="操作" width="width">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="studentDetails(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination class="pagination" style="text-align: center; margin-top: 15px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next, jumper" :total="total" @size-change="getList" @current-change="getList"> </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseExamExamStudentList, caseExamClbumList, caseExamExportStudentList } from '@/api/caseExam'
import { formatDate } from '@/filters'
import Avatar from '@/views/casePractise/components/Avatar'

export default {
  name: '',
  components: {
    Avatar
  },
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },

  data() {
    return {
      caseInfo: null,
      caseId: null,
      queryInfo: {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        clbumId: null, // 班级id
        sort: null, // 1 总分数 2 总用时
        orderby: null, // 1 倒叙 2 正序
        pageNum: 1,
        pageSize: 6
      },
      clbumList: [],
      time: null,
      total: 0,
      list: []
    }
  },
  created() {},
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    async getList() {
      const { data } = await caseExamExamStudentList({ ...this.queryInfo, caseId: this.caseId })
      this.total = data.total
      this.list = data.list
    },
    reset() {
      this.queryInfo = {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 6
      }
      this.getList()
    },
    async exportExcel() {
      const { data } = await caseExamExportStudentList({ ...this.queryInfo, caseId: this.caseId })
      console.log(data)
      if (!data.length) return false
      // 生成包含动态病例键的模板
      const headers = this.generateDynamicTemplate(data[0])
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        // var tHeader = ['考试名称', '考试开始时间', '考试结束时间', '考试限时', '成绩公布时间', '考试人数', '考试病例', '总分', '及格分', '状态', '创建人', '创建时间']
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '考生成绩列表' // 非必填
        })
      })
    },
    generateDynamicTemplate(data) {
      // 基础模板
      let template = {
        姓名: 'name',
        性别: 'sex',
        学号: 'login_name',
        班级: 'clbum_name',
        提交时间: 'endTime',
        总得分: 'score',
        总用时: 'time'
      }
      // 检查数据中的动态“病例”键
      const dynamicCases = Object.keys(data).filter((key) => key.startsWith('病例'))

      // 将这些动态键添加到模板中
      dynamicCases.forEach((caseKey) => {
        template[caseKey] = caseKey // 例如，'病例1': '病例1'
      })

      // 在动态病例之后添加
      template['状态'] = 'stateName'
      template['是否及格'] = 'isPass'

      return template
    },
    // 处理导出数据格式
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (key === '性别') {
            item[headers[key]] = item[headers[key]] === 'M' ? '男' : '女'
          } else if (key === '是否及格') {
            item[headers[key]] = item[headers[key]] ? '是' : '否'
          } else if (key === '提交时间') {
            item[headers[key]] = formatDate(item[headers[key]], 'yyyy-MM-dd')
          }
          return item[headers[key]]
        })
      })
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    async getClbum() {
      const { data } = await caseExamClbumList({ examId: this.$route.params.id })
      this.clbumList = data
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? '' : row.state === 3 ? 'warning' : 'danger'
      return type
    },
    studentDetails(row) {
      this.$router.push(`/examGrade/student/${row.examStudentId}`)
    },
    sortChange(val) {
      if (val.prop === 'score') {
        this.queryInfo.sort = 1
      } else if (val.prop === 'time') {
        this.queryInfo.sort = 2
      }
      if (val.order) {
        this.queryInfo.orderby = val.order === 'ascending' ? 2 : 1
      } else {
        this.queryInfo.orderby = null
      }
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .caseDetailsDialog {
    height: 628px;
    background: #ffffff;
    .el-dialog__header {
      padding: 0;
    }
    .el-dialog__body {
      padding: 30px;
    }
  }
  .searchRow {
    margin-top: 20px;

    .el-form {
      .el-form-item {
        margin-bottom: 20px;
      }
    }
    .el-form-item__label {
      font-size: 16px;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      color: #121212;
    }
    .studentName,
    .selectClbum > .el-input {
      width: 204px;
      height: 40px;
      .el-input__inner {
        width: 100%;
        height: 100%;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #dcdfe6;
      }
    }
    .el-date-editor {
      width: 330px;
      height: 40px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #dcdfe6;
    }
    .el-range-editor--small .el-range__icon {
      line-height: 33px;
    }
    .el-date-editor .el-range-separator {
      line-height: 33px;
      width: 10%;
    }
    .el-button--medium {
      height: 44px;
      border-radius: 8px;
    }
  }
  .el-table {
    border: 1px solid #dfe6ec;
    border-bottom: none;
    margin-top: 17px;
    .tableHeader {
      height: 40px;
      background: #f4f7ff;
      font-size: 16px;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      color: #333333;
    }
    .tableCell {
      font-size: 16px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      color: #6e6f6d;
    }
  }
}
.caseData {
  .caseItem {
    display: inline-block;
    min-width: 261px;
    height: 129px;
    padding-right: 10px;
    margin-right: 16px;
    background: #f4f6f8;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e0e4e8;
    cursor: pointer;
    .caseItem_header {
      padding: 10px 0 10px 20px;
      border-bottom: 1px solid #e0e4e8;
      font-size: 18px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      color: #0091ff;
    }
    .caseItem_body {
      display: flex;
      align-items: center;
      padding-top: 18px;
      padding-left: 20px;
      .caseName {
        margin-left: 8px;
        & > div {
          margin-bottom: 5px;
          font-size: 20px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #000000;
        }
        span {
          font-size: 16px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #666666;
          &:last-of-type {
            margin-left: 3px;
          }
        }
      }
    }
  }
}
</style>
