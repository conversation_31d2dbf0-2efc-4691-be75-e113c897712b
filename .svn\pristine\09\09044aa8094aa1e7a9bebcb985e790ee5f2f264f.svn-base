import request from '@/utils/request'
// 病例库管理

/** 病例列表 */
export function caseList(data) {
  return request({
    url: '/case/list',
    method: 'post',
    data
  })
}
/** 添加病例 */
export function caseAdd(data) {
  return request({
    url: '/case/add',
    method: 'POST',
    data
  })
}
/** 修改病例 */
export function caseUpdate(data) {
  return request({
    url: '/case/update',
    method: 'POST',
    data
  })
}

/** 删除病例 */
export function caseRemove(params) {
  return request({
    url: '/case/remove',
    method: 'DELETE',
    params
  })
}
/** 病例详情 */
export function caseDetail(params) {
  return request({
    url: '/case/detail',
    method: 'get',
    params
  })
}
/** 查询所有病例信息 */
export function caseAllList(params) {
  return request({
    url: '/case/allList',
    method: 'get',
    params
  })
}

/** 修改问诊问题 */
export function caseUpdateQuestion(data) {
  return request({
    url: '/case/updateQuestion',
    method: 'POST',
    data
  })
}

/** 添加问诊问题 */
export function caseAddQuestion(data) {
  return request({
    url: '/case/addQuestion',
    method: 'POST',
    data
  })
}
/** 批量删除问诊问题 */
export function caseRemoveQuestionBatch(data) {
  return request({
    url: '/case/removeQuestionBatch',
    method: 'POST',
    data
  })
}
/** 问诊问题列表 */
export function caseQuestionList(params) {
  return request({
    url: '/case/questionList',
    method: 'get',
    params
  })
}

/** 复制病例 */
export function caseCopy(data) {
  return request({
    url: '/case/copy',
    method: 'POST',
    data
  })
}
/** 添加病例使用次数 */
export function caseAddCount(params) {
  return request({
    url: '/case/addCount',
    method: 'get',
    params
  })
}

// ------------病例权限
/** 添加 */
export function casePowerAdd(data) {
  return request({
    url: '/casePower/add',
    method: 'post',
    data
  })
}

/** 根据学生id查询病例信息 */
export function casePower_selectByStudentId(params) {
  return request({
    url: '/casePower/selectByStudentId',
    method: 'get',
    params
  })
}
