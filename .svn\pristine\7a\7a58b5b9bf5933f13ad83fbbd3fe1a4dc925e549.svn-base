<template>
  <div class="casePhoto">
    <template v-if="type === 'statistics'">
      <template v-if="sex === 'M'">
        <img v-if="age >= 40" src="@/assets/case/man_2.png" alt="" :style="{ width: width, height: height }" />
        <img v-else-if="age >= 15" src="@/assets/case/man_1.png" alt="" :style="{ width: width, height: height }" />
        <img v-else src="@/assets/case/man_0.png" alt="" :style="{ width: width, height: height }" />
      </template>
      <template v-if="sex === 'F'">
        <img v-if="age >= 40" src="@/assets/case/woman_2.png" alt="" :style="{ width: width, height: height }" />
        <img v-else-if="age >= 15" src="@/assets/case/woman_1.png" alt="" :style="{ width: width, height: height }" />
        <img v-else src="@/assets/case/woman_0.png" alt="" :style="{ width: width, height: height }" />
      </template>
    </template>
    <template v-if="type === 'case'">
      <template v-if="sex === 'M'">
        <img v-if="age >= 40" src="@/assets/case/case_man2.png" alt="" :style="{ width: width, height: height }" />
        <img v-else-if="age >= 15" src="@/assets/case/case_man1.png" alt="" :style="{ width: width, height: height }" />
        <img v-else src="@/assets/case/case_man0.png" alt="" :style="{ width: width, height: height }" />
      </template>
      <template v-if="sex === 'F'">
        <img v-if="age >= 40" src="@/assets/case/case_woman2.png" alt="" :style="{ width: width, height: height }" />
        <img v-else-if="age >= 15" src="@/assets/case/case_woman1.png" alt="" :style="{ width: width, height: height }" />
        <img v-else src="@/assets/case/case_woman0.png" alt="" :style="{ width: width, height: height }" />
      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: 'CasePhoto',
  props: ['height', 'width', 'sex', 'age', 'type'],
  data() {
    return {}
  }
}
</script>

<style scoped lang="scss"></style>
