<template>
  <div class="app-container">
    <el-card>
      <template v-slot:header>
        <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
        <div class="title">病例考核成绩详情</div>
      </template>
      <el-row v-if="examDetailInfo" type="flex" justify="space-between" class="header">
        <div class="header_left">
          <img src="@/assets/grade/header_left.png" alt="" />
          <div>
            <div class="examName">考试名称:{{ examDetailInfo.name }}</div>
            <div style="margin-top: 16px; margin-bottom: 16px">
              <span>考核班级:{{ examDetailInfo.clbumNames }} </span> <span>考试时间:{{ examDetailInfo.startTime }} ~ {{ examDetailInfo.endTime }} </span>
            </div>
            <div>
              <span>考试限时：{{ examDetailInfo.time }}分钟 </span> <span>总分：{{ examDetailInfo.allScore }}分 </span> <span>及格分：{{ examDetailInfo.passScore }}分</span>
            </div>
          </div>
        </div>
        <div class="header_right">
          <span
            ><img src="@/assets/grade/examInfo_icon1.png" alt="" /> 应考人数：<i>{{ examDetailInfo.allNumber }}</i></span
          >
          <span style="margin-right: 0"
            ><img src="@/assets/grade/examInfo_icon2.png" alt="" /> 实考人数：<i>{{ examDetailInfo.alreadyNumber }}</i></span
          >
          <span
            ><img src="@/assets/grade/examInfo_icon5.png" alt="" /> 及格人数：<i>{{ examDetailInfo.passNumber }}</i></span
          >
        </div>
      </el-row>
      <!-- 搜索 -->
      <el-row type="flex" align="center" justify="space-between">
        <el-form label-width="80px" inline>
          <el-form-item label="考生姓名:">
            <el-input v-model="queryInfo.studentName" size="small" placeholder="请输入考生姓名"></el-input>
          </el-form-item>
          <el-form-item label="是否及格:">
            <el-radio-group v-model="queryInfo.isPass" @change="getStudentList">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提交时间:">
            <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
          </el-form-item>
          <el-form-item label="状态:">
            <el-radio-group v-model="queryInfo.state" @change="getStudentList">
              <el-radio :label="1">未参考</el-radio>
              <el-radio :label="2">已参考</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="success" size="small" @click="getStudentList">查询</el-button>
          <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        </div>
      </el-row>
      <el-table :data="studentList" style="width: 100%" border header-cell-class-name="tableHeader" cell-class-name="tableCell">
        <el-table-column align="center" label="序号" width="80" type="index"> </el-table-column>
        <el-table-column align="center" prop="name" label="姓名" width="width"> </el-table-column>
        <el-table-column align="center" label="性别" width="width">
          <template v-slot="{ row }">
            <span>{{ row.sex === 'F' ? '女' : '男' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="loginName" label="学号" width="width"> </el-table-column>
        <el-table-column align="center" prop="clbumName" label="所在班级" width="width"> </el-table-column>
        <el-table-column align="center" prop="state" label="状态" width="width">
          <template v-slot="{ row }">
            <el-tag :type="rowStateStyle(row)">{{ row.state === 1 ? '未参考' : row.state === 2 ? '考试中' : row.state === 3 ? '已结束' : '异常' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="是否及格" width="width">
          <template v-slot="{ row }">
            <span>{{ row.isPass ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="score" label="得分" width="width"> </el-table-column>
        <el-table-column align="center" prop="time" label="用时" width="width">
          <template v-slot="{ row }">
            <span>{{ parseFloat(parseFloat(row.time / 60).toFixed(1)) }}分钟</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="endTime" label="提交时间" width="170"> </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-pagination class="pagination" style="text-align: center; margin-top: 15px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next, jumper" :total="total" @size-change="getStudentList" @current-change="getStudentList"> </el-pagination>
    </el-card>
  </div>
</template>
<script>
import { caseExamDetail, caseExamExamStudentList } from '@/api/caseExam'
import { formatDate } from '@/filters'
export default {
  name: '',
  data() {
    return {
      examDetailInfo: null,
      queryInfo: {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 6
      },
      time: null,
      studentList: [],
      total: 0
    }
  },
  created() {
    this.getExamDetails()
    this.getStudentList()
  },
  methods: {
    goBack() {
      this.$router.push('/examGrade')
    },
    async getExamDetails() {
      const { data } = await caseExamDetail({ id: this.$route.params.id })
      this.examDetailInfo = { ...data }
    },
    async getStudentList() {
      const { data } = await caseExamExamStudentList(this.queryInfo)
      this.studentList = data.list
      this.total = data.total
      console.log(data)
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? '' : row.state === 3 ? 'warning' : 'danger'
      return type
    },
    reset() {
      this.queryInfo = {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: null,
        pageSize: null
      }
      this.getStudentList()
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getStudentList()
    }
  }
}
</script>
<style scoped lang="scss">
.goBack {
  position: absolute;
}
.title {
  font-size: 25px;
  text-align: center;
}
.header {
  width: 100%;
  height: 134px;
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 25px;
  background: linear-gradient(238deg, #f9fcff 0%, #bcddff 100%);
  border-radius: 4px 4px 0px 0px;
  .header_left {
    display: flex;
    align-items: center;
    & > div {
      display: flex;
      flex-direction: column;
      .examName {
        font-size: 18px;
        font-family:
          Microsoft YaHei-Bold,
          Microsoft YaHei;
        font-weight: bold;
        color: #1a1a1a;
      }
      & > div:nth-of-type(2) {
        span {
          font-size: 14px;
          font-family:
            Microsoft YaHei-Regular,
            Microsoft YaHei;
          font-weight: 400;
          color: #1a1a1a;
        }
      }
      & > div:last-of-type {
        font-size: 14px;
        font-family:
          Microsoft YaHei-Regular,
          Microsoft YaHei;
        font-weight: 400;
        color: #314d61;
      }
      span {
        margin-right: 20px;
      }
    }
  }
  .header_right {
    display: flex;
    flex-wrap: wrap;
    width: 425px;
    span {
      display: flex;
      align-items: center;
      margin-right: 20px;
      font-size: 16px;
      font-family:
        Microsoft YaHei-Regular,
        Microsoft YaHei;
      font-weight: 400;
      color: #1a1a1a;
      img {
        margin-top: 8px;
      }
      i {
        font-style: normal;
        font-size: 16px;
        font-family:
          Microsoft YaHei-Bold,
          Microsoft YaHei;
        font-weight: bold;
        color: #1a1a1a;
      }
    }
  }
}
.details {
  ::v-deep {
    .el-card__body {
      display: flex;
      .el-descriptions {
        flex: 1;
      }
    }
  }

  .score {
    display: flex;
    align-items: center;
    width: 265px;
    & > div {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 30px;
      & > span:last-of-type {
        margin-top: 8px;
        font-size: 20px;
        color: #269dfa;
      }
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
}
::v-deep {
  .el-tag {
    font-size: 14px;
  }
  .el-form {
    .el-form-item__label {
      // font-size: 14px;
      font-family:
        Microsoft YaHei-Regular,
        Microsoft YaHei;
      font-weight: 400;
      color: #737373;
    }
    .el-form-item__content {
      .el-input {
        width: 236px;
        height: 32px;
        .el-input__inner {
          height: 32px;
          background: #ffffff;
          opacity: 1;
          border: 1px solid #dcdcdc;
          &::placeholder {
            // font-size: 14px;
            font-weight: 400;
            color: #d2d2d2;
          }
        }
      }
    }
    .el-button--info {
      background: #fff;
      color: #a3a3a3;
      border-color: #a3a3a3;
      &:hover {
        background: #fff;
        color: #a3a3a3;
        border-color: #a3a3a3;
      }
    }
  }
  .el-table {
    border: 1px solid #eae9e9;
    &::before {
      /* 去除下边框 */
      display: none;
    }
    .el-table__row > td {
      /* 去除表格线 */
      border: none !important;
    }
    th.is-leaf {
      /* 去除上边框 */
      border: none;
    }
  }
  // 鼠标经过样式

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #f5f5f5;
  }
  .tableHeader {
    height: 40px;
    background: #ecf2f9 !important;
    // font-size: 14px;
    font-weight: bold;
    color: #1a1a1a;
  }
  .tableCell {
    // font-size: 14px;
    color: #1a1a1a;
  }
  .button {
    width: 72px;
    height: 28px;
    padding: 0;
    border: none;
    background: rgba(64, 158, 255, 0.12);
    // font-size: 14px;
    font-weight: 400;
    color: #409eff;
    text-align: center;
  }
}
::v-deep {
  .row2 {
    display: none !important;
    &::after {
      display: none;
    }
  }
}
</style>
