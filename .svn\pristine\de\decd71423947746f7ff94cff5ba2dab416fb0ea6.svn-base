import request from '@/utils/request'

//班级树
export function clbumTree(data) {
  return request({
    url: '/system/clbum/clbumTree',
    method: 'get',
    params: data
  })
}
//添加班级
export function saveClbum(data) {
  return request({
    url: '/system/clbum/saveClbum',
    method: 'post',
    data
  })
}
//修改班级信息
export function updateClbum(data) {
  return request({
    url: '/system/clbum/updateClbum',
    method: 'post',
    data
  })
}
// 删除班级
export function removeClbum(data) {
  return request({
    url: '/system/clbum/removeClbum',
    method: 'delete',
    params: data
  })
}

//查询所有班级所有学生
export function systemClbumAll(data) {
  return request({
    url: '/system/clbum/all',
    method: 'post',
    data
  })
}
