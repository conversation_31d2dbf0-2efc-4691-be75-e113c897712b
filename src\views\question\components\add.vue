<template>
  <div class="topicinfo">
    <el-dialog :title="questionId ? '编辑试题' : '新增试题'" custom-class="addTopicDialog" :visible.sync="dialogVisible" width="width" top="50px" @open="getUserInfo" @close="close">
      <el-form :model="form" :rules="rules" ref="addForm">
        <el-row>
          <el-col :span="24" v-if="!questionId" class="tabs">
            <el-form-item :label-width="labelwidth">
              <el-button v-for="item in addtypes" :key="item.value" @click="changeType(item)" :type="addtype == item.value ? 'primary' : ''">
                {{ item.name }}
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="select-major" label="所属专业：" :label-width="labelwidth" prop="majorId">
              <el-select v-model="form.majorId" placeholder="请选择所属专业">
                <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24" class="select-difficulty-questionType" v-if="addtype == '1'">
            <el-form-item class="select-difficulty" label="难度：" :label-width="labelwidth" prop="difficulty">
              <el-radio-group v-model="form.difficulty">
                <el-radio-button v-for="item in difficultys" :key="item.value" :label="item.value">{{ item.name }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="select-questionType" label="题型：" :label-width="labelwidth" prop="questionType">
              <el-radio-group v-model="form.questionType" @change="topicChange">
                <el-radio-button v-for="item in questionTypes" :key="item.value" :label="item.value">{{ item.name }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="addtype == '1'">
            <questioninput ref="questioninput" class="questioninput" :letters="letters" :form="form" :questionType="questionType"></questioninput>
            <div class="save-operate">
              <el-button @click="close" type="info" v-if="form.questionType">取消</el-button>
              <el-button @click="beforeCheck" type="primary" v-if="form.questionType">保存</el-button>
            </div>
          </el-col>
          <el-col :span="24" v-if="addtype == '2'">
            <paste class="pasteComponent" @beforePasteCheck="beforePasteCheck" @close="close"></paste>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { selectQuestionDetail, saveQuestion, updateQuestion, removeQuestion, updateState, importQuestion, questionExport } from '@/api/question.js'
import { selectTeacherById } from '@/api/teacher.js'
import questioninput from '@/views/question/input/index'
import paste from '@/views/question/input/paste'
import { questionTypes } from '@/filters'
export default {
  components: {
    questioninput,
    paste
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    return {
      dialogVisible: false,
      questionId: '',
      labelwidth: '110px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: '',

      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      form: {
        majorId: '',
        questionUse: 0,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        scoreType: 1,
        score: 0
      },
      rules: {
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  methods: {
    close() {
      this.clearCheck()
      this.addtype = '1'
      this.form.majorId = ''
      this.questionId = ''
      this.dialogVisible = false
      this.$emit('refreshList')
    },

    beforePasteCheck(item) {
      var data = Object.assign({}, item, {
        majorId: this.form.majorId,
        questionUse: this.form.questionUse
      })
      saveQuestion(data).then((res) => {
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: res.message
          })
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    beforeCheck() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$refs.questioninput
            .beforeCheck()
            .then((resdata) => {
              var data = Object.assign({}, this.form, resdata)
              if (this.questionId) {
                updateQuestion(data).then((res) => {
                  if (res.code == '200') {
                    this.$message({
                      type: 'success',
                      message: res.message
                    })
                    this.close()
                  } else {
                    this.$message({
                      type: 'error',
                      message: res.message
                    })
                  }
                })
              } else {
                saveQuestion(data).then((res) => {
                  if (res.code == '200') {
                    this.$message({
                      type: 'success',
                      message: res.message
                    })
                    this.clearCheck()
                  } else {
                    this.$message({
                      type: 'error',
                      message: res.message
                    })
                  }
                })
              }
            })
            .catch((err) => {
              console.log(err)
            })
        }
      })
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.form.majorId || '',
          questionUse: 0,
          difficulty: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: ''
        }
      )
      this.questionType = ''
      this.$refs.addForm.resetFields()
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    topicChange() {
      this.questionType = ''
      var form = {
        majorId: this.form.majorId,
        questionUse: this.form.questionUse,
        difficulty: this.form.difficulty,
        questionType: this.form.questionType,
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: ''
      }
      this.$set(this, 'form', form)
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    changeType(item) {
      if (this.addtype != item.value) {
        this.addtype = item.value
        if (item.value == '1') {
          this.clearCheck()
        }
      }
    },
    getInfo() {
      selectQuestionDetail({
        questionId: this.questionId
      }).then((res) => {
        var questionArr = ['', '', '', '']
        if (res.data.questionType == 'COMPATIBILITY') {
          try {
            var questionObject = JSON.parse(form.question)
            questionArr = Object.values(questionObject)
          } catch (err) {}
        }
        this.form = Object.assign({}, this.form, res.data, {
          questionArr
        })
        this.$nextTick(() => {
          this.questionType = this.form.questionType
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (this.questionId) {
          this.getInfo()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.topicinfo {
  ::v-deep {
    .addTopicDialog {
      width: 1100px;
      height: 840px;
      padding-bottom: 30px;
      border-radius: 30px 30px 30px 30px;

      .el-dialog__header {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        padding-top: 35px;
        &__title {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 26px;
          color: #333333;
        }
        .el-dialog__headerbtn {
          top: 40px;
          right: 40px;
          .el-icon-close {
            font-size: 24px;
            color: #666;
            font-weight: 600;
          }
        }
      }
      .el-dialog__body {
        padding: 0;
        padding-top: 32px;
        max-height: 770px;
        overflow: auto;
        &::-webkit-scrollbar {
          background: transparent;
        }
        .tabs {
          border-bottom: 2px solid #d0d6df;
          .el-form-item {
            position: relative;
            top: 1px;
            padding-left: 50px;
            margin-bottom: 0;
            &__content {
              display: flex;
              align-items: center;
              .el-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 112px;
                height: 48px;
                padding: 0;
                background: #ffffff;
                border: 1px solid #d0d6df;
                &:hover {
                  color: #555555;
                }
                &--primary {
                  color: #fff;
                  background: #274e6a;
                  border: 1px solid #274e6a;
                }
              }
            }
          }
        }
        .el-form-item {
          display: flex;
          align-items: center;
          padding-left: 60px;
          &__content {
            flex: 1;
            margin-left: 0 !important;
          }
          &__label {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #555555;
          }
        }

        .select-major {
          margin-top: 25px;
          .el-select {
            width: 507px;
            height: 50px;

            .el-input {
              width: 100%;
              height: 100%;
              .el-input__inner {
                width: 100%;
                height: 100%;
                background: #e9e9e9;
                border-radius: 10px 10px 10px 10px;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                &:focus {
                  border-color: #e9e9e9;
                }
                &::placeholder {
                  color: #999;
                }
              }
            }
          }
        }
        .select-difficulty-questionType {
          display: flex;
          align-items: center;
          .select-difficulty,
          .select-questionType {
            .el-radio-group {
              .el-radio-button {
                width: 68px;
                height: 50px;
                border-radius: 0px 0px 0px 0px;
                // border: 1px solid #dadada;
                &:first-of-type {
                  .el-radio-button__inner {
                    border-radius: 10px 0px 0px 10px;
                  }
                }
                &:last-of-type {
                  .el-radio-button__inner {
                    border-radius: 0px 10px 10px 0px;
                  }
                }
                &__inner {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-family: PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #000;
                }
                &.is-active {
                  .el-radio-button__inner {
                    background: #274e6a;
                    color: #fff;
                    border-color: #274e6a;
                    box-shadow: -1px 0 0 0 #274e6a;
                  }
                }
              }
            }
          }
          .select-questionType {
            padding-left: 50px;
          }
        }

        .questioninput {
          .el-form-item {
            align-items: flex-start;
            .small_inputbtn {
              display: flex;
              align-items: center;
              padding-top: 20px;
              padding-left: 10px;
            }
            .clear__button,
            .remove__button {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 59px;
              height: 32px;
              padding: auto;
              background: #274e6a;
              border: none;
              border-radius: 4px 4px 4px 4px;

              font-family: PingFang SC;
              font-weight: 400;
              font-size: 14px;
              color: #ffffff;
            }
            .remove__button {
              background: #d94444;
            }
            .small_input {
              .el-form-item {
                padding-left: 0;
                margin-left: -30px;
                &__label {
                  font-family: PingFang SC;
                  font-weight: 500;
                  font-size: 16px;
                  color: #000000;
                }
              }
            }
            .add_input {
              .el-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 120px;
                height: 40px;
                padding: 0;
                background: #ffffff;
                border-radius: 60px 60px 60px 60px;
                border: 1px solid #274e6a;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #274e6a;
              }
            }
            // 单选答案样式
            .answer-group {
              display: flex;
              align-items: center;
              margin-top: 9px;
              .answer-button {
                display: flex;
                align-items: center;
                .el-radio__input {
                  .el-radio__inner {
                    width: 18px;
                    height: 18px;
                    &:hover {
                      border-color: #274e6a;
                    }
                  }
                  &.is-checked {
                    .el-radio__inner {
                      background: #274e6a;
                      border-color: #274e6a;
                    }
                    &::after {
                      background: #fff;
                      width: 12px;
                      height: 12px;
                    }
                  }
                }
                .el-radio__label {
                  font-family: PingFang SC;
                  font-weight: 400;
                  font-size: 16px;
                  color: #000000;
                }
              }
            }
            // 多选答案样式
            .anser-checkbox-group {
              .anser-checkbox {
                .el-checkbox__input {
                  .el-checkbox__inner {
                    width: 18px;
                    height: 18px;
                    &:hover {
                      border-color: #274e6a;
                    }
                  }
                  &.is-checked {
                    .el-checkbox__inner {
                      background: #274e6a;
                      border-color: #274e6a;
                      &::after {
                        top: 3px;
                        left: 6px;
                      }
                    }
                  }
                }
                .el-checkbox__label {
                  font-family: PingFang SC;
                  font-weight: 400;
                  font-size: 16px;
                  color: #000000;
                }
              }
            }
          }
          .ql-toolbar {
            background: #f0f0f0;
            border-color: #dddee3;
            border-radius: 4px 4px 0 0;
          }
          .ql-container {
            border-radius: 0px 0px 4px 4px;
            border-color: #dddee3;
          }
        }
        // 粘贴组件相关样式
        .pasteComponent {
          .paste-form-item {
            align-items: flex-start;
            .paste-input {
              .el-textarea__inner {
                width: 883px;
                height: 249px;
                background: #fafafa;
                border-radius: 8px 8px 8px 8px;
                border: 1px solid #d7d7dc;
                color: #333333;
                &::placeholder {
                  color: #888888;
                }
              }
            }
          }
          .create-col {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 50px;
            .create-button {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 102px;
              height: 48px;
              padding: auto;
              background: #274e6a;
              border: none;
              border-radius: 8px;

              font-family: PingFang SC;
              font-weight: 400;
              font-size: 18px;
              color: #ffffff;
            }
          }
        }

        .save-operate {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-right: 54px;
          .el-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 93px;
            height: 50px;
            background: #274e6a;
            border-color: #274e6a;
            border-radius: 63px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #ffffff;
            &--info {
              color: #274e6a;
              background: #fff;
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.el-select-dropdown {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>
