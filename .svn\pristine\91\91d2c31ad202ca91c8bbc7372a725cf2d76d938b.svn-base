<template>
  <div class="home-container">
    <div class="home_top">
      <div class="title">SP开放性人机对话系统</div>
      <!-- <img src="@/assets/images/home_top.png" alt="" /> -->
    </div>
    <div class="bigBox">
      <section>
        <div v-if="isExist('system:system')" class="box" @click="jump(1)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>系统管理</span>
        </div>
        <div v-if="isExist('case:case')" class="box" @click="jump(2)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例库</span>
        </div>
        <div v-if="isExist('caseRectify:caseRectify')" class="box" @click="jump(3)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>问答纠错库</span>
        </div>
        <div v-if="isExist('caseExam:caseExam')" class="box" @click="jump(4)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例考核</span>
        </div>
        <div v-if="isExist('volume:volume')" class="box" @click="jump(5)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>理论考核</span>
        </div>
        <div v-if="isExist('question:questionlist')" class="box" @click="jump(6)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>题库</span>
        </div>
        <div v-if="isExist('casePractise:casePractise')" class="box" @click="jump(7)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例训练记录</span>
        </div>
        <div v-if="isExist('examGrade:examGrade')" class="box" @click="jump(8)">
          <img src="@/assets/home/<USER>" alt="" />
          <span>病例考核成绩</span>
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {}
  },
  created() {},
  computed: {
    ...mapGetters(['permission_routes', 'roles'])
  },
  methods: {
    isExist(role) {
      return this.roles.includes(role)
    },
    jump(type) {
      // type: 1系统管理
      window.localStorage.setItem('sp_admin_modelType', type)
      this.$router.push(this.getFirstRoute(type))
    },
    getFirstRoute(type) {
      const route = this.permission_routes.filter((item) => {
        if (item.meta && item.meta.moduleType === type) {
          return item
        }
      })
      return route[0].path
    }
  }
}
</script>
<style scoped lang="scss">
.home-container {
  width: 100%;
  height: 100%;
  background: url('~@/assets/home/<USER>') no-repeat;
  overflow: hidden;
  .home_top {
    position: relative;
    padding-top: 80px;
    padding-bottom: 60px;
    width: 100%;
    .title {
      display: flex;
      justify-content: center;
      font-size: 50px;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 500;
      color: #000000;
    }
  }
  .bigBox {
    position: relative;
    z-index: 1;
    margin: 0 auto;
    padding: 0 52px;
    section {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 318px;
        height: 280px;
        margin-right: 60px;
        margin-bottom: 60px;
        background: #fff;
        border-radius: 14px;
        cursor: pointer;
        &:hover {
          background: #3a81f0;
          & > span {
            color: #fff;
          }
        }
        &:nth-of-type(4n) {
          margin-right: 0;
        }
        & > span {
          margin-top: 26px;
          font-size: 24px;
          font-weight: 500;
          font-family:
            Microsoft YaHei,
            Microsoft YaHei;
          font-weight: 400;
          color: #000000;
        }
      }
    }
  }
}
</style>
