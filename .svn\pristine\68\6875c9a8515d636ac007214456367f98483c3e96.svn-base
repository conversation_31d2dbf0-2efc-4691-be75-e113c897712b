<template>
  <div class="app-container">
    <div class="goBackButton" @click="goBack">
      <img src="@/assets/case/goBackIcon.png" alt="" />
    </div>
    <!-- 表单填写区域 -->
    <div class="formBox">
      <div class="header">
        <img src="@/assets/case/aiIcon.png" alt="" />
        AI智能生成病例
      </div>
      <div class="alert">*请填写至少一个病例生成条件</div>
      <el-form ref="form" :model="formInfo" label-width="90px" label-position="top">
        <el-form-item label="疾病名称:">
          <el-input v-model="formInfo.name" placeholder="请输入疾病名称(选填)" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="系统分类:">
          <el-select v-model="formInfo.form" placeholder="请选择系统分类" size="small" clearable>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.label"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名:">
          <el-input v-model="formInfo.realName" placeholder="请输入姓名(选填)" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="性别:" class="inlineItem">
          <el-radio-group v-model="formInfo.sex" fill="#284e6a" text-color="#fff">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="年龄:">
          <el-input v-model="formInfo.age" placeholder="请输入年龄(选填)" maxlength="50" type="number"></el-input>
        </el-form-item>
      </el-form>
      <div class="startBtn" @click="startCreate">{{ resultForm ? '重新生成' : '开始生成' }}</div>
    </div>
    <!-- 进度条 -->
    <div v-if="createLoading" class="createLoading">
      <div class="loadingText">
        <span>正在生成病例...</span>
        <span>{{ progress }}%</span>
      </div>
      <div class="loadingBox">
        <div class="progressBar"></div>
      </div>
    </div>
    <!-- 结果区域 -->
    <div v-if="createStatus" class="resultBox">
      <div class="textBox">{{ resultText }}</div>
      <div class="textOperate">
        <div>
          <el-tooltip popper-class="againCreateTooltip" effect="dark" content="保持生成条件不变，重新生成病例。" placement="bottom-startCreate">
            <span @click="againCreate">
              <i class="el-icon-refresh"></i>
              继续生成
            </span>
          </el-tooltip>
          <span @click="edit">
            <i class="el-icon-edit"></i>
            编辑
          </span>
        </div>
        <div @click="saveCase">保存病例</div>
      </div>
    </div>
    <EditCaseDialog ref="EditCaseDialog" :addDialog.sync="addDialog" :isAi="true" @success="editOver" />
  </div>
</template>
<script>
import { gsap } from 'gsap'
import _ from 'lodash'
import { caseFormList } from '@/filters'
import { getSecret, caseAdd } from '@/api/case.js'
import { chatRoleModel, chatRoleInfo, appConf, generateStream, aiCall } from '@/api/ai'
import EditCaseDialog from '@/views/case/add.vue'
import { aiRequest } from '@/utils/aiRequest'
import { jsonrepair } from 'jsonrepair'
import Ajv from 'ajv'

const schema = {
  type: 'object',
  properties: {
    realName: { type: 'string' },
    name: { type: 'string' },
    sex: { type: 'string' },
    age: { type: 'string' },
    form: { type: 'string' },
    mainDemands: { type: 'string' }
  },
  required: ['realName', 'name', 'sex', 'age', 'form'] // 必需字段
}

export default {
  name: 'AiCreate',
  components: {
    EditCaseDialog
  },
  data() {
    return {
      formInfo: {
        realName: '',
        name: '',
        sex: '',
        age: '',
        form: ''
      },
      oldInfo: null,
      options: caseFormList,
      // ai相关
      aiToken: null,
      appId: null,
      apiHeaders: null,
      promptTemplate: null, // 提示词
      appConfig: null,
      // 开始生成
      createLoading: false,
      loadingTl: null,
      progress: 0,
      ctx: null,
      // 生成后处理
      aiRes: null,
      createStatus: false,
      resultForm: null,
      resultText: '',
      // 编辑
      addDialog: false
    }
  },
  created() {
    // this.getAiConfig()
  },
  mounted() {
    this.initAnimationContext()
  },
  methods: {
    goBack() {
      this.$router.push('/case')
    },
    // #region ai相关
    // async getAiConfig() {
    //   const { data } = await getSecret()
    //   this.aiToken = data.aiToken
    //   this.appId = data.appId
    //   this.apiHeaders = data
    //   this.getChatRoleModel()
    // },
    // async getChatRoleModel() {
    //   const { data } = await chatRoleModel(this.appId, this.apiHeaders)
    //   // 获取角色配置
    //   const chatRoleId = data.chatRoleList.find((item) => item.roleName === 'SP病例生成').chatRoleId
    //   const { data: roleData } = await chatRoleInfo(chatRoleId, this.apiHeaders)
    //   this.promptTemplate = roleData.promptTemplate
    //   // 获取应用配置
    //   const { data: appConfig } = await appConf(this.appId, this.apiHeaders)
    //   this.appConfig = appConfig
    // },
    // #endregion

    // 初始化动画逻辑
    initAnimationContext() {
      this.ctx = gsap.context(() => {
        return () => {
          this.createStatus = false
          this.resultText = ''
        }
      })
    },
    // #region 进度条动画
    // 生成的动画，进度条
    loadingAnimation() {
      this.createLoading = true
      this.$nextTick(() => {
        this.ctx.add(() => {
          this.loadingTl = gsap.timeline()
          this.loadingTl.to('.formBox', { y: -70, duration: 0.3, ease: 'power1.out' })
          this.loadingTl.from('.createLoading', { scale: 0, duration: 0.3, ease: 'power1.out' })
          this.loadingTl.to('.createLoading > .loadingBox > .progressBar', {
            flexBasis: '100%',
            duration: 2.5,
            onUpdate: () => {
              this.progress = parseInt(this.loadingTl.totalProgress() * 100)
            }
          })
        })
      })
    },
    // 进度条结束后的动画
    createOver() {
      if (this.createLoading) {
        this.loadingTl.progress(1)
        this.ctx.add(() => {
          gsap.to('.createLoading', {
            duration: 0.3,
            scale: 0,
            opacity: 0,
            onComplete: () => {
              this.progress = 0
              this.loadingTl.clear()
              this.disposeData()
              this.$nextTick(() => {
                this.createLoading = false
              })
            }
          })
        })
      }
    },
    // #endregion

    startCreate() {
      if (this.createStatus) {
        this.$confirm('即将根据所填写的条件重新生成病例。请注意，这一操作将覆盖原有数据。请确认是否继续。', '提示', {
          confirmButtonText: '确定继续',
          cancelButtonText: '取消',
          center: true,
          customClass: 'caseConfirm'
        })
          .then(() => {
            if (this.ctx) {
              this.ctx.revert()
            }
            this.initAnimationContext()
            this.sendAIMsg(this.formInfo)
          })
          .catch(() => {})
      } else {
        if (this.createLoading) return this.$message.error('正在生成中，请稍后')
        this.sendAIMsg(this.formInfo)
      }
    },
    sendAIMsg(info) {
      this.loadingAnimation()
      this.oldInfo = _.cloneDeep(info)

      // aiRequest(this.promptTemplate, JSON.stringify(info))
      //   .then((res) => {
      //     console.log(res, '------')
      //     this.aiRes = res
      //     // this.aiRes = res.choices[0].message.content
      //     this.createOver()
      //   })
      //   .catch((err) => {
      //     this.$message.error(err.message)
      //   })
      aiCall({
        type: 1,
        content: JSON.stringify(info),
        isStream: 2,
        model: window.config.VUE_AI_MODEL,
        chatModel: window.config.VUE_AI_CHAT_MODEL
      })
        .then((res) => {
          this.aiRes = res.data
          this.createOver()
        })
        .catch((err) => {
          this.$message.error(err.message)
        })
    },
    // 数据处理
    async disposeData() {
      try {
        // 1. 修复JSON格式

        const repaired = jsonrepair(this.aiRes)

        // 2. 解析JSON
        const data = JSON.parse(repaired)

        // 3. 验证数据结构
        const ajv = new Ajv()
        const validate = ajv.compile(schema)
        const valid = validate(data)

        // 4. 处理缺失字段
        if (!valid) {
          console.log('数据缺失字段:', validate.errors)
          // 补充缺失字段的默认值
          validate.errors.forEach((error) => {
            if (error.keyword === 'required') {
              data[error.params.missingProperty] = null // 或默认值
            }
          })
        }

        // 返回处理后的数据
        const patientInfo = data
        console.log('数据提取成功！')

        const form = this.options.find((item) => item.label === patientInfo.form).value
        const age = patientInfo.age.split('岁')[0]
        this.resultForm = { ...patientInfo, sex: patientInfo.sex == '男' ? 'M' : 'F', form, age, caseType: 1 }

        const text = `姓名：${patientInfo.realName}\n性别：${patientInfo.sex}\n年龄：${patientInfo.age}\n疾病名称：${patientInfo.name}\n系统分类：${patientInfo.form}\n病例简介：${patientInfo?.mainDemands ? patientInfo.mainDemands : ''}`
        this.createStatus = true

        this.$nextTick(() => {
          this.ctx.add(() => {
            gsap
              .timeline()
              .to('.formBox', { x: -500, y: 0, duration: 0.5, ease: 'power1.out' })
              .fromTo(
                '.resultBox',
                { x: 1000 },
                {
                  duration: 0.5,
                  x: 0,
                  ease: 'power1.out',
                  onComplete: () => {
                    this.startTypewriting(text)
                  }
                },
                '-=0.1'
              )
          })
        })
      } catch (error) {
        console.error('数据处理失败:', error)
        return null
      }
    },
    // 打字机效果
    startTypewriting(val) {
      let index = 0
      const text = val.trim().split('')
      const timer = setInterval(() => {
        if (index >= text.length) {
          clearInterval(timer)
          this.ctx.add(() => {
            gsap.to('.textOperate', { duration: 0.5, y: 0, ease: 'power1.inOut' })
          })
        } else {
          this.resultText += text[index]
          index++
        }
      }, 20)
    },

    // #region 重新生成/编辑
    // 重新生成
    againCreate() {
      if (this.ctx) {
        this.ctx.revert()
      }
      this.initAnimationContext()
      this.sendAIMsg(this.oldInfo)
    },
    edit() {
      this.addDialog = true
      this.$refs['EditCaseDialog'].form = _.cloneDeep({ ...this.resultForm, caseType: 1 })
    },
    editOver(info) {
      this.resultForm = info
      this.resultText = ''

      const form = this.options.find((item) => item.value === info.form).label
      const patientInfo = { ...info, sex: info.sex === 'F' ? '女' : '男', form }

      const text = `姓名：${patientInfo.realName}\n性别：${patientInfo.sex}\n年龄：${patientInfo.age}岁\n疾病名称：${patientInfo.name}\n系统分类：${patientInfo.form}\n病例简介：${patientInfo.mainDemands}`
      this.startTypewriting(text)
    },
    // #endregion

    // 保存
    saveCase() {
      this.$confirm('请注意，AI生成的病例可能与实际情况存在偏差，请在保存前仔细检查并核对信息', '提示', {
        confirmButtonText: '确定保存',
        cancelButtonText: '取消',
        center: true,
        customClass: 'caseConfirm'
      })
        .then(() => {
          this.ctx.revert()
          this.sendAIMsg(this.formInfo)

          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          const info = _.cloneDeep({ ...this.resultForm, allScore: 0, createType: 2 })
          caseAdd(info)
            .then(() => {
              this.$message.success('病例保存成功！')
              loading.close()
              this.goBack()
            })
            .catch((err) => {
              console.log(err)
              loading.close()
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  position: relative;
  display: flex;
  justify-content: center;
  height: 100vh;
  width: 100%;
  padding: 0;
  background: url('~@/assets/case/ai/bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  .goBackButton {
    position: absolute;
    left: 50px;
    top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    background: #f4f9ff;
    cursor: pointer;
    img {
      width: 38px;
      height: 38px;
    }
    &:hover {
      background: #e7f0ff;
    }
  }
  .formBox {
    position: relative;
    width: 910px;
    height: 780px;
    padding: 90px 60px 28px 68px;
    margin-top: 107px;
    background: url('~@/assets/case/ai/formBg.png') no-repeat;
    background-size: cover;
    .header {
      position: absolute;
      left: 27px;
      top: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      img {
        margin-right: 6px;
      }
    }
    .alert {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #999999;
    }
    .startBtn {
      position: relative;
      width: 135px;
      height: 50px;
      margin: 0 auto;
      margin-top: 50px;
      background: linear-gradient(270deg, #1f5cff 0%, #9901ff 100%);
      box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
      border-radius: 55px 55px 55px 55px;
      font-family: PingFang SC;
      font-size: 20px;
      color: #ffffff;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        &::after {
          content: '';
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 60px;
          height: 12px;
          background: #fff;
          filter: blur(10.5px);
        }
      }
    }
  }
  .createLoading {
    position: absolute;
    left: 50%;
    bottom: 35px;
    transform: translateX(-50%);
    width: 895px;
    .loadingText {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      & > span:first-of-type {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ababab;
      }
      & > span:last-of-type {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
      }
    }
    .loadingBox {
      display: flex;
      align-items: center;
      width: 100%;
      height: 23px;
      padding: 0 7px;
      margin-top: 7px;
      background: rgba(245, 245, 245, 0.2);
      box-shadow: inset 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
      border-radius: 72px 72px 72px 72px;
      overflow: hidden;
      .progressBar {
        flex-basis: 0;
        width: 100%;
        height: 15px;
        background: linear-gradient(270deg, #1f5cff 0%, #9901ff 100%);
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
        border-radius: 55px 55px 55px 55px;
      }
    }
  }
  .resultBox {
    position: absolute;
    right: 0;
    top: 0;
    width: 960px;
    height: 100%;
    padding: 36px 40px;
    background: #ffffff;
    .textBox {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #000000;
      line-height: 45px;
      white-space: pre-wrap;
    }
    .textOperate {
      position: absolute;
      bottom: 37px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: calc(100% - 80px);
      height: 79px;
      padding: 0 20px;
      background: #f4f4f4;
      border-radius: 20px 20px 20px 20px;
      border: 1px solid #eaeaea;
      transform: translateY(300px);
      & > div:first-of-type {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #666666;
        cursor: pointer;
        & > span:hover {
          color: #000;
        }
        & > span:first-of-type {
          margin-right: 25px;
        }
      }
      & > div:last-of-type {
        width: 135px;
        height: 50px;
        background: #2a54ff;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
        border-radius: 55px 55px 55px 55px;
        font-family: PingFang SC;
        font-size: 20px;
        color: #ffffff;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        transition: all 0.3s;
        &:hover {
          background: linear-gradient(270deg, #1f5cff 0%, #9901ff 100%);
        }
      }
    }
  }

  ::v-deep {
    .el-form {
      margin-top: 20px;
      .el-form-item {
        .el-form-item__label {
          padding-bottom: 5px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 22px;
          color: #000000;
        }
        .el-input {
          .el-input__inner {
            width: 782px;
            height: 50px;
            background: #ffffff;
            border: none;
            border-radius: 10px;
            color: #000000;
            font-family: PingFang SC;
            font-size: 20px;
            &::placeholder {
              color: #999999;
            }
          }
        }
        .el-radio-group {
          display: flex;
          .el-radio {
            display: flex;
            align-items: center;
            .el-radio__inner {
              width: 18px;
              height: 18px;

              background: #fff;
              border-color: #242e3b;
            }
            .el-radio__label {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              font-size: 20px;
              color: #000;
            }
          }

          .el-radio__input.is-checked .el-radio__inner {
            &::after {
              background: #242e3b;
              width: 12px;
              height: 12px;
            }
          }
        }
      }
      .inlineItem {
        display: flex;
        align-items: center;
        .el-form-item__label {
          margin-right: 15px;
          padding-bottom: 0;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-select-dropdown__wrap {
  overflow-x: hidden;
  .el-select-dropdown__item {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 20px;
    color: #666666;
    &:hover {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #000000;
    }
  }
}
.againCreateTooltip {
  font-size: 16px;
}
</style>
