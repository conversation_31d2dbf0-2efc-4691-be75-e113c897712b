<template>
  <div class="examGrade-studentDetails">
    <el-row class="pageTop">
      <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
      <div class="title">学生考核成绩详情</div>
    </el-row>
    <div class="dataBox">
      <template v-if="info">
        <div class="header">
          <div class="studentInfo">
            <div class="title">学生信息</div>
            <div class="info">
              <div>
                <div class="user">
                  <div class="photo">
                    <img class="photo" :src="baseurl + info.icon" alt="" />
                    <img v-if="info.sex === 'M'" class="sexIcon" src="@/assets/case/manIcon.png" alt="" />
                    <img v-else class="sexIcon" src="@/assets/case/womanIcon.png" alt="" />
                  </div>
                  <div class="name">
                    <span>{{ info.name }}</span>
                    <span>{{ info.loginName }}</span>
                  </div>
                </div>
                <div class="statistics_item border_item">
                  <span>得分</span>
                  <span>{{ info.score ? info.score : 0 }}</span>
                </div>
                <div class="statistics_item">
                  <span>实考病例 <i>(个)</i></span>
                  <span>{{ info.alreadyCaseCont ? info.alreadyCaseCont : 0 }}</span>
                </div>
                <div class="statistics_item border_item">
                  <span>用时<i>(分)</i></span>
                  <span>{{ info.time ? parseInt(info.time / 60) : 0 }}</span>
                </div>
                <div class="statistics_item">
                  <span>考试时间<i>(分)</i></span>
                  <span>{{ info.startTime }} ~ {{ info.endTime }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="examInfo">
            <div class="title">考试信息</div>
            <div class="info">
              <div>
                <div class="statistics_item">
                  <span>总分 <i>(分)</i></span>
                  <span>{{ info.allScore ? info.allScore : 0 }}</span>
                </div>
                <div class="statistics_item border_item">
                  <span>及格分 <i>(分)</i></span>
                  <span>{{ info.passScore ? info.passScore : 0 }}</span>
                </div>
                <div class="statistics_item border_item">
                  <span>考核病例 <i>(个)</i></span>
                  <span>{{ info.allCaseCount ? info.allCaseCount : 0 }}</span>
                </div>
                <div class="statistics_item border_item">
                  <span>考核限时 <i>(分)</i></span>
                  <span>{{ info.examTime ? info.examTime : 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="body">
          <div class="title">病史采集统计</div>
          <div class="cases">
            <div :class="[{ checkedCase: checkedCaseId === item.examCaseId }, 'case']" v-for="item in info.caseExamDetails" :key="item.examCaseId" @click="caseChange(item)">
              <Avatar :age="item.age" :sex="item.sex" />
              <div class="caseName">
                <div>{{ item.name }}</div>
                <span>{{ item.realName }}</span>
                <span>{{ item.age }}岁</span>
              </div>
              <span :class="[{ isTest2: item.isTest === 1 }, 'isTest']">{{ item.isTest === 1 ? '已考' : '未考' }}</span>
            </div>
          </div>
          <div class="statistics">
            <ChartCasehistory ref="ChartCasehistory" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import { caseExamStudentDetail } from '@/api/caseExam'
import Avatar from '@/views/case/Avatar'
import ChartCasehistory from '@/views/examGrade/components/Chart_Casehistory'
export default {
  name: '',
  components: {
    Avatar,
    ChartCasehistory
  },
  data() {
    return {
      baseurl: window.config.VUE_FILE_BASE_PATH,
      info: null,
      checkedCaseId: null
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    async getDetails() {
      const { data } = await caseExamStudentDetail({ examStudentId: this.$route.params.examStudentId })
      this.info = data
      this.checkedCaseId = this.info.caseExamDetails[0].examCaseId
      this.$nextTick(() => {
        this.$refs['ChartCasehistory'].init(this.info.caseExamDetails[0])
      })
    },
    caseChange(val) {
      this.checkedCaseId = val.examCaseId
      this.$nextTick(() => {
        this.$refs['ChartCasehistory'].init(val)
      })
      console.log(val)
    }
  }
}
</script>
<style scoped lang="scss">
.examGrade-studentDetails {
  padding: 0 22px;
  .pageTop {
    margin-top: 40px;
    margin-bottom: 28px;
    .goBack {
      position: absolute;
    }
    .title {
      font-size: 25px;
      text-align: center;
    }
  }
  .dataBox {
    width: 100%;
    margin-top: 20px;
    border: 1px solid #c7cbd0;
    .header {
      display: flex;
      align-items: center;
      padding: 30px 20px 25px;
      width: 100%;
      border-bottom: 1px solid #c7cbd0;
      .title {
        margin-bottom: 24px;
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        color: #333333;
      }
      .studentInfo,
      .examInfo {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        .info {
          display: flex;
          & > div {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-around;
            height: 90px;
            margin-right: 11px;
            background: #f4f7ff;
            border-radius: 4px 4px 4px 4px;
            &:last-of-type {
              margin-right: 0;
            }
            .user {
              display: flex;
              align-items: center;
              .photo {
                position: relative;
                height: 51px;
                width: 51px;
                border-radius: 50%;
                .sexIcon {
                  position: absolute;
                  right: -3px;
                  bottom: 0;
                  width: 18px;
                  height: 18px;
                }
              }
              .name {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 12px;
                & > span:first-of-type {
                  margin-bottom: 8px;
                  font-size: 18px;
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  color: #333333;
                }
                & > span:last-of-type {
                  font-size: 16px;
                  font-family:
                    PingFang SC,
                    PingFang SC;
                  font-weight: 500;
                  color: #999999;
                }
              }
            }
            .statistics_item {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;

              & > span:first-of-type {
                font-size: 16px;
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 500;
                color: #333333;
                i {
                  font-style: normal;
                  font-size: 12px;
                }
              }
              & > span:last-of-type {
                margin-top: 14px;
                font-size: 18px;
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: bold;
                color: #1890ff;
              }
            }
            .border_item {
              position: relative;
              &::before {
                content: '';
                position: absolute;
                left: -50%;
                height: 47px;
                border-left: 1px dashed #d6ebff;
              }
              &::after {
                content: '';
                position: absolute;
                right: -50%;
                height: 47px;
                border-right: 1px dashed #d6ebff;
              }
            }
          }
        }
      }
      .examInfo {
        margin-left: 11px;
        .info > div {
          .statistics_item {
            flex: 1;
          }
          .border_item {
            &::before {
              left: 0%;
            }
            &::after {
              display: none;
            }
          }
        }
      }
    }
    .body {
      padding: 30px 20px 20px;
      .title {
        margin-bottom: 20px;
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        color: #333333;
      }
      .cases {
        display: flex;
        .case {
          position: relative;
          display: flex;
          align-items: center;
          margin-right: 12px;
          min-width: 268px;
          padding-right: 60px;
          height: 87px;
          background: #f4f6f8;
          border-radius: 10px;
          border: 1px solid #f4f6f8;
          cursor: pointer;
          ::v-deep {
            .Avatar {
              margin-left: 14px;
              & > div {
                position: relative;
                width: 49px;
                height: 49px;
              }
              .sexIcon {
                position: absolute;
                width: 16px;
                height: 16px;
                bottom: 1px;
                right: -2px;
              }
            }
          }
          .caseName {
            margin-left: 10px;
            & > div {
              margin-bottom: 5px;
              font-size: 20px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #000000;
            }
            & > span {
              margin-right: 10px;
              font-size: 16px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #666666;
              &:last-of-type {
                margin-right: 0;
                color: #999999;
                font-size: 14px;
              }
            }
          }
          .isTest {
            position: absolute;
            right: 14px;
            top: 24px;
            width: 34px;
            height: 22px;
            line-height: 22px;
            background: rgba(153, 153, 153, 0.2);
            border-radius: 4px;
            font-size: 12px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #999999;
            text-align: center;
          }
          .isTest2 {
            background: #cfeccf;
            color: #11a011;
          }
        }
        .checkedCase {
          position: relative;
          border: 1px solid #1890ff;
          &::before {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -20px;
            transform: translateX(-50%);
            border: 10px solid #1890ff;
            border-left-color: transparent;
            border-right-color: transparent;
            border-bottom-color: transparent;
          }
        }
      }
      .statistics {
        margin-top: 18px;
        width: 100%;
        height: 407px;
        background: linear-gradient(180deg, #f4f6f8 0%, rgba(244, 246, 248, 0) 100%);
        border-radius: 10px;
      }
    }
  }
}
</style>
