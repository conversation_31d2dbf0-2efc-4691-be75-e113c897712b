<template>
  <div class="">
    <el-dialog :visible="addDialog" width="850px" @open="getTypeList" @close="close" :close-on-click-modal="false">
      <template v-slot:title>
        <el-row type="flex" justify="space-between" class="dialogHeader">
          <div>{{ dialogTitle }}</div>
          <div class="totalStatistics">
            <span
              >已录入问题/个 （<i>{{ questionNum }}</i> ）</span
            >
            <span
              >分值合计/分 （<i>{{ questionTotalScore }}</i> ）</span
            >
          </div>
        </el-row>
      </template>
      <el-form ref="addForm" class="addForm" :model="formInfo" label-width="90px" :rules="rules">
        <el-form-item label="问诊类型:" prop="typeId">
          <el-radio-group v-model="formInfo.typeId" size="small">
            <el-radio v-for="item in typeList" :key="item.typeId" :label="item.typeId">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="问题属性:" prop="level">
          <el-radio-group v-model="formInfo.level" size="small">
            <el-radio :label="1">重要</el-radio>
            <el-radio :label="2">常规</el-radio>
            <el-radio :label="3">无效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分数:" prop="score">
          <el-input-number v-model="formInfo.score" :min="0" :max="100" label="分数" size="small" :step="0.5" :disabled="formInfo.level == 3"></el-input-number>
        </el-form-item>
        <el-form-item label="问题:" prop="problem">
          <el-input v-model="formInfo.problem" type="textarea" :rows="3" :autosize="{ minRows: 3, maxRows: 6 }" size="small" maxlength="150" placeholder="请输入问诊问题内容"></el-input>
        </el-form-item>
        <el-form-item label="前置问题:">
          <ElSelectTree ref="ElSelectTree" @selectChange="formInfo.parentId = $event" />
        </el-form-item>
        <!-- <el-form-item label="关键字:" prop="keyword">
          <el-row type="flex" justify="space-between" class="keyword">
            <el-col v-for="(item, index) in keywords" :key="index" :span="12">
              <span class="keywordLabel"> 关键字{{ index + 1 }}:</span>
              <el-popover :value="item.showPopover && Boolean(repeatQuestionKeywordList.length)" placement="bottom-start" popper-class="keywordPopover" title="关键词，重叠提示" width="200" trigger="manual">
                <div class="repeatQuestion" v-for="li in repeatQuestionKeywordList" :key="li.questionId">
                  <div>
                    <span>问:</span>
                    <span>{{ li.problem }}</span>
                  </div>
                  <div>
                    <span>词:</span>
                    <span v-html="highlightedSentence(item.keyword, li.keyword)"></span>
                  </div>
                </div>
                <el-input v-model="item.keyword" placeholder="请输入关键字" class="keywordInput" size="small" @blur="test(item)" @focus="inputSearchRepeat(item)" @input="inputSearchRepeat(item)" slot="reference"></el-input>
              </el-popover>
              <i class="el-icon-circle-plus" @click="addKeyword"></i>
              <i v-if="index !== 0" class="el-icon-remove" @click="delKeyword(index)"></i>
            </el-col>
          </el-row>
        </el-form-item> -->
        <el-form-item label="回答:" prop="answer">
          <el-input v-model="formInfo.answer" type="textarea" :rows="3" :autosize="{ minRows: 3, maxRows: 6 }" size="small" maxlength="2000" placeholder="请输入问诊问题答复内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseQuestionTypeList } from '@/api/caseQuestionType'
import { caseAddQuestion, caseUpdateQuestion, questionListByKeyword } from '@/api/case'
import elSelectTree from './ElSelectTree'
export default {
  name: '',
  components: {
    ElSelectTree: elSelectTree
  },
  props: {
    addDialog: {
      type: Boolean,
      require: true
    },
    questionNum: {
      type: String | Number,
      require: true
    },
    questionTotalScore: {
      type: String | Number,
      require: true,
      default: 0
    }
  },
  computed: {
    dialogTitle() {
      return this.formInfo.questionId ? '编辑问诊问题' : '添加问诊问题'
    }
  },
  watch: {
    'formInfo.level': {
      handler(val) {
        if (val == 3) {
          this.formInfo.score = 0
        }
      }
    }
  },
  data() {
    return {
      formInfo: {
        caseId: this.$route.params.id,
        typeId: null,
        parentId: 0,
        level: null, // 问题属性 1 重要 2 常规 3 无效
        score: null,
        problem: null,
        keyword: null,
        answer: null
      },
      keywords: [{ keyword: null, showPopover: false }],
      typeList: [],
      rules: {
        typeId: [{ required: true, message: '请选择问诊问题', trigger: 'change', type: 'number' }],
        level: [{ required: true, message: '请选择问题属性', trigger: 'change', type: 'number' }],
        score: [{ required: true, message: '请选择输入分数', trigger: 'change', type: 'number' }],
        problem: [{ required: true, message: '请输入问题', trigger: 'blur' }],
        keyword: [{ required: true, message: '关键字至少有一个', trigger: 'blur' }],
        answer: [{ required: true, message: '请输入回答', trigger: 'blur' }]
      },
      repeatQuestionKeywordList: [] // 根据输入的关键字查出的重复的数据
    }
  },
  created() {},
  methods: {
    async getTypeList() {
      const { data } = await caseQuestionTypeList()
      this.typeList = data.list
    },
    test(val) {
      this.$refs['addForm'].validateField('keyword')
      this.formInfo.keyword = this.keywords
        .map((item) => item.keyword)
        .filter((item) => item)
        .join(',')
      val.showPopover = false
    },
    async inputSearchRepeat(val) {
      if (val.keyword) {
        const info = {
          keyword: val.keyword,
          caseId: this.formInfo.caseId,
          questionId: this.formInfo.questionId ? this.formInfo.questionId : null
        }
        const { data } = await questionListByKeyword(info)
        this.repeatQuestionKeywordList = data
        val.showPopover = this.repeatQuestionKeywordList.length ? true : false
        console.log('查出的数据', data)
      } else {
        this.repeatQuestionKeywordList = []
        val.showPopover = false
      }
    },
    // 高亮显示输入的关键字
    highlightedSentence(inputKeyword, questionKeyword) {
      if (inputKeyword && questionKeyword) {
        const escapedWord = this.escapeRegExp(inputKeyword)
        const re = new RegExp(`(${escapedWord})`, 'gi')
        const highlighted = questionKeyword.replace(re, '<span class="highlight">$1</span>')
        return highlighted
      }
    },
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    },

    addKeyword() {
      this.keywords.push({ keyword: null, showPopover: false })
      this.$refs['addForm'].validateField('keyword')
    },
    delKeyword(index) {
      this.keywords.splice(index, 1)
      this.formInfo.keyword = this.keywords.map((item) => item.keyword).join(',')

      this.$refs['addForm'].validateField('keyword')
    },
    // 回显编辑数据
    showData(val) {
      this.formInfo = {
        caseId: this.$route.params.id,
        questionId: val.questionId,
        typeId: val.typeId,
        parentId: val.parentId,
        level: val.level, // 问题属性 1 重要 2 常规 3 无效
        score: val.score,
        problem: val.problem,
        keyword: val.keyword,
        answer: val.answer
      }
      // const arr = []
      // val.keyword.split(',').forEach((element) => {
      //   arr.push({
      //     keyword: element,
      //     showPopover: false
      //   })
      // })
      // this.keywords = arr
      this.$nextTick(() => {
        this.$refs['ElSelectTree'].showData(val.parentId)
      })
    },
    close() {
      this.formInfo = {
        caseId: this.$route.params.id,
        typeId: null,
        parentId: 0,
        level: null, // 问题属性 1 重要 2 常规 3 无效
        score: null,
        problem: null,
        keyword: null,
        answer: null
      }
      this.repeatQuestionKeywordList = []
      this.keywords = [{ keyword: null, showPopover: false }]
      this.$refs['addForm'].resetFields()
      this.$refs['ElSelectTree'].reset()
      this.$emit('update:addDialog', false)
    },
    confirm() {
      this.$refs['addForm'].validate((val) => {
        if (val) {
          const loading = this.$loading({
            text: '数据保存中,请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.formInfo.questionId) {
            caseUpdateQuestion({ ...this.formInfo })
              .then(() => {
                loading.close()
                this.$message.success('修改问诊问题成功！')
                this.close()
                this.$emit('success')
              })
              .catch(() => {
                loading.close()
              })
          } else {
            caseAddQuestion({ ...this.formInfo })
              .then(() => {
                loading.close()
                this.$message.success('添加问诊问题成功！')
                this.close()
                this.$emit('success')
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .dialogHeader {
    width: 560px;
    .totalStatistics {
      font-size: 16px;
      i {
        font-style: normal;
        color: red;
      }
    }
  }
}

.keyword {
  flex-wrap: wrap;
  .el-col {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    .keywordLabel {
      padding: 0 12px 0 0;
      font-size: 14px;
      font-weight: 700;
      color: #606266;
      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    .keywordInput {
      width: 200px;
    }
    .score {
      padding-left: 15px;
    }
    .el-icon-circle-plus {
      margin-left: 15px;
      font-size: 20px;
      color: #3f9cfc;
      cursor: pointer;
    }
    .el-icon-remove {
      margin-left: 15px;
      font-size: 20px;
      color: #ff0000;
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.keywordPopover {
  width: 400px !important;
  .el-popover__title {
    font-size: 14px;
    font-weight: bold;
  }
  .repeatQuestion {
    padding-bottom: 10px;
    border-bottom: 1px solid #606266;
    font-size: 14px;
    & > div {
      margin-top: 5px;
    }
  }
  .highlight {
    color: red; /* 或者其他你想要的颜色 */
  }
}
</style>
