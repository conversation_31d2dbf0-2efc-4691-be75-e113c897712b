<template>
  <div class="app-container">
    <div class="goBackButton" @click="goBack">
      <img src="@/assets/case/goBackIcon.png" alt="" />
    </div>
    <div class="contentBox" :class="{ createOverLayout: createQuestionStatas }">
      <!-- 病例信息 -->
      <div class="caseInfo">
        <div class="headerTitle">病例信息</div>
        <div class="infoBody" v-if="caseInfo">
          <div class="patientsInfo">
            <span>
              <em>姓名:</em>
              {{ caseInfo.realName }}
            </span>
            <span class="sex">
              <em>性别:</em>
              {{ caseInfo.sex === 'F' ? '女' : '男' }}
            </span>
            <span class="age">
              <em>年龄:</em>
              {{ caseInfo.age }}
            </span>
            <span>
              <em>疾病名称:</em>
              {{ caseInfo.name }}
            </span>
            <span>
              <em>系统分类:</em>
              {{ caseInfo.form | caseForm }}
            </span>
          </div>
          <div class="mainDemands">
            <span>病例简介：</span>
            <p>{{ caseInfo.mainDemands }}</p>
          </div>
        </div>
      </div>
      <!-- 生成数量表单 -->
      <div class="createNumBox">
        <div class="header">
          <img src="@/assets/case/aiIcon.png" alt="" />
          AI智能生成对话
        </div>
        <div class="createBox">
          <div class="createAlert">您需要生成多少轮对话？（每轮对话包括一次提问和一次回答）</div>
          <el-input class="createNumInput" v-model="createNum" placeholder="请输入对话轮数" type="number" @input="createNumInputHandle"></el-input>
          <div class="startBtn" @click="startCreate(false)">{{ createQuestionStatas ? '重新生成' : '开始生成' }}</div>
        </div>
        <div v-if="createQuestionStatas" class="resultBox">
          <div class="resultView" id="dialogueContentParent">
            <div class="dialogueContent" v-for="(item, index) in resultList" :key="index">
              <div class="ask">
                <section class="operate">
                  <el-tooltip popper-class="operateTooltip" effect="dark" content="删除" placement="bottom">
                    <i class="el-icon-delete" @click="remove(item)"></i>
                  </el-tooltip>
                  <el-tooltip popper-class="operateTooltip" effect="dark" content="编辑" placement="bottom">
                    <i class="el-icon-edit" @click="edit(item, index)"></i>
                  </el-tooltip>
                  <el-tooltip popper-class="operateTooltip" effect="dark" content="保持原意，重新生成该轮对话内容" placement="bottom">
                    <i class="el-icon-refresh" @click="againCreateQuestion(item)"></i>
                  </el-tooltip>
                </section>
                <section class="askText">
                  <div>
                    {{ item.problem ? item.problem.split('&')[0] : '' }}
                    <span class="qustionLoadingText" v-show="!item.problem">生成中... <i>_</i> </span>
                  </div>
                  <div>
                    <span></span>
                    {{ item.type }}
                  </div>
                </section>
                <section class="photo">
                  <img src="@/assets/case/ai/doctorPhoto.png" alt="" />
                </section>
              </div>
              <div class="reply">
                <section class="photo">
                  <img src="@/assets/case/ai/patientsPhoto.png" alt="" />
                </section>
                <section class="askText">
                  {{ item.answer }}
                  <span class="qustionLoadingText" v-show="!item.answer">生成中... <i>_</i> </span>
                </section>
              </div>
            </div>
          </div>
          <div class="operateView">
            <div>
              <el-tooltip effect="dark" content="基于原有问题继续生成" placement="bottom-start">
                <span @click="startCreate(true)">
                  <i class="el-icon-refresh"></i>
                  继续生成
                </span>
              </el-tooltip>
            </div>
            <div @click="saveQuestion">保存对话</div>
          </div>
        </div>
      </div>
      <!-- 进度条 -->
      <div v-if="showProgress" class="createLoading">
        <div class="loadingText">
          <span>正在生成对话...</span>
          <span>{{ progress }}%</span>
        </div>
        <div class="loadingBox">
          <div class="progressBar"></div>
        </div>
      </div>
    </div>
    <!-- 编辑弹窗 -->
    <el-dialog title="编辑" custom-class="editDialog" center :visible.sync="editDialog" width="width">
      <div class="editDialog-content">
        <div>
          <label>问题：</label>
          <el-input v-model="userProblem" placeholder="请输入问题"></el-input>
        </div>
        <div>
          <label>回答：</label>
          <el-input v-model="userAnswer" placeholder="请输入回答"></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="editDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">保存编辑</el-button>
      </div>
    </el-dialog>
    <!-- 保存对话弹窗 -->
    <SaveQuestionDialog ref="SaveQuestionDialogRef" @success="goBack" />
  </div>
</template>
<script>
import { caseDetail, getSecret } from '@/api/case'
import { chatRoleModel, chatRoleInfo, appConf, generateStream, aiCall } from '@/api/ai'

import { gsap } from 'gsap'
import { caseForm } from '@/filters'
import SaveQuestionDialog from './SaveQuestionDialog.vue'
import { aiRequest } from '@/utils/aiRequest'
import { jsonrepair } from 'jsonrepair'
import Ajv from 'ajv'
export default {
  name: 'AiCreateQuestion',
  components: {
    SaveQuestionDialog
  },
  data() {
    return {
      caseInfo: null,
      createNum: null,
      // ai相关
      aiToken: null,
      appId: null,
      apiHeaders: null,
      chatRoleList: [],
      promptTemplate: null, // 提示词
      appConfig: null,
      // 进度条
      isStartCreate: false, // 是否开始生成
      showProgress: false,
      progressTl: null,
      progress: 0,
      // 生成后处理
      aiRes: null,
      createQuestionStatas: false, // 是否生成结束
      resultList: [],
      // 编辑
      editDialog: false,
      editIndex: 0,
      userProblem: null,
      userAnswer: null,
      // 继续生成
      oldList: []
    }
  },
  created() {
    // this.getAiConfig()
    this.getDetails()
  },
  mounted() {
    this.initAnimationContext()
  },
  methods: {
    goBack() {
      this.$router.push(`/case/historyCollect/${this.$route.params.id}`)
    },
    async getDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.caseInfo = data
    },
    createNumInputHandle() {
      const value = this.createNum.toString()
      const maxLength = 3
      // 如果超过最大长度，则截断
      if (value.length > maxLength) {
        this.createNum = value.slice(0, maxLength)
      }
    },

    // #region ai模型配置
    // async getAiConfig() {
    //   const { data } = await getSecret()
    //   this.aiToken = data.aiToken
    //   this.appId = data.appId
    //   this.apiHeaders = data
    //   this.getChatRoleModel()
    // },
    // async getChatRoleModel() {
    //   const { data } = await chatRoleModel(this.appId, this.apiHeaders)
    //   this.chatRoleList = data.chatRoleList
    //   // 获取角色配置
    //   const chatRoleId = data.chatRoleList.find((item) => item.roleName === 'SP对话生成').chatRoleId
    //   const { data: roleData } = await chatRoleInfo(chatRoleId, this.apiHeaders)
    //   this.promptTemplate = roleData.promptTemplate
    //   // 获取应用配置
    //   const { data: appConfig } = await appConf(this.appId, this.apiHeaders)
    //   this.appConfig = appConfig
    // },

    // #endregion

    // #region 动画相关逻辑
    initAnimationContext() {
      this.ctx = gsap.context(() => {
        return () => {}
      })
    },
    // 进度条动画
    loadingAnimation() {
      this.showProgress = true
      this.$nextTick(() => {
        this.ctx.add(() => {
          this.progressTl = gsap.timeline()
          this.progressTl.from('.createLoading', { scale: 0, duration: 0.3, ease: 'power1.out' })
          this.progressTl.to('.createLoading > .loadingBox > .progressBar', {
            flexBasis: '100%',
            duration: 2.5,
            onUpdate: () => {
              this.progress = parseInt(this.progressTl.totalProgress() * 100)
            }
          })
        })
      })
    },
    // 进度条结束后的动画
    loadingAnimationOver() {
      if (this.showProgress) {
        this.progressTl.progress(1)
        this.ctx.add(() => {
          gsap.to('.createLoading', {
            duration: 0.3,
            scale: 0,
            opacity: 0,
            onComplete: () => {
              this.progress = 0
              this.progressTl.clear()
              this.disposeData({ isFirst: this.createQuestionStatas ? 1 : 0, isAppend: this.oldList.length })
              this.$nextTick(() => {
                this.showProgress = false
              })
            }
          })
        })
      }
    },
    // 结果显示动画
    resultShowAnimation(type) {
      this.ctx.add(() => {
        if (type === 0) {
          gsap.fromTo(
            '.contentBox',
            { slice: 0, opacity: 0 },
            {
              delay: 0.3,
              duration: 0.6,
              scale: 1,
              opacity: 1,
              ease: 'power1.in'
            }
          )
        } else {
          gsap.from('.resultView', { duration: 0.3, scale: 0.2, opacity: 0, ease: 'power1.inOut' })
        }
      })
    },
    // 重新生成单轮对话的动画
    againCreateQuestionAnimation() {
      this.$nextTick(() => {
        this.ctx.add(() => {
          gsap.to('.qustionLoadingText > i', {
            repeat: -1,
            opacity: 0,
            duration: 0.3,
            yoyo: true,
            ease: 'steps(1)'
          })
        })
      })
    },
    // #endregion

    // 开始生成
    /**  @param {boolean} isAppend 是否继续生成 */
    startCreate(isAppend = false) {
      if (this.isStartCreate) {
        return this.$message.error('正在生成中,请稍后')
      }
      if (!this.createNum?.trim()) {
        return this.$message.error('请输入需要生成的对话数量')
      }
      this.oldList = []
      this.isStartCreate = true

      // 继续生成
      if (this.createQuestionStatas && isAppend) {
        this.oldList = _.cloneDeep(this.resultList)
        this.resultList = []
        this.sendAIMsg()
      } else if (this.createQuestionStatas) {
        this.$confirm('即将根据所填写的对话轮数重新生成对话。请注意，这一操作将覆盖原有数据。请确认是否继续', '提示', {
          confirmButtonText: '确定继续',
          cancelButtonText: '取消',
          center: true,
          customClass: 'caseConfirm'
        })
          .then(() => {
            // 重新生成
            this.resultList = []
            this.sendAIMsg()
          })
          .catch(() => {
            this.isStartCreate = false
          })
      } else {
        this.sendAIMsg()
      }
    },

    async sendAIMsg() {
      this.loadingAnimation()
      const info = {
        realName: this.caseInfo.realName,
        sex: this.caseInfo.sex === 'F' ? '女' : '男',
        age: this.caseInfo.age,
        name: this.caseInfo.name,
        form: caseForm(this.caseInfo.form),
        mainDemands: this.caseInfo.mainDemands
      }
      let promptTemplate
      let userContent
      if (this.oldList.length) {
        // const chatRoleId = this.chatRoleList.find((chatRole) => chatRole.roleName === 'sp继续生成').chatRoleId
        // const { data } = await chatRoleInfo(chatRoleId, this.apiHeaders)
        // promptTemplate = data.promptTemplate
        userContent = `病例信息：${JSON.stringify(info)} \n 排除列表: ${JSON.stringify(this.oldList)}`
      } else {
        // promptTemplate = this.promptTemplate.replace(/{{createNum}}/g, this.createNum)
        userContent = JSON.stringify(info)
      }

      // aiRequest(promptTemplate, userContent)
      //   .then((res) => {
      //     // this.aiRes = res.choices[0].message.content
      //     this.aiRes = res
      //     this.isStartCreate = false
      //     this.loadingAnimationOver()
      //   })
      //   .catch((err) => {
      //     this.$message.error(err.message)
      //     this.isStartCreate = false
      //     this.showProgress = false
      //     this.initAnimationContext()
      //   })
      aiCall({
        type: this.oldList.length ? 3 : 2,
        content: userContent,
        isStream: 2,
        model: window.config.VUE_AI_MODEL,
        chatModel: window.config.VUE_AI_CHAT_MODEL
      })
        .then((res) => {
          this.aiRes = res.data
          console.log(this.aiRes, '-----')
          this.isStartCreate = false
          this.loadingAnimationOver()
        })
        .catch((err) => {
          this.$message.error(err.message)
          this.isStartCreate = false
          this.showProgress = false
          this.initAnimationContext()
        })
    },
    // 生成结束，开始处理数据
    async disposeData({ isFirst, isAppend }) {
      try {
        const patientInfo = this.cleanJsonResponse(this.aiRes)

        if (patientInfo.length > this.createNum) {
          this.resultList = isAppend ? this.oldList.concat(patientInfo.slice(0, this.createNum)) : patientInfo.slice(0, this.createNum)
        } else {
          this.resultList = isAppend ? this.oldList.concat(patientInfo) : patientInfo
        }
        if (isAppend) {
          this.scrollBottom()
        }
        console.log('数据提取成功！', patientInfo)
        this.createQuestionStatas = true
        // 第一次生成和其他次生成动画不一样。
        if (this.ctx) {
          this.ctx.revert()
        }
        this.initAnimationContext()
        this.resultShowAnimation(isFirst)
      } catch (err) {
        if (isAppend) {
          this.resultList = this.oldList
        }
        console.error(err)
        this.$message.error('数据格式错误，请重新生成')
      }
    },
    // 对json字符串进行处理
    cleanJsonResponse(val) {
      const repaired = jsonrepair(val)
      const data = JSON.parse(repaired)
      console.log(data, '----')
      // 定义Schema进行校验
      const schema = {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            problem: { type: 'string' },
            answer: { type: 'string' },
            type: { type: 'string' },
            id: { type: 'number', nullable: true },
            options: {
              type: 'array',
              items: { type: 'string' },
              nullable: true
            }
          },
          required: ['problem', 'answer', 'type'],
          additionalProperties: false // 确保没有多余字段
        }
      }
      const ajv = new Ajv()
      const validate = ajv.compile(schema)
      const valid = validate(data)

      // 处理缺失字段
      if (!valid) {
        console.log('数据缺失字段 (cleanJsonResponse):', validate.errors)
        data.forEach((item) => {
          validate.errors.forEach((error) => {
            if (error.keyword === 'required' && error.params.missingProperty && !item.hasOwnProperty(error.params.missingProperty)) {
              item[error.params.missingProperty] = null
            }
          })
        })
      }
      return data // 返回经过修复、解析、校验和处理的数据
    },
    // #region 针对对话的操作
    remove(val) {
      this.$confirm('确定要删除该条对话', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true,
        customClass: 'caseConfirm'
      })
        .then(() => {
          this.resultList = this.resultList.filter((item) => {
            if (item.problem !== val.problem && item.answer !== val.answer) {
              return item
            }
          })
        })
        .catch(() => {})
    },
    // 编辑
    edit(item, index) {
      this.editIndex = index
      this.userProblem = item.problem
      this.userAnswer = item.answer
      this.editDialog = true
    },
    saveEdit() {
      this.resultList[this.editIndex].problem = this.userProblem
      this.resultList[this.editIndex].answer = this.userAnswer
      this.editDialog = false
    },
    // 重新生成单轮对话
    // async againCreateQuestion(item) {
    //   item.problem = null
    //   item.answer = null
    //   this.againCreateQuestionAnimation()
    //   const chatRoleId = this.chatRoleList.find((chatRole) => chatRole.roleName === 'SP单条对话生成').chatRoleId
    //   const { data } = await chatRoleInfo(chatRoleId, this.apiHeaders)
    //   const promptTemplate = data.promptTemplate

    //   const msg = {
    //     message: [
    //       { messageType: 'SYSTEM', content: promptTemplate, hidden: true },
    //       { messageType: 'USER', content: JSON.stringify(item), hidden: true }
    //     ],
    //     // model: 'qwen2.5:7b',
    //     model: 'qwen2.5:14b',

    //     // model: 'zf-32b-Instruct:latest',
    //     chatId: this.appId + (Math.random() + new Date().getTime()).toString(32).slice(0, 8),
    //     appId: this.appId,
    //     maxTokens: this.appConfig.onceToken,
    //     numCtx: this.appConfig.contextToken
    //   }
    //   generateStream(msg, this.apiHeaders)
    //     .then(async (data) => {
    //       let val = ''
    //       data.forEach((item) => {
    //         val += item.result?.output?.content
    //       })
    //       console.log(val)

    //       // 解析 JSON 字符串为对象
    //       const patientInfo = await this.cleanJsonResponse(val)[0]
    //       if (this.ctx) {
    //         this.ctx.revert()
    //       }
    //       this.startTypewriting(patientInfo.problem, item, 'problem')
    //       this.startTypewriting(patientInfo.answer, item, 'answer')

    //       item.type = patientInfo.type
    //     })
    //     .catch((err) => {
    //       console.error(err.message)

    //       this.$message.error('对话生成格式出现错误，请重新生成。')
    //     })
    // },
    async againCreateQuestion(item) {
      item.problem = null
      item.answer = null
      this.againCreateQuestionAnimation()
      aiCall({
        type: 4,
        content: JSON.stringify(item),
        isStream: 2,
        model: window.config.VUE_AI_MODEL,
        chatModel: window.config.VUE_AI_CHAT_MODEL
      })
        .then(async (res) => {
          // 解析 JSON 字符串为对象
          const patientInfo = this.cleanJsonResponse(res.data)[0]
          console.log(patientInfo)

          if (this.ctx) {
            this.ctx.revert()
          }
          this.startTypewriting(patientInfo.problem, item, 'problem')
          this.startTypewriting(patientInfo.answer, item, 'answer')

          item.type = patientInfo.type
        })
        .catch((err) => {
          console.error(err.message)

          this.$message.error('对话生成格式出现错误，请重新生成。')
        })
    },
    // #endregion

    // 打字机效果
    startTypewriting(val, item, key) {
      let index = 0
      item[key] = ''
      const text = val.trim().split('')
      const timer = setInterval(() => {
        if (index >= text.length) {
          clearInterval(timer)
        } else {
          item[key] += text[index]
          index++
        }
      }, 20)
    },
    // 保存对话
    saveQuestion() {
      this.$refs['SaveQuestionDialogRef'].setList(this.resultList)
    },
    scrollBottom(id = 'dialogueContentParent') {
      let div = document.getElementById(id)
      if (div) {
        setTimeout(() => {
          div.scrollTop = div.scrollHeight
        }, 300)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  position: relative;
  display: flex;
  justify-content: center;
  height: 100vh;
  width: 100%;
  padding: 0;
  background: url('~@/assets/case/ai/bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  .goBackButton {
    position: absolute;
    left: 50px;
    top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    background: #f4f9ff;
    cursor: pointer;
    z-index: 999;
    img {
      width: 38px;
      height: 38px;
    }
    &:hover {
      background: #e7f0ff;
    }
  }
  .contentBox {
    .caseInfo {
      position: relative;
      width: 895px;
      height: 357px;
      margin-top: 80px;
      padding: 110px 40px 25px;
      background: url('~@/assets/case/ai/caseInfoBg.png') no-repeat;
      background-size: cover;
      .headerTitle {
        position: absolute;
        left: 15px;
        top: 40px;
        font-family: PingFang SC;
        font-size: 24px;
        color: #ffffff;
        line-height: 24px;
        &::after {
          content: '';
          position: absolute;
          left: -12px;
          top: 3px;
          width: 4px;
          height: 18px;
          background: #e4e4e4;
          border-radius: 2px;
        }
      }
      .infoBody {
        width: 100%;
        .patientsInfo {
          display: flex;
          justify-content: space-between;
          & > span {
            em {
              font-style: normal;
              color: #666666;
            }
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #000000;
          }
        }
        .mainDemands {
          width: 100%;
          height: 187px;
          margin-top: 10px;
          padding: 20px;
          background: #ffffff;
          border-radius: 20px;
          overflow: auto;
          & > span {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #666666;
          }
          & > p {
            margin: 0;
            margin-top: 12px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #000000;
            line-height: 30px;
          }
        }
      }
    }
    .createNumBox {
      position: relative;
      width: 910px;
      height: 312px;
      margin-top: 30px;
      margin-left: -17px;
      padding: 120px 70px 0;
      background: url('~@/assets/case/ai/createNumBg.png') no-repeat;
      background-size: cover;
      .header {
        position: absolute;
        left: 27px;
        top: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        img {
          margin-right: 6px;
        }
      }

      .createAlert {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 22px;
        color: #000000;
      }
      .createNumInput {
        margin-top: 20px;
        ::v-deep {
          .el-input__inner {
            width: 100%;
            height: 50px;
            border: none;
            background: #ffffff;
            border-radius: 10px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #000;
            &::placeholder {
              color: #999999;
            }
          }
        }
      }
      .startBtn {
        position: relative;
        width: 135px;
        height: 50px;
        margin: 0 auto;
        margin-top: 20px;
        background: linear-gradient(270deg, #1f5cff 0%, #9901ff 100%);
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
        border-radius: 55px 55px 55px 55px;
        font-family: PingFang SC;
        font-size: 20px;
        color: #ffffff;
        line-height: 50px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        &:hover {
          &::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
            width: 60px;
            height: 12px;
            background: #fff;
            filter: blur(10.5px);
          }
        }
      }
    }
    .createLoading {
      position: absolute;
      left: 50%;
      bottom: 35px;
      transform: translateX(-50%);
      width: 895px;
      .loadingText {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        & > span:first-of-type {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ababab;
        }
        & > span:last-of-type {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
        }
      }
      .loadingBox {
        display: flex;
        align-items: center;
        width: 100%;
        height: 23px;
        padding: 0 7px;
        margin-top: 7px;
        background: rgba(245, 245, 245, 0.2);
        box-shadow: inset 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
        border-radius: 72px 72px 72px 72px;
        overflow: hidden;
        .progressBar {
          flex-basis: 0;
          width: 100%;
          height: 15px;
          background: linear-gradient(270deg, #1f5cff 0%, #9901ff 100%);
          box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
          border-radius: 55px 55px 55px 55px;
        }
      }
    }
  }
  // #region 生成问题后整个页面格式改变
  .createOverLayout {
    display: flex;
    width: 100%;
    height: 100vh;
    padding: 30px 45px;
    .caseInfo {
      width: 445px;
      height: calc(100% - 40px);
      margin-top: 40px;
      background: url('~@/assets/case/ai/caseInfoBg_left.png') no-repeat;
      background-size: contain;
      .infoBody {
        height: 100%;
        .patientsInfo {
          flex-wrap: wrap;
          & > span {
            width: 33.33%;
          }
          & > .sex {
            text-align: center;
          }
          & > .age {
            text-align: right;
          }
          & > :nth-of-type(n + 4) {
            margin-top: 15px;
            width: 100%;
          }
        }
        & > .mainDemands {
          height: calc(100% - 115px);
        }
      }
    }
    .createNumBox {
      flex: 1;
      margin-left: 30px;
      height: 100%;
      margin-top: 0;
      padding: 110px 20px 20px 55px;
      background: url('~@/assets/case/ai/caseInfoBg_Right.png') no-repeat;
      background-size: contain;
      .createBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .createAlert {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 22px;
          color: #000000;
        }
        .createNumInput {
          width: 382px;
          height: 50px;
          margin: 0;
        }
        .startBtn {
          margin: 0;
        }
      }
      .resultBox {
        position: relative;
        height: calc(100% - 80px);
        width: 100%;
        margin-top: 32px;
        padding-top: 20px;

        .resultView {
          height: 550px;
          width: 100%;
          padding-right: 20px;
          overflow: auto;
          &::-webkit-scrollbar {
            background: #fff;
          }
          &::-webkit-scrollbar-thumb {
            background: #b0b0b0;
            border-radius: 5px;
          }
          .dialogueContent {
            margin-bottom: 15px;
            .ask,
            .reply {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              .operate {
                width: 95px;
                i {
                  font-size: 28px;
                  cursor: pointer;
                }
              }
              .askText {
                position: relative;
                margin-left: 14px;
                & > div:first-of-type {
                  min-width: 80px;
                  max-width: 100%;
                  padding: 10px 15px;
                  background: #2855ff;
                  border-radius: 15px 0 15px 15px;
                  font-family: PingFang SC;
                  font-weight: 500;
                  font-size: 18px;
                  color: #ffffff;
                  text-align: left;
                  line-height: 28px;
                }
                & > div:last-of-type {
                  position: absolute;
                  bottom: -25px;
                  right: 0;
                  display: flex;
                  align-items: center;
                  margin-top: 6px;
                  font-family: PingFang SC;
                  font-weight: 500;
                  font-size: 16px;
                  color: #000000;
                  span {
                    display: inline-block;
                    margin-right: 5px;
                    width: 16px;
                    height: 16px;
                    background: #ffb958;
                    border-radius: 4px 4px 4px 4px;
                  }
                }
                .qustionLoadingText {
                  i {
                    display: inline-block;
                    font-style: normal;
                    color: #fff;
                  }
                }
              }
              .photo {
                margin-left: 14px;
                img {
                  width: 60px;
                  height: 60px;
                  border-radius: 50%;
                  object-fit: scale-down;
                }
              }
            }

            .reply {
              width: 100%;
              justify-content: flex-start;
              margin-top: 15px;
              .askText {
                min-width: 80px;
                max-width: 80%;
                padding: 10px 15px;
                background: #e3e3e3;
                border-radius: 0 15px 15px 15px;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #333333;
                text-align: left;
                line-height: 28px;
                .qustionLoadingText {
                  i {
                    color: #333333;
                  }
                }
              }
            }
          }
        }
        .operateView {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 79px;
          padding: 0 20px;
          margin-top: 7px;
          background: #f4f4f4;
          border-radius: 20px 20px 20px 20px;
          border: 1px solid #eaeaea;
          //   transform: translateY(300px);
          & > div:first-of-type {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #666666;
            cursor: pointer;
            & > span:hover {
              color: #000;
            }
          }
          & > div:last-of-type {
            width: 135px;
            height: 50px;
            background: #2a54ff;
            box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
            border-radius: 55px 55px 55px 55px;
            font-family: PingFang SC;
            font-size: 20px;
            color: #ffffff;
            text-align: center;
            line-height: 50px;
            cursor: pointer;
            transition: all 0.3s;
            &:hover {
              background: linear-gradient(270deg, #1f5cff 0%, #9901ff 100%);
            }
          }
        }
      }
    }
    .createLoading {
      left: 50%;
      bottom: 50%;
      transform: translateX(-23%);
      .loadingText {
        & > span:last-of-type {
          color: #ababab;
        }
      }
    }
  }

  // #endregion

  ::v-deep {
    .editDialog {
      width: 1006px;
      height: 390px;
      background: #ffffff;
      border-radius: 20px;
      .el-dialog__header {
        padding: 32px 0 0 0;
        .el-dialog__title {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 26px;
          color: #000000;
        }
        .el-dialog__headerbtn {
          top: 10px;
          right: 15px;
          .el-dialog__close {
            font-size: 32px;
            color: #666666;
          }
        }
      }
      .el-dialog__body {
        padding: 45px 50px;
        .editDialog-content {
          & > div {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            label {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 24px;
              color: #666666;
            }
            .el-input {
              flex: 1;
              .el-input__inner {
                width: 100%;
                height: 60px;
                background: #efefef;
                border-radius: 6px;
                border: none;

                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24px;
                color: #000;
                &::placeholder {
                  color: #999999;
                }
              }
            }
          }
        }
      }
      .el-dialog__footer {
        & > div {
          & > .el-button {
            &:first-of-type,
            &:last-of-type {
              padding: 0;
              width: 93px;
              height: 50px;
              border-radius: 63px 63px 63px 63px;
              border: 1px solid #274e6a;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 20px;
              text-align: center;
              color: #274e6a;
              line-height: 50px;
              cursor: pointer;
              &:hover {
                background: rgba($color: #fff, $alpha: 0.8);
              }
            }
            &:last-of-type {
              width: 132px;
              margin-left: 12px;
              background: #274e6a;
              border: none;
              color: #ffffff;
              &:hover {
                background: rgba($color: #274e6a, $alpha: 0.8);
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.operateTooltip {
  font-size: 16px;
}
</style>
