<template>
  <div class="Chart_Rate" ref="ChartRate"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
export default {
  name: 'ChartRate',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  created() {},
  methods: {
    init(data) {
      const that = this
      var option = {
        title: {
          text: '班级及格率',
          left: '20px',
          top: '20px',
          textStyle: {
            color: '#333333',
            fontSize: 20
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            var data = params.data
            return `${data.name}<br/>参考人数：${data.value}人<br/>及格率：${data.rate}%`
          },
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // 提示框背景颜色为黑色的50%透明度
          borderColor: 'transparent',
          borderRadius: 10,
          textStyle: {
            color: '#FFFFFF', // 设置 tooltip 文字颜色为白色
            fontSize: 16
          }
        },
        series: [
          {
            name: '及格率',
            type: 'pie',
            radius: '121px',
            center: ['50%', '50%'],
            left: 'center',
            width: 500,
            color: ['#0091FF', '#13CABC', '#FFC02C', '#FF8D72'],
            data: data.map(function (item) {
              return {
                name: item.clbumName,
                value: item.testCount,
                rate: item.passRate,
                itemStyle: {
                  color: item.color // 确保颜色与数据项的颜色一致
                }
              }
            }),
            label: {
              alignTo: 'edge',
              edgeDistance: 10,
              lineHeight: 25,
              formatter: function (params) {
                // 这里使用ECharts的富文本进行配置
                return `{circle|●}{name| ${params.name}}\n{value| ${params.value}人,${params.percent}%}`
              },
              rich: {
                circle: {
                  fontSize: 15,
                  color: 'inherit', // 动态继承数据项的颜色
                  padding: [0, 0, 45, 0]
                },
                name: {
                  align: 'left',
                  fontSize: 14,
                  color: 'inherit', // 动态继承数据项的颜色
                  padding: [0, 3, 45, 0]
                },
                value: {
                  align: 'left',
                  fontSize: 16,
                  color: 'inherit', // 动态继承数据项的颜色
                  padding: [0, 0, 45, 0]
                }
              }
            },
            labelLine: {
              length: 15,
              length2: 0,
              maxSurfaceAngle: 80
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < that.chart.getWidth() / 2
              const points = params.labelLinePoints
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            }
          }
        ]
      }

      // 基于准备好的dom，初始化echarts实例并应用配置
      this.chart = echarts.init(this.$refs['ChartRate'])
      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.Chart_Rate {
  width: 100%;
  height: 100%;
}
</style>
