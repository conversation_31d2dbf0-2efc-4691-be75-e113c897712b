class WebSocketHeartbeat extends EventTarget {
  constructor(url) {
    super()
    this.socket = new WebSocket(url)
    this.heartbeatInterval = 10000
    this.heartbeatTimer = null

    this.socket.addEventListener('open', (event) => {
      console.log('socket连接成功.')
      this.startHeartBeat()
    })

    this.socket.addEventListener('message', (event) => {
      console.log('收到消息 ', event.data)
      // 派发自定义事件
      const messageEvent = new CustomEvent('socketMessage', { detail: event.data })
      this.dispatchEvent(messageEvent)
    })

    this.socket.addEventListener('close', (event) => {
      console.log('连接关闭.')
      this.stopHeartBeat()
    })

    this.socket.addEventListener('error', (event) => {
      console.error('连接出错:', event)
    })
  }

  sendHeartBeat() {
    if (this.socket.readyState === WebSocket.OPEN) {
      this.socket.send('success')
    }
  }

  startHeartBeat() {
    this.heartbeatTimer = setInterval(() => this.sendHeartBeat(), this.heartbeatInterval)
  }

  stopHeartBeat() {
    clearInterval(this.heartbeatTimer)
  }
}
export default WebSocketHeartbeat
