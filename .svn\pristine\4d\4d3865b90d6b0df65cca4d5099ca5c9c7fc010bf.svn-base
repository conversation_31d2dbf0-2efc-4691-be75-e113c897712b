import request from '@/utils/request'
import download from '@/utils/downloader.js'

//用户分页列表（根据学校id查询）
export function teacherList(data) {
  return request({
    url: '/system/teacher/teacherList',
    method: 'get',
    params: data
  })
}

//保存用户信息
export function teacherSave(data) {
  return request({
    url: '/system/teacher/teacherSave',
    method: 'post',
    data
  })
}
//更新用户信息
export function teacherUpdate(data) {
  return request({
    url: '/system/teacher/teacherUpdate',
    method: 'post',
    data
  })
}
//重置用户密码
export function teacherPwdReset(data) {
  return request({
    url: '/system/teacher/teacherPwdReset',
    method: 'put',
    params: data
  })
}
//删除用户
export function teacherRemove(data) {
  return request({
    url: '/system/teacher/teacherRemove',
    method: 'delete',
    params: data
  })
}
//批量导入用户(不选择管理的班级)
export function importTeacher(data) {
  return request({
    url: '/system/teacher/importTeacher',
    method: 'post',
    data
  })
}
//导出用户
export function teacherExport(data) {
  return download({
    url: '/system/teacher/teacherExport',
    method: 'post',
    data
  })
}
//修改启用/禁用状态
export function updateState(data) {
  return request({
    url: '/system/teacher/updateState',
    method: 'post',
    params: data
  })
}

//当前用户查看个人用户资料
export function selectTeacherById(data) {
  return request({
    url: '/system/teacher/selectTeacherById',
    method: 'get',
    params: data
  })
}
//当前用户修改密码
export function teacherUpdatePwd(data) {
  return request({
    url: '/system/teacher/teacherUpdatePwd',
    method: 'post',
    data
  })
}
//当前用户修改个人用户资料
export function updateTeacherDetail(data) {
  return request({
    url: '/system/teacher/updateTeacherDetail',
    method: 'post',
    data
  })
}
