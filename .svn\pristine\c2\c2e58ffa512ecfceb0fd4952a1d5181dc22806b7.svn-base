import request from '@/utils/request'

//组织机构树(根据学校id查询)
export function organizationTree(data) {
  return request({
    url: '/system/organization/organizationTree',
    method: 'get',
    params: data
  })
}
//新增组织机构
export function organizationSave(data) {
  return request({
    url: '/system/organization/organizationSave',
    method: 'post',
    data
  })
}
//修改组织机构
export function organizationUpdate(data) {
  return request({
    url: '/system/organization/organizationUpdate',
    method: 'post',
    data
  })
}
// 删除组织机构
export function organizationRemove(data) {
  return request({
    url: '/system/organization/organizationRemove',
    method: 'delete',
    params: data
  })
}
