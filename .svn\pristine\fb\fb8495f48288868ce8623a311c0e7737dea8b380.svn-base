<template>
  <div class="app-container">
    <el-row type="flex" justify="center" align="middle">
      <div class="goBackButton" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <span class="moduleTitle">病史采集</span>
    </el-row>
    <!-- 病例基础信息 -->
    <div class="caseInfo" v-if="caseInfo">
      <div>
        <div class="patientsInfo">
          <span class="caseType" :style="{ background: caseInfo.caseType === 1 ? '#47e33f' : '#ff7846' }">{{ caseInfo.caseType === 1 ? '学习病例' : '考核病例' }}</span>
          <span class="caseName">{{ caseInfo.name }}</span>
          <span class="patientsOther">{{ caseInfo.realName }}</span>
          <span class="patientsOther">{{ caseInfo.sex === 'F' ? '女' : '男' }}</span>
          <span class="patientsOther">{{ caseInfo.age }}岁</span>
        </div>
        <div class="caseMainDemands">
          <span>病例简介:</span>
          <p>{{ caseInfo.mainDemands ? caseInfo.mainDemands : '' }}</p>
        </div>
      </div>
      <div>
        <div>
          <span>{{ caseInfo.questionCount }}</span>
          <span>问题数量</span>
        </div>
        <div>
          <span>{{ caseInfo.allScore }}</span>
          <span>总分</span>
        </div>
      </div>
    </div>

    <div class="questionBox">
      <!-- 查询 -->
      <el-form ref="form" :model="queryInfo" label-width="100px" style="padding-top: 15px" inline>
        <!-- <el-form-item label="关键字:" label-width="80px">
          <el-input v-model="queryInfo.keyword" maxlength="40" size="small" placeholder="请输入关键字" clearable @clear="getDetails" @keydown.native.enter="getDetails"></el-input>
        </el-form-item> -->
        <el-form-item label="问诊属性:">
          <el-radio-group v-model="queryInfo.level" @change="getDetails">
            <el-radio :label="1">重要</el-radio>
            <el-radio :label="2">常规</el-radio>
            <el-radio :label="3">无效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="问诊类型:" style="margin-right: 0">
          <el-select v-model="queryInfo.typeId" placeholder="请选择问诊类型" @focus="getTypeList" @change="getDetails">
            <el-option v-for="item in typeList" :key="item.typeId" :value="item.typeId" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="btnBox">
            <span class="btn searchButton" @click="getDetails">查询</span>
            <span class="btn resetButton" @click="reset">重置</span>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="btnBox">
            <span class="btn questionAddButton" @click="addDialog = true">自定义添加</span>
            <span class="btn aiCreateButton" @click="aiCrateQuestion">
              <img src="@/assets/case/aiIcon.png" alt="" />
              AI创建对话
            </span>
            <span v-if="caseJudgeVoice" class="btn voiceCreateButton" @click="showBatchCreateVoiceDialog = true">批量语音生成</span>
            <span class="btn delsButton" @click="removes">批量删除</span>
            <span class="btn questionViewButton" @click="questionViewsDialog = true">问诊视图</span>
          </div>
        </el-form-item>
      </el-form>
      <!-- 列表 -->
      <el-table :data="list" style="width: 100%" header-cell-class-name="headerCellClass" cell-class-name="cellClass" @selection-change="selectionChange">
        <el-table-column align="center" type="selection"> </el-table-column>
        <el-table-column align="center" label="序号" width="60" type="index"> </el-table-column>
        <el-table-column align="center" prop="typeName" label="问诊类型" width="120"> </el-table-column>
        <el-table-column align="center" prop="level" label="问题属性" width="120">
          <template v-slot="{ row }">
            <span>{{ row.level | caseLevel }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="problem" label="问题" show-overflow-tooltip width="width"> </el-table-column>
        <!-- <el-table-column align="center" prop="keyword" label="关键字" width="width"> </el-table-column> -->
        <el-table-column align="center" prop="answer" label="回答" show-overflow-tooltip width="width"> </el-table-column>
        <el-table-column align="center" prop="answerFileUrl" label="语音状态" width="100">
          <template v-slot="{ row }">
            <i v-if="row.answerFileStatus" class="el-icon-loading"></i>
            <template v-else>
              <svg-icon v-if="row.answerFileUrl" class="playVoice" icon-class="sound" @click="playVoice(row)"></svg-icon>
              <span v-else>——</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="score" label="分数" width="60"> </el-table-column>
        <el-table-column align="center" label="操作" width="330">
          <template v-slot="{ row }">
            <div class="btns">
              <el-button v-if="caseJudgeVoice" size="small" plan type="success" class="createVoice" @click="createVoice(row)">
                <svg-icon icon-class="sound"></svg-icon>
                语音生成
              </el-button>
              <el-button size="small" type="primary" icon="el-icon-edit" class="editBtn" @click="edit(row)"> 编辑 </el-button>
              <el-button size="small" type="danger" icon="el-icon-delete" class="delBtn" @click="del(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="width: 100%; margin-top: 15px; text-align: center">
        <el-pagination background @current-change="getDetails" @size-change="getDetails" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="total, prev, pager, next" :total="total"> </el-pagination>
      </div>
    </div>
    <audio ref="audioRef" autoplay>
      <source :src="playUrl" type="audio/wav" />
      您的浏览器不支持 audio 标签。
    </audio>
    <!-- 新增问诊问题 -->
    <AddQuestionDialog ref="AddQuestionDialog" :addDialog.sync="addDialog" :questionNum="questionNum" :questionTotalScore="caseInfo.allScore" @success="getDetails" />
    <!-- 问诊试图 -->
    <QuestionViews :show-dialog.sync="questionViewsDialog" />
    <!-- 语音生成 -->
    <CreateVoiceDialog ref="CreateVoiceDialog" :show-dialog.sync="showBatchCreateVoiceDialog" @success="getDetails" />
  </div>
</template>
<script>
import { caseQuestionTypeList } from '@/api/caseQuestionType'
import { caseDetail, caseQuestionList, caseRemoveQuestionBatch, caseJudgeVoiceOpen, caseCreateVoice } from '@/api/case'
import addQuestionDialog from './addQuestion.vue'
import QuestionViews from '@/views/case/historyCollect/QuestionViews'
import CreateVoiceDialog from './CreateVoiceDialog.vue'
import webSocketHeartbeat from '@/utils/webSocketHeartbeat.js'

export default {
  name: '',
  components: {
    AddQuestionDialog: addQuestionDialog,
    QuestionViews,
    CreateVoiceDialog
  },
  data() {
    return {
      queryInfo: {
        typeId: null,
        level: null,
        keyword: null,
        pageNum: 1,
        pageSize: 10
      },
      typeList: [],
      caseInfo: {},
      list: [],
      checkedList: [], // 被勾选的问题
      total: 0,
      addDialog: false,
      questionNum: 0,
      questionViewsDialog: false,
      // socket
      socket: null,
      // 语音生成相关
      caseJudgeVoice: null,
      showBatchCreateVoiceDialog: false,
      playUrl: null
    }
  },
  created() {
    this.judgeVoice()
    this.getDetails()
    this.initSocket()
  },
  beforeDestroy() {
    console.log('Socket before closing:', this.socket)
    if (this.socket) {
      this.socket.socket.close()
    }
  },
  methods: {
    goBack() {
      this.$router.push('/case')
    },
    initSocket() {
      this.socket = new webSocketHeartbeat(window.config.VUE_WSS_BASE_API + this.$route.params.id)
      this.socket.addEventListener('socketMessage', (event) => {
        if (event.detail === 'success') return
        this.dataHandle(event.detail)
      })
    },
    dataHandle(json) {
      const data = JSON.parse(json)
      if ((data.rule === 'voice' || data.rule === 'voice_one') && data.caseId == this.$route.params.id) {
        this.list.forEach((item) => {
          if (item.questionId === data.questionId) {
            item.answerFileStatus = true
            item.answerFileUrl = data.answerFileUrl
            setTimeout(() => {
              item.answerFileStatus = false
            }, 1000)
          }
        })
      }
    },
    async getDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.caseInfo = data
      const { data: caseList } = await caseQuestionList({ caseId: this.$route.params.id, ...this.queryInfo })
      this.list = caseList.list
      this.list.forEach((item) => {
        this.$set(item, 'answerFileStatus', false)
      })
      this.total = caseList.total
      this.questionNum = caseList.total
    },
    async getTypeList() {
      const { data } = await caseQuestionTypeList()
      this.typeList = data.list
    },
    reset() {
      this.queryInfo = {
        typeId: null,
        level: null,
        keyword: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getDetails()
    },
    selectionChange(val) {
      this.checkedList = val
    },
    edit(row) {
      this.$refs['AddQuestionDialog'].showData(row)
      this.addDialog = true
    },
    del(row) {
      this.$confirm('确定要删除该问诊问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          console.log(row)
          await caseRemoveQuestionBatch({ ids: [row.questionId], caseId: this.$route.params.id })
          this.getDetails()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 批量删除
    removes() {
      if (!this.checkedList.length) return this.$message.warning('未选中问诊问题')
      this.$confirm('确定要删除被选中的问诊问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const ids = this.checkedList.map((item) => item.questionId)
          await caseRemoveQuestionBatch({ ids, caseId: this.$route.params.id })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    aiCrateQuestion() {
      this.$router.push(`/case/aiCreateQuestion/${this.$route.params.id}`)
    },
    // #region 语音生成相关
    // 判断是否开启语音生成，若开启说明学生端不使用实时语音
    async judgeVoice() {
      const { data } = await caseJudgeVoiceOpen()
      this.caseJudgeVoice = data
      console.log(data)
    },
    async createVoice(row) {
      if (this.caseInfo.refId) {
        await caseCreateVoice({ caseId: this.$route.params.id, questionId: row.questionId })
      } else {
        this.showBatchCreateVoiceDialog = true
        this.$refs['CreateVoiceDialog'].isOne = true
        this.$refs['CreateVoiceDialog'].questionId = row.questionId
      }
      console.log(this.caseInfo)
    },
    playVoice(item) {
      this.playUrl = window.config.VUE_FILE_BASE_PATH + item.answerFileUrl
      console.log(this.playUrl)

      this.$nextTick(() => {
        const audio = this.$refs['audioRef']
        audio.load()
      })
    }

    // #endregion
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100vh;
  padding: 30px 40px;
  background: #d2dbe5;
  & > .el-row {
    .goBackButton {
      position: absolute;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      background: #f4f9ff;
      cursor: pointer;
      img {
        width: 38px;
        height: 38px;
      }
      &:hover {
        background: #e7f0ff;
      }
    }
    .moduleTitle {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 35px;
      color: #293543;
      line-height: 35px;
      text-align: center;
    }
  }
  .caseInfo {
    display: flex;
    width: 100%;
    height: 127px;
    margin-top: 15px;
    padding: 22px 17px;
    background: #65849a;
    border-radius: 20px 20px 20px 20px;
    & > div:first-of-type {
      flex: 1;
      .patientsInfo {
        display: flex;
        align-items: center;
        .caseType {
          padding: 3px 5px;
          border-radius: 4px 4px 4px 4px;
          font-family: PingFang SC;
          font-size: 16px;
          color: #ffffff;
          text-align: center;
          line-height: 24px;
        }
        .caseName {
          margin-left: 10px;
          font-family: PingFang SC;
          font-size: 22px;
          color: #ffffff;
        }
        .patientsOther {
          margin-left: 10px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: rgba(255, 255, 255, 0.7);
        }
      }
      .caseMainDemands {
        display: flex;
        align-items: center;
        width: calc(100% - 30px);
        min-height: 40px;
        margin-top: 10px;
        padding: 0 15px;
        background: #7399b4;
        border-radius: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        color: rgba(255, 255, 255, 0.7);
        span {
          margin-right: 8px;
          color: #fff;
        }
        p {
          flex: 1;
          margin: 0;
        }
      }
    }
    & > div:last-of-type {
      position: relative;
      display: flex;
      justify-content: space-around;
      width: 230px;
      height: 83px;
      background: #bfd6e3;
      border-radius: 10px 10px 10px 10px;
      &::after {
        content: '';
        position: absolute;
        left: 55%;
        top: 55%;
        height: 33px;
        width: 1px;
        background: rgba($color: #000000, $alpha: 0.5);
        transform: translate(-50%, -50%);
      }
      & > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC;

        & > span:first-of-type {
          font-weight: bold;
          font-size: 26px;
          color: #293543;
        }
        & > span:last-of-type {
          margin-top: 4px;
          font-weight: 500;
          font-size: 14px;
          color: rgba($color: #293543, $alpha: 0.8);
        }
      }
    }
  }
  .questionBox {
    width: 100%;
    min-height: 500px;
    max-height: 688px;
    height: 660px;
    margin-top: 20px;
    padding: 0 25px;
    padding-bottom: 20px;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 30px;
    overflow: auto;
    .el-icon-loading {
      font-size: 30px;
      color: #46647a;
    }
    .playVoice {
      font-size: 24px;
      color: #3381b9;
      cursor: pointer;
    }
    .sound {
      font-size: 24px;
      color: #3381b9;
      cursor: pointer;
    }
    .btns {
      display: flex;
      align-items: center;
    }
    .createVoice,
    .editBtn,
    .delBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      padding: 0 10px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #e2e2e2;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #3381b9;
      line-height: 38px;
      cursor: pointer;
      transition: all 0.2s;
      .svgIcon {
        font-size: 16px;
      }
      &:hover {
        background: #65849a;
        color: #fff;
        border-color: transparent;
      }
    }
    ::v-deep {
      .el-form {
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        background: transparent;

        .el-form-item {
          display: flex;
          align-items: center;
          margin-bottom: 0;
          margin-right: 25px;
          &:last-of-type {
            margin-right: 0;
            margin-left: 30px;
          }
          .el-form-item__label {
            display: flex;
            align-items: center;

            height: 50px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #666;
          }
          .el-input {
            .el-input__inner {
              width: 190px;
              height: 45px;
              background: #eef0f2;
              border-radius: 74px;
              border: none;
              color: #333;
              font-weight: 500;
              font-size: 20px;
              font-family: PingFang SC;
              &::placeholder {
                color: #cccccc;
                font-weight: 500;
              }
            }
            .el-input__suffix {
              top: 3px;
              .el-input__suffix-inner {
                left: 10px;
                .el-icon-circle-close {
                  font-size: 24px;
                }
              }
            }
          }
          .el-select {
            .el-input__suffix {
              top: 0px;
              .el-input__suffix-inner {
                .el-select__caret {
                  font-size: 20px;
                }
              }
            }
          }

          .el-radio-group {
            display: flex;
            .el-radio {
              display: flex;
              align-items: center;
              margin-right: 15px;
              .el-radio__inner {
                width: 18px;
                height: 18px;

                background: #fff;
                border-color: #242e3b;
              }
              .el-radio__label {
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 500;
                font-size: 20px;
                color: #000;
              }
            }

            .el-radio__input.is-checked .el-radio__inner {
              &::after {
                background: #242e3b;
                width: 12px;
                height: 12px;
              }
            }
          }
          .btnBox {
            display: flex;
            align-items: center;
            .btn {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 18px;
              margin-left: 11px;
              height: 45px;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 16px;
              color: #ffffff;
              border-radius: 63px;
              cursor: pointer;
              transition: all 0.3s;
            }
            .searchButton {
              background: #65849a;
            }
            .resetButton {
              border: 1px solid #6287a1;
              color: #65849a;
            }
            .delsButton,
            .voiceCreateButton,
            .questionAddButton,
            .questionViewButton {
              background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
            }
            .aiCreateButton {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 13px;
              background: linear-gradient(97deg, #00eeff 0%, #0054ff 47%, #b700ff 100%);
              border-radius: 63px;
              font-size: 16px;
              img {
                margin-right: 8px;
              }
            }
          }
        }
      }
      .el-table::before {
        display: none;
      }
      .el-table__header-wrapper {
        margin-bottom: 4px;
        border-radius: 8px 8px 8px 8px;
        overflow: hidden;
      }

      .cellClass {
        height: 55px;
        border-bottom: solid 4px #fff;
        background: #f6f8fa;
        border-radius: 4px 4px 4px 4px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
      }
      .headerCellClass {
        height: 55px;
        border-bottom: none;
        background: #f6f8fa;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #999999;
      }

      // #region 分页样式
      .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #9ab0be;
        color: #eef0f2;
      }
      .el-pagination.is-background .el-pager li {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        background-color: #eef0f2;
        color: #65849a;
      }
      .el-pagination.is-background .btn-prev,
      .el-pagination.is-background .btn-next {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        background-color: #eef0f2;
        color: #65849a;
        font-size: 16px;
      }
      .el-pagination.is-background .btn-prev:disabled {
        color: #577183;
      }
      .el-pagination__total {
        height: 40px;
        line-height: 40px;
        color: #65849a;
        font-size: 15px;
      }
      // #endregion
    }
  }
}
</style>
<style lang="scss">
.el-select-dropdown__wrap {
  overflow-x: hidden;
  .el-select-dropdown__item {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 20px;
    color: #666666;
    transition: all 0.3s;
    &:hover {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #000000;
    }
  }
}
.el-tooltip__popper {
  font-size: 16px;
}
</style>
