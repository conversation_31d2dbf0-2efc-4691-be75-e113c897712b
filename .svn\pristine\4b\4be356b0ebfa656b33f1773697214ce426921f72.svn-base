formatDate // import parseTime, formatTime and set to filter
export function parseTime(time, cFormat) {
  if (!time || time.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1e18, symbol: 'E' },
    { value: 1e15, symbol: 'P' },
    { value: 1e12, symbol: 'T' },
    { value: 1e9, symbol: 'G' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value + 0.1).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}
export function tofixTime(second) {
  var durationEx = Number(second)
  var durationStr = ''
  if (!isNaN(durationEx)) {
    var durationHour = durationEx / 3600
    if (durationHour >= 1) {
      durationHour = Math.floor(durationHour)
      durationStr += durationHour + '小时'
    }
    var durationSecond = (durationEx % 3600) / 60
    if (durationSecond >= 1) {
      durationSecond = Math.floor(durationSecond)
      durationStr += durationSecond + '分钟'
    } else if (durationHour >= 1) {
      durationStr += '0分钟'
    }
    var durationMin = Math.floor((durationEx % 3600) % 60)
    durationStr += durationMin + '秒'

    return durationStr
  } else {
    return second
  }
}

export function formatTime(timeoriginal, option) {
  if (timeoriginal) {
    var time = timeoriginal.replace(/-/g, '/') // 时间格式转换
    time = time.replace(/T/g, ' ') // 时间格式转换
    var d = new Date(time)
    var nowreal = new Date()
    //	var now = new Date()
    //	var u = navigator.userAgent;
    //	var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
    //	var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    //	if(isiOS) {
    //		var offset = 8;
    //		//		var utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    //		var utc = now.getTime();
    //		nowreal = new Date(utc + (3600000 * offset));
    //	}
    const diff = (nowreal - d) / 1000

    if (diff < 30) {
      return '刚刚'
    } else if (diff < 3600) {
      // less 1 hour
      return Math.ceil(diff / 60) + '分钟前'
    } else if (diff < 3600 * 24) {
      return Math.ceil(diff / 3600) + '小时前'
    } else if (diff < 3600 * 24 * 2) {
      return '1天前'
    } else {
      var month = d.getMonth() + 1
      month = month < 10 ? '0' + month : month
      var day = d.getDate() < 10 ? '0' + d.getDate() : d.getDate()
      return month + '-' + day
    }
    if (option) {
      return parseTime(time, option)
    } else {
      return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
    }
  } else {
    return ''
  }
}

export function formatTimeLocal(timeoriginal, option) {
  if (timeoriginal) {
    var time = timeoriginal.replace(/-/g, '/')
    time = time.replace(/T/g, ' ')
    if (option) {
      return parseTime(time, option)
    } else {
      return parseTime(time)
    }
  } else {
    return timeoriginal
  }
}
export function spliceForSecond(timeoriginal) {
  if (timeoriginal) {
    var newnum = Number(timeoriginal)
    if (!isNaN(newnum)) {
      newnum = (newnum * 1000).toFixed(0)
      var oldtime = ''
      var hours = parseInt((newnum % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      hours = hours < 10 ? '0' + hours : hours
      if (hours != '00') {
        oldtime += hours
        oldtime += ':'
      }
      var minutes = parseInt((newnum % (1000 * 60 * 60)) / (1000 * 60))
      minutes = minutes < 10 ? '0' + minutes : minutes
      oldtime += minutes
      oldtime += ':'
      var seconds = (newnum % (1000 * 60)) / 1000
      seconds = seconds.toFixed(0)
      seconds = seconds < 10 ? '0' + seconds : seconds
      oldtime += seconds
      return oldtime
    } else {
      return timeoriginal
    }
  } else {
    if (timeoriginal == 0) {
      return '00:00'
    }
    return timeoriginal
  }
}

export function spliceQuestion(question) {
  if (question) {
    return question.split('@').join('___')
  } else {
    return question
  }
}
export function filesize(size) {
  if (size && size != '') {
    var file_size = size / 1024 / 1024
    file_size = file_size.toFixed(2)
    file_size += 'M'
    return file_size
  } else {
    return size
  }
}

export function formatDate(date, fmt = 'yyyy-MM-dd hh:mm') {
  if (!(date instanceof Array)) {
    date = new Date(date)
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = o[k] + ''
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
    }
  }
  return fmt
}
function padLeftZero(str) {
  return ('00' + str).substr(str.length)
}
export const questionTypes = [
  {
    name: '单选题',
    value: 'SINGLE'
  },
  {
    name: '多选题',
    value: 'MULTIPLE'
  }
  // {
  //   name: '判断题',
  //   value: 'JUDGE'
  // },
  // {
  //   name: '填空题',
  //   value: 'COMPLETION'
  // },
  // {
  //   name: '简答题',
  //   value: 'SHORT'
  // },
  // {
  //   name: '配伍题',
  //   value: 'COMPATIBILITY'
  // },
  // {
  //   name: '组合题',
  //   value: 'COMPREHENSIVE'
  // }
]

export const caseFormList = [
  {
    label: '呼吸系统',
    value: 1
  },
  {
    label: '循环系统',
    value: 2
  },
  {
    label: '消化系统',
    value: 3
  },
  {
    label: '泌尿系统',
    value: 4
  },
  {
    label: '内分泌系统',
    value: 5
  },
  {
    label: '风湿免疫系统',
    value: 6
  },
  {
    label: '血液系统',
    value: 7
  },
  {
    label: '理化因素疾病',
    value: 8
  }
]
// 考试状态
export function caseForm(state) {
  const states = [
    {
      label: '呼吸系统',
      value: 1
    },
    {
      label: '循环系统',
      value: 2
    },
    {
      label: '消化系统',
      value: 3
    },
    {
      label: '泌尿系统',
      value: 4
    },
    {
      label: '内分泌系统',
      value: 5
    },
    {
      label: '风湿免疫系统',
      value: 6
    },
    {
      label: '血液系统',
      value: 7
    },
    {
      label: '理化因素疾病',
      value: 8
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}

// 问题属性
export function caseLevel(state) {
  const states = [
    {
      label: '重要',
      value: 1
    },
    {
      label: '常规',
      value: 2
    },
    {
      label: '无效',
      value: 3
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}

// 考试状态
export function examState(state) {
  const states = [
    {
      label: '未开考',
      value: 1
    },
    {
      label: '进行中',
      value: 2
    },
    {
      label: '已结束',
      value: 3
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}

// 考试形式
export function examType(state) {
  const states = [
    {
      label: '随机全考',
      value: 1
    },
    {
      label: '随机抽考一个',
      value: 2
    },
    {
      label: '考一个',
      value: 3
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}
