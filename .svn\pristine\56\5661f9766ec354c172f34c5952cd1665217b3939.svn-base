.cont_main {
  width: 1500px;
  margin: 0 auto;
}

.index_cont {
  position: absolute;
  background: #ffffff;
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.other_cont {
  position: absolute;
  background: #ffffff;
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.index_item {
  height: 200px;
  width: 200px;
  display: inline-block;
  margin: 40px 20px 0;
  text-align: center;
  box-sizing: border-box;
  padding: 50px 0;
  line-height: 50px;
  font-size: 18;
}

.index_item i {
  font-size: 30px;
}

.top_label {
  color: rgb(32, 29, 29);
  border-bottom: 1px solid rgba(32, 30, 30, 0.432);
  line-height: 30px;
  font-size: 20px;
  padding: 20px;
}

.part_cont {
  background: #ffffff;
  height: auto;
  box-sizing: border-box;
  padding: 0 20px;
  margin: 0px 0 20px 30px;
  border-radius: 10px;
}

.list_cont {
  height: auto;
  box-sizing: border-box;
  margin: 0px 0 20px 30px;
  border-radius: 10px;
}

.userinfo {
  float: left;
  height: auto;
  width: auto;
  box-sizing: border-box;
  padding: 0 0 0 95px;
  position: relative;
  min-height: 200px;
  margin: 20px 0 0 0;
}

.userinfo img {
  width: 75px;
  position: absolute;
  left: 0;
  top: 5px;
}

.userinfoP1 {
  line-height: 35px;
}

.userinfoP2 {
  line-height: 25px;
  font-size: 14px;
  color: #999999;
}
.userinfo_course {
  padding: 0 0 0 140px;
  min-height: 100px;
}
.userinfo_course img {
  width: 120px;
  position: absolute;
  left: 0;
  top: 5px;
}
.userbase {
  float: right;
  height: 190px;
  width: 50%;
  float: right;
}

.all_box_p {
  padding: 20px 0;
}

.maincolor {
  color: #0093f3;
}

.nav_item {
  width: 120px;
  height: 54px;
  line-height: 54px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  position: relative;
  float: left;
}

.nav_item:hover,
.nav_item.active {
  color: #0093f3;
}

.nav_item.active:after {
  content: ' ';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  height: 2px;
  width: 30px;
  background: #0093f3;
}

.course_item {
  width: 100%;
  height: 120px;
  float: left;
  margin: 0 0 20px;
  background: #ffffff;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 20px 80px 20px 160px;
  position: relative;
}

.course_item img {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  height: 80px;
  width: 120px;
}

.course_itemP1 {
  width: 100%;
  line-height: 35px;
  font-size: 16px;
}

.course_itemP2 {
  width: 100%;
  line-height: 35px;
  font-size: 14px;
  color: #666666;
}

.course_itemP3 {
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 14px;
  color: #1eb0f9;
  text-align: center;
  position: absolute;
  right: 20px;
  top: 20px;
  cursor: pointer;
}

.no_data {
  height: 120px;
  line-height: 120px;
  text-align: center;
  font-size: 15px;
  color: #666666;
  background: #ffffff;
  border-radius: 10px;
  margin: 0 0 20px 0;
}

.teacher_info {
  width: 100%;
  height: 520px;
  position: relative;
}

.teacher_infoit {
  border-radius: 10px;
  overflow: hidden;
  position: absolute;
  width: calc(33.3% - 12px);
  height: calc(33.3% - 12px);
  box-sizing: border-box;
  padding: 20px 0 0 30px;
  color: #ffffff;
}

.teacher_infoleft1 {
  left: 0;
}

.teacher_infoleft2 {
  left: 33.3%;
  margin-left: 6px;
}

.teacher_infoleft3 {
  left: 66.6%;
  margin-left: 12px;
}

.teacher_infotop1 {
  top: 0;
}

.teacher_infotop2 {
  top: 33.3%;
  margin-top: 6px;
}

.teacher_infotop3 {
  top: 66.6%;
  margin-top: 12px;
}

.teacher_infoit1 {
  background: #67dac1;
}

.teacher_infoit2 {
  background: #fe9f7f;
}

.teacher_infoit3 {
  background: #fb7293;
}

.teacher_infoit4 {
  background: #46c8e8;
}

.teacher_infoit5 {
  background: #3b9aff;
  height: calc(66.6% - 5px);
}

.teacher_infoit6 {
  background: #d2aaf2;
}

.teacher_infoit7 {
  background: #65a4fc;
}

.teacher_infoit8 {
  background: #9d91f2;
}

.teacher_infoitP1 {
  height: 50px;
  line-height: 50px;
  font-size: 17px;
}

.teacher_infoitP2 {
  line-height: 48px;
  font-size: 36px;
  margin-top: 25px;
  width: 40%;
  float: left;
}

.teacher_infoitP2 span {
  font-size: 14px;
  margin-left: 6px;
  font-family: Microsoft YaHei;
}

.teacher_infoitP3 {
  width: 45%;
  height: auto;
  position: absolute;
  bottom: 20px;
  right: 20px;
  line-height: 26px;
  font-size: 13px;
  display: none;
}

.teacher_infoitP4 {
  line-height: 26px;
  font-size: 13px;
  text-align: center;
}

.teacher_infoit5 .teacher_infoitP2 {
  margin-top: 55px;
  width: 100%;
  text-align: center;
}

.data_part {
  height: auto;
  width: 100%;
  background: #ffffff;
  border-radius: 10px;
  padding: 0 20px 30px 20px;
  box-sizing: border-box;
  margin: 0 0 20px 0;
}

.data_title {
  height: auto;
  line-height: 24px;
  padding: 26px 26px 10px;
}

.data_title span {
  font-size: 12px;
  font-weight: normal;
  margin-left: 16px;
  color: #b2b2b2;
}

.data_label {
  line-height: 20px;
  font-size: 14px;
  color: #999;
  padding: 0 26px 10px;
}

.data_value {
  line-height: 40px;
  margin-top: 6px;
  font-size: 36px;
  padding: 0 26px 10px;
}

.data_value span {
  font-size: 16px;
  margin-left: 4px;
}

.echart_box {
  width: 1460px;
  height: 220px;
}
.echart_boxhelf {
  width: 550px;
  height: 220px;
}
.mainoperation {
  line-height: 20px;
  font-size: 16px;
  padding: 0 0 10px 0;
}
.clearfix:after {
  content: '\0020';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  zoom: 1;
}
