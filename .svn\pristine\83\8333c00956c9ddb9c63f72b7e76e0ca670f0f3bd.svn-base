<template>
  <div>
    <div class="top_info">
      <span class="info_label">试卷名称：</span>
      <span>{{ paperInfo.paperName }}</span>
      <span class="info_label">试题数量：</span>
      <span>{{ paperInfo.questionCount }}</span>
      <span class="info_label">实考人数：</span>
      <span>{{ paperInfo.realExamineUserCount }}</span>
      <span class="info_label">总人数：</span>
      <span>{{ paperInfo.totalCount }}</span>
      <br />
      <span class="info_label">试卷分数：</span>
      <span>{{ paperInfo.totalScore }}</span>
      <span class="info_label">及格分数：</span>
      <span>{{ paperInfo.passScore }}</span>
      <span class="info_label">考试时长：</span>
      <span>{{ paperInfo.duration }}分钟</span>
      <span class="info_label">待批改：</span>
      <span>{{ paperInfo.waitCorrectCount }}</span>
      <br />
      <span class="info_label">考试时间：</span>
      <span>{{ paperInfo.startTime }} 至 {{ paperInfo.endTime }}</span>
      <span class="info_label">批阅时间：</span>
      <span>{{ paperInfo.correctStartTime }} 至 {{ paperInfo.correctEndTime }}</span>
      <span class="info_label">成绩发布：</span>
      <span>{{ paperInfo.scorePublishTime }}</span>
      <span class="info_label">申诉截止：</span>
      <span>{{ paperInfo.complainEndTime }}</span>
    </div>
    <div style="margin: 20px">
      <span class="search_label">提交状态：</span>
      <el-select v-model="isFinish" placeholder="请选择提交状态" style="width: 200px" clearabled>
        <el-option v-for="item in isFinishs" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <span class="search_label">批改状态：</span>
      <el-select v-model="isReview" placeholder="请选择批改状态" style="width: 200px" clearabled>
        <el-option v-for="item in isReviews" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <el-button type="primary" @click="currentChange(1)" size="medium">查询</el-button>
      <el-button type="success" @click="exportUser" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出全部' }}</el-button>
    </div>
    <div style="margin: 15px">
      <el-table :data="exams" border row-key="correctId" @selection-change="selectionChange">
        <el-table-column type="selection" width="55" align="center"> </el-table-column>
        <el-table-column prop="loginName" align="center" label="学号"> </el-table-column>
        <el-table-column prop="name" align="center" label="姓名"> </el-table-column>
        <el-table-column prop="sex" align="center" label="性别">
          <template slot-scope="scope">
            <span v-for="item in sexs" v-if="scope.row.sex == item.value">{{ item.label }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="clbumName" align="center" label="班级"> </el-table-column>
        <el-table-column prop="isFinish" align="center" label="提交状态">
          <template slot-scope="scope">
            <span v-for="item in isFinishs" v-if="scope.row.isFinish == item.value">{{ item.label }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isReview" align="center" label="批改状态">
          <template slot-scope="scope">
            <span v-if="scope.row.isFinish == 1">
              <span v-if="scope.row.isComplain == 2">成绩申诉</span>
              <span v-else>
                <span v-for="item in isReviews" v-if="scope.row.isReview == item.value">{{ item.label }}</span>
              </span>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="paperScore" align="center" label="得分">
          <template slot-scope="scope">
            <span v-if="scope.row.isFinish == 1">
              <span v-if="scope.row.lastPaperScore">
                <span style="text-decoration: line-through">{{ scope.row.paperScore || 0 }}</span>
                &nbsp;
                <span>{{ scope.row.lastPaperScore || 0 }}分</span>
              </span>
              <span v-else>{{ scope.row.paperScore || 0 }}</span>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="duration" align="center" label="耗时（分钟）">
          <template slot-scope="scope">
            <span v-if="scope.row.isFinish == 1">
              {{ scope.row.duration }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="paperEndTime" align="center" label="提交时间">
          <template slot-scope="scope">
            <span v-if="scope.row.isFinish == 1">
              {{ scope.row.paperEndTime }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.isFinish == 1">
              <el-button type="primary" @click="openViewInfo(scope.row, 'edit')" size="mini" v-if="scope.row.isReview == 3">重新批改</el-button>
              <el-button type="primary" @click="openViewInfo(scope.row, 'edit')" size="mini" v-if="scope.row.isReview == 2 && getInTime(scope.row)">批改</el-button>
              <span v-if="scope.row.isReview == 2 && !getInTime(scope.row)">批改时间已过</span>
              <el-button type="primary" @click="openViewInfo(scope.row, 'view')" size="mini" v-if="scope.row.isReview == 1">查看</el-button>
              <!--<el-button type="success" @click="exportUserItem(scope.row)" size="mini">导出</el-button>-->
              <el-button type="danger" @click="openCorrigendum(scope.row)" size="mini" v-if="scope.row.isComplain == 2">成绩勘误</el-button>
            </div>
            <div v-else>未提交</div>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>
    <el-dialog title="成绩勘误" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="corrigendumDialog" width="550px" class="correct_complain">
      <el-form :model="addForm" ref="addForm">
        <el-form-item label="勘误原因" prop="complainReason">
          {{ addForm.complainReason }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="corrigendumReset">取 消</el-button>
        <el-button type="danger" @click="corrigendumReturn">勘误驳回</el-button>
        <el-button type="primary" @click="corrigendumReTry">重新批阅</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { paperRecordDetail, paperUserRecordList, rejectComplain, anewComplain, paperCorrectExport } from '@/api/correct.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    return {
      userinfo: {},
      paperId: this.$route.query.paperId,
      isFinish: null,
      isReview: null,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      exams: [],
      paperInfo: {},
      multipleSelect: [],
      corrigendumDialog: false,
      addForm: {},
      isReviews: [
        {
          label: '全部',
          value: null
        },
        {
          label: '批阅中',
          value: 2
        },
        {
          label: '已批阅',
          value: 1
        },
        {
          label: '勘误中',
          value: 3
        }
      ],
      isFinishs: [
        {
          label: '全部',
          value: null
        },
        {
          label: '未提交',
          value: 0
        },
        {
          label: '已提交',
          value: 1
        }
      ],
      sexs: [
        {
          value: 'M',
          label: '男'
        },
        {
          value: 'F',
          label: '女'
        }
      ]
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getInTime(item) {
      var nowDate = new Date()
      var endTime = new Date(this.paperInfo.correctEndTime)
      if (nowDate < endTime) {
        return true
      } else {
        return false
      }
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        paperId: this.paperId,
        isFinish: this.isFinish,
        isReview: this.isReview
      }
      paperUserRecordList(data).then(async (res) => {
        this.exams = res.data.list
        this.total = res.data.total
      })
    },
    getPaperInfo() {
      paperRecordDetail({
        paperId: this.paperId
      }).then((res) => {
        this.paperInfo = res.data
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.getGroups()
        this.getPaperInfo()
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    exportUser() {
      var multipleSelect = this.multipleSelect
      var paperUserRecordIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          paperUserRecordIds.push(item.paperUserRecordId)
        })
      }
      paperCorrectExport({
        paperId: this.paperId,
        paperUserRecordIds: paperUserRecordIds && paperUserRecordIds.length > 0 ? paperUserRecordIds : null
      })
    },
    exportUserItem(item) {
      paperCorrectExport({
        paperId: this.paperId,
        paperUserRecordIds: [item.paperUserRecordId]
      })
    },
    openViewInfo(item, type) {
      this.$router.replace({
        name: 'correctinfo',
        query: {
          paperUserRecordId: item.paperUserRecordId,
          type: type
        }
      })
    },
    openCorrigendum(item) {
      this.addForm = Object.assign({}, item)
      this.corrigendumDialog = true
    },
    corrigendumReset() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.corrigendumDialog = false
    },
    corrigendumReTry() {
      anewComplain({
        paperUserRecordId: this.addForm.paperUserRecordId
      }).then((res) => {
        this.corrigendumReset()
        if (res.code == '200') {
          this.openViewInfo(this.addForm, 'edit')
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    corrigendumReturn() {
      rejectComplain({
        paperUserRecordId: this.addForm.paperUserRecordId
      }).then((res) => {
        this.corrigendumReset()
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: '已驳回！'
          })
          this.currentChange(1)
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    }
  }
}
</script>
