<template>
  <el-row :gutter="20">
    <el-col :span="4">
      <div class="user_left">
        <div class="user_lable">选择专业</div>
        <div class="user_leftitem" v-for="(item, index) in majors" :class="{ active: item.majorId == majorId }" @click="selectItem(item)">
          {{ item.majorName }}
        </div>
      </div>
    </el-col>
    <el-col :span="20" v-if="majorId">
      <div class="user_right">
        <div class="user_alert">提示：配置的模拟规则，适用于该专业全部模拟考试</div>
        <div class="simulation_cont">
          <el-form :model="addform" :rules="rules" ref="addform" class="clearfix">
            <el-row>
              <el-col :span="10">
                <el-form-item label="难度：" :label-width="labelwidth" prop="difficulty">
                  <el-radio-group v-model="addform.difficulty" @change="diffChange">
                    <el-radio :label="item.value" v-for="item in difficultys">{{ item.name }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="考试时长：" :label-width="labelwidth" prop="duration">
                  <el-input-number v-model="addform.duration" placeholder="请输入考试时长" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                  分钟
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="单选题数量：" :label-width="labelwidth" prop="singleNum">
                  <el-input-number v-model="addform.singleNum" placeholder="请输入单选题数量" :min="0" :max="numbers.singleNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.singleNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每题分数：" :label-width="labelwidth" prop="singleScore">
                  <el-input-number v-model="addform.singleScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="多选题数量：" :label-width="labelwidth" prop="multipleNum">
                  <el-input-number v-model="addform.multipleNum" placeholder="请输入多选题数量" :min="0" :max="numbers.multipleNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.multipleNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每题分数：" :label-width="labelwidth" prop="multipleScore">
                  <el-input-number v-model="addform.multipleScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="判断题数量：" :label-width="labelwidth" prop="judgeNum">
                  <el-input-number v-model="addform.judgeNum" placeholder="请输入判断题数量" :min="0" :max="numbers.judgeNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.judgeNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每题分数：" :label-width="labelwidth" prop="judgeScore">
                  <el-input-number v-model="addform.judgeScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="填空题数量：" :label-width="labelwidth" prop="completionNum">
                  <el-input-number v-model="addform.completionNum" placeholder="请输入填空题数量" :min="0" :max="numbers.completionNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.completionNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每题分数：" :label-width="labelwidth" prop="completionScore">
                  <el-input-number v-model="addform.completionScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="简答题数量：" :label-width="labelwidth" prop="shortNum">
                  <el-input-number v-model="addform.shortNum" placeholder="请输入配伍题数量" :min="0" :max="numbers.shortNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.shortNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每题分数：" :label-width="labelwidth" prop="shortScore">
                  <el-input-number v-model="addform.shortScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="配伍题数量：" :label-width="labelwidth" prop="compatibilityNum">
                  <el-input-number v-model="addform.compatibilityNum" placeholder="请输入配伍题数量" :min="0" :max="numbers.compatibilityNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.compatibilityNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每小题分数：" :label-width="labelwidth" prop="compatibilityScore">
                  <el-input-number v-model="addform.compatibilityScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="组合题数量：" :label-width="labelwidth" prop="comprehensiveNum">
                  <el-input-number v-model="addform.comprehensiveNum" placeholder="请输入组合题数量" :min="0" :max="numbers.comprehensiveNum" :step="1" step-strictly></el-input-number>
                  &nbsp;&nbsp;共<span class="maincolor">{{ numbers.comprehensiveNum }}</span
                  >道题
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="每小题分数：" :label-width="labelwidth" prop="comprehensiveScore">
                  <el-input-number v-model="addform.comprehensiveScore" placeholder="请输入每题分数" :min="0" :max="99" :step="1" step-strictly></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="simulation_all">
              模拟试卷总分：<span class="maincolor">{{ getAllScore }}</span
              >，总数量：<span class="maincolor">{{ getAllNumber }}</span>
            </div>
            <div class="simulation_info" style="color: red">配伍题和组合题在学生抽题生成模拟考试后生成具体分数</div>
            <div class="simulation_info">
              已选：单选题<span class="maincolor">{{ getNum(addform.singleNum) }}</span
              >道、 多选题 <span class="maincolor">{{ getNum(addform.multipleNum) }}</span
              >道、 判断题 <span class="maincolor">{{ getNum(addform.judgeNum) }}</span
              >道、 填空题 <span class="maincolor">{{ getNum(addform.completionNum) }}</span
              >道、 填空题 <span class="maincolor">{{ getNum(addform.shortNum) }}</span
              >道、 配伍题 <span class="maincolor">{{ getNum(addform.compatibilityNum) }}</span
              >道、 组合题 <span class="maincolor">{{ getNum(addform.comprehensiveNum) }}</span
              >道
            </div>
            <div class="simulation_info">
              分值：单选题<span class="maincolor">{{ getNum(addform.singleScore) }}</span
              >分每道（总分：<span class="maincolor">{{ getSum(addform.singleNum, addform.singleScore) }}</span
              >分）、 多选题 <span class="maincolor">{{ getNum(addform.multipleScore) }}</span
              >分每道（总分：<span class="maincolor">{{ getSum(addform.multipleNum, addform.multipleScore) }}</span
              >分）、 判断题 <span class="maincolor">{{ getNum(addform.judgeScore) }}</span
              >分每道（总分：<span class="maincolor">{{ getSum(addform.judgeNum, addform.judgeScore) }}</span
              >分）、 填空题 <span class="maincolor">{{ getNum(addform.completionScore) }}</span
              >分每道（总分：<span class="maincolor">{{ getSum(addform.completionNum, addform.completionScore) }}</span
              >分）、 简答题 <span class="maincolor">{{ getNum(addform.compatibilityScore) }}</span
              >分每道（总分：<span class="maincolor">{{ getSum(addform.shortNum, addform.shortScore) }}</span
              >分）
            </div>
            <div style="text-align: center">
              <el-button type="primary" @click="checkForm">保存</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import { selectSimulateParamList, updateSimulateParam, saveSimulateParam } from '@/api/param.js'
import { selectQuestionCountByType } from '@/api/question.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        if (value == 0) {
          return callback()
        } else {
          return callback(new Error('请填写数字'))
        }
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    return {
      code: 'paper_rules',
      userinfo: {},
      majors: [],
      majorId: '',
      labelwidth: '110px',
      numbers: {},
      difficultys: [
        {
          name: '随机',
          value: 'STOCHASTIC'
        },
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes: [
        {
          name: '单选题',
          value: 'SINGLE'
        },
        {
          name: '多选题',
          value: 'MULTIPLE'
        },
        {
          name: '判断题',
          value: 'JUDGE'
        },
        {
          name: '填空题',
          value: 'COMPLETION'
        },
        {
          name: '简答题',
          value: 'SHORT'
        },
        {
          name: '配伍题',
          value: 'COMPATIBILITY'
        },
        {
          name: '组合题',
          value: 'COMPREHENSIVE'
        }
      ],
      isEdited: false,
      addform: {
        difficulty: 'STOCHASTIC',
        duration: '',
        singleNum: '',
        singleScore: '',
        multipleNum: '',
        multipleScore: '',
        judgeNum: '',
        judgeScore: '',
        completionNum: '',
        completionScore: '',
        shortNum: '',
        shortScore: '',
        compatibilityNum: '',
        compatibilityScore: '',
        comprehensiveNum: '',
        comprehensiveScore: ''
      },
      rules: {
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        duration: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入时长'
          }
        ],
        singleNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        singleScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        multipleNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        multipleScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        judgeNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        judgeScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        completionNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        completionScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        shortNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        shortScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        compatibilityNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        compatibilityScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        comprehensiveNum: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ],
        comprehensiveScore: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入数字'
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    getAllScore() {
      var allScore = this.getSum(this.addform.singleNum, this.addform.singleScore) + this.getSum(this.addform.multipleNum, this.addform.multipleScore) + this.getSum(this.addform.judgeNum, this.addform.judgeScore) + this.getSum(this.addform.completionNum, this.addform.completionScore) + this.getSum(this.addform.shortNum, this.addform.shortScore)
      return allScore
    },
    getAllNumber() {
      return this.getNum(this.addform.singleNum) + this.getNum(this.addform.multipleNum) + this.getNum(this.addform.judgeNum) + this.getNum(this.addform.completionNum) + this.getNum(this.addform.shortNum)
    }
  },
  methods: {
    checkForm() {
      var that = this
      this.$refs.addform.validate((valid) => {
        if (valid) {
          var addform = this.addform
          var paramReqs = []
          for (let i in addform) {
            var data = {
              code: i,
              value: addform[i],
              paramName: addform[i + 'name'],
              paramId: that.isEdited ? addform[i + 'id'] : null,
              majorId: that.majorId,
              schoolId: that.userinfo.schoolId
            }
            if (addform[i + 'name']) {
              paramReqs.push(data)
            }
          }
          if (this.isEdited) {
            updateSimulateParam(paramReqs).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.getDataInfo()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          } else {
            saveSimulateParam({
              paramName: '模拟考试配卷规则_' + that.userinfo.schoolName + '_' + that.majorName,
              code: 'simulate_rule',
              value: 'simulate_rule',
              parentId: '0',
              majorId: that.majorId,
              schoolId: that.userinfo.schoolId,
              paramReqs: paramReqs
            }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.getDataInfo()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          }
          console.log(paramReqs)
          return
        }
      })
    },
    getNum(num) {
      var getNum = num
      if (!num) {
        getNum = 0
      } else if (isNaN(Number(num))) {
        getNum = 0
      } else if (!Number.isInteger(Number(num))) {
        getNum = 0
      }
      return Number(getNum)
    },
    getSum(num, score) {
      var getNum = num
      var getScore = score
      if (!num) {
        getNum = 0
      } else if (isNaN(Number(num))) {
        getNum = 0
      } else if (!Number.isInteger(Number(num))) {
        getNum = 0
      }
      if (!score) {
        getScore = 0
      } else if (isNaN(Number(score))) {
        getScore = 0
      } else if (!Number.isInteger(Number(score))) {
        getScore = 0
      }

      return Number(getNum * score)
    },
    selectItem(item) {
      if (this.majorId != item.majorId) {
        this.$refs.addform.resetFields()
        this.$refs.addform.clearValidate()
        this.majorId = item.majorId
        this.majorName = item.majorName
        this.getDataInfo()
        this.$nextTick(() => {
          this.$refs.addform.clearValidate()
        })
      }
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (res.data.majors && res.data.majors.length > 0) {
          this.majorId = res.data.majors[0].majorId
          this.majorName = res.data.majors[0].majorName
          this.getDataInfo()
        }
      })
    },
    diffChange() {
      selectQuestionCountByType({
        majorId: this.majorId,
        schoolId: this.userinfo.schoolId,
        difficulty: this.addform.difficulty,
        questionUse: 1
      }).then((res) => {
        this.numbers = res.data
        this.addform.singleNum = this.addform.singleNum && this.addform.singleNum > this.numbers.singleNum ? this.numbers.singleNum : this.addform.singleNum
        this.addform.multipleNum = this.addform.multipleNum && this.addform.multipleNum > this.numbers.multipleNum ? this.numbers.multipleNum : this.addform.multipleNum
        this.addform.judgeNum = this.addform.judgeNum && this.addform.judgeNum > this.numbers.judgeNum ? this.numbers.judgeNum : this.addform.judgeNum
        this.addform.completionNum = this.addform.completionNum && this.addform.completionNum > this.numbers.completionNum ? this.numbers.completionNum : this.addform.completionNum
        this.addform.shortNum = this.addform.shortNum && this.addform.shortNum > this.numbers.shortNum ? this.numbers.shortNum : this.addform.shortNum
        this.addform.compatibilityNum = this.addform.compatibilityNum && this.addform.compatibilityNum > this.numbers.compatibilityNum ? this.numbers.compatibilityNum : this.addform.compatibilityNum
        this.addform.comprehensiveNum = this.addform.comprehensiveNum && this.addform.comprehensiveNum > this.numbers.comprehensiveNum ? this.numbers.comprehensiveNum : this.addform.comprehensiveNum
      })
    },
    getDataInfo() {
      selectQuestionCountByType({
        majorId: this.majorId,
        schoolId: this.userinfo.schoolId,
        difficulty: this.addform.difficulty,
        questionUse: 1
      }).then((res) => {
        this.numbers = res.data
        this.isEdited = false
        var majorId = this.majorId
        var schoolId = this.userinfo.schoolId
        selectSimulateParamList({
          majorId,
          schoolId,
          code: 'simulate_rule'
        }).then((res) => {
          if (res.data) {
            res.data.map((item) => {
              this.addform[item.code] = item.value
              this.addform[item.code + 'id'] = item.paramId
              this.addform[item.code + 'name'] = item.paramName
              if (item.schoolId == schoolId && item.majorId == majorId) {
                this.isEdited = true
              }
            })
          }
        })
      })
    }
  }
}
</script>
