<template>
  <div>
    <div class="topic_des">
      难度：<span v-for="item in difficultys" v-if="item.value == form.difficulty">{{ item.name }}</span>
    </div>
    <div class="topic_question editor_box" v-html="form.question"></div>
    <div v-if="splited">
      <div v-for="(item, index) in form.list">
        <div class="topic_question">
          <span class="editor_box" v-html="item.question"></span>
        </div>
        <div class="topic_selectionit" v-for="(optionItem, index) in item.optionsArr">
          <span class="work-selectionlabel">{{ letters[index] }}、</span>
          <span class="editor_box" v-html="optionItem"></span>
        </div>
        <div class="topic_answermr topic_answer">正确答案：<span class="editor_box" v-html="item.answer"></span></div>
        <div class="topic_answermr topic_answer">解析：<span class="editor_box" v-html="item.analysis"></span></div>
      </div>
    </div>
    <el-tabs v-model="activeName" class="topic_answermr topic_selection" v-else>
      <el-tab-pane :label="'问题' + (index + 1)" :name="'' + (index + 1)" v-for="(item, index) in form.list">
        <div class="topic_question">
          <span class="work-selectionlabel">{{ index + 1 }}、</span>
          <span class="editor_box" v-html="item.question"></span>
        </div>
        <div class="topic_selectionit" v-for="(optionItem, index) in item.optionsArr">
          <span class="work-selectionlabel">{{ letters[index] }}、</span>
          <span class="editor_box" v-html="optionItem"></span>
        </div>
        <div class="topic_answermr topic_answer">正确答案：<span class="editor_box" v-html="item.answer"></span></div>
        <div class="topic_answermr topic_answer">解析：<span class="editor_box" v-html="item.analysis"></span></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      required: true
    },
    letters: {
      type: Array,
      required: true
    },
    difficultys: {
      type: Array,
      required: true
    },
    splited: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      activeName: '1'
    }
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      var form = this.form
      if (form.list && form.list.length > 0) {
        form.list.map((item) => {
          if (item.options) {
            var optionObject = JSON.parse(item.options)
            item.optionObject = optionObject
            item.optionsArr = Object.values(optionObject)
          } else {
            item.optionObject = {}
            item.optionsArr = []
          }
        })
      } else {
        form.list = []
      }
    }
  }
}
</script>
