<template>
  <div>
    <div style="margin: 20px">
      <span class="search_label">考试名称：</span>
      <el-input placeholder="请输入考试名称" width="30px" v-model="paperName" style="width: 200px; margin-left: 5px" clearable />
      <!-- <span class="search_label">创建人：</span> -->
      <!-- <el-input placeholder="请输入创建人" width="30px" v-model="createUserName" style="width: 200px; margin-left: 5px" clearable /> -->
      <!-- <span class="search_label">批改状态：</span>
      <el-select v-model="correctState" placeholder="请选择批改状态" style="width: 200px" clearabled>
        <el-option v-for="item in correctStates" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select> -->
      <!-- <span class="search_label">创建时间：</span>
      <el-date-picker v-model="createTimes" @change="createTimesChange" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" clearabled></el-date-picker> -->
      <span class="search_label">考试时间：</span>
      <el-date-picker v-model="examineTimes" @change="examineTimesChange" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" clearabled></el-date-picker>
      <el-button type="primary" @click="currentChange(1)" size="medium">查询</el-button>
    </div>
    <div style="margin: 15px">
      <el-table :data="exams" border>
        <el-table-column prop="paperName" align="center" label="考试名称"> </el-table-column>
        <el-table-column prop="clbums" align="center" label="下发班级">
          <template slot-scope="scope">
            <div v-if="scope.row.clbums">
              <div v-for="item in scope.row.clbums" :key="item.id" style="white-space: nowrap">【{{ item.clbumName }}】</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="" align="center" label="考试时间">
          <template slot-scope="scope">
            {{ scope.row.startTime }}
            <br />
            至
            <br />
            {{ scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCount" align="center" label="总人数"> </el-table-column>
        <el-table-column prop="totalScore" align="center" label="总分数"> </el-table-column>
        <el-table-column prop="passScore" align="center" label="及格分"> </el-table-column>
        <!-- <el-table-column prop="waitCorrectCount" align="center" label="待批改"> </el-table-column> -->

        <!-- <el-table-column prop="correctState" align="center" label="批改状态">
          <template slot-scope="scope">
            <span v-for="item in correctStates" v-if="scope.row.correctState == item.value">{{ item.label }}</span>
          </template>
        </el-table-column> -->

        <!-- <el-table-column prop="createUserName" align="center" label="创建人"> </el-table-column> -->
        <!-- <el-table-column prop="createTime" align="center" label="创建时间"> </el-table-column> -->
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <span v-if="getInTime(scope.row)">考试未结束</span>
            <el-button v-else type="primary" @click="openViewInfo(scope.row)" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-size="pageSize" layout="total,  prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { paperCorrectList } from '@/api/correct.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    return {
      userinfo: {},
      paperName: '',
      createUserName: '',
      createTimes: [],
      startCreateTime: '',
      endCreateTime: '',
      examineTimes: [],
      startExamineTime: '',
      endExamineTime: '',
      correctState: null,
      pageNum: 1,
      pageSize: 8,
      total: 0,
      exams: [],
      correctStates: [
        {
          label: '全部',
          value: null
        },
        {
          label: '未完成',
          value: 2
        },
        {
          label: '已完成',
          value: 1
        }
      ]
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getInTime(item) {
      var nowDate = new Date()
      var endTime = new Date(item.endTime)
      if (nowDate < endTime) {
        return true
      } else {
        return false
      }
    },
    createTimesChange() {
      if (this.createTimes && this.createTimes.length == 2) {
        this.startCreateTime = this.createTimes[0]
        this.endCreateTime = this.createTimes[1]
      } else {
        this.startCreateTime = ''
        this.endCreateTime = ''
      }
    },
    examineTimesChange() {
      if (this.examineTimes && this.examineTimes.length == 2) {
        this.startExamineTime = this.examineTimes[0]
        this.endExamineTime = this.examineTimes[1]
      } else {
        this.startExamineTime = ''
        this.endExamineTime = ''
      }
      this.getGroups()
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        paperName: this.paperName,
        createUserName: this.createUserName,
        startCreateTime: this.startCreateTime,
        endCreateTime: this.endCreateTime,
        startExamineTime: this.startExamineTime,
        endExamineTime: this.endExamineTime,
        correctState: this.correctState,
        schoolId: this.userinfo.schoolId
      }
      paperCorrectList(data).then(async (res) => {
        this.exams = res.data.list
        this.total = res.data.total
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.getGroups()
      })
    },
    openViewInfo(item) {
      this.$router.replace({
        name: 'correctlist',
        query: {
          paperId: item.paperId
        }
      })
    }
  }
}
</script>
