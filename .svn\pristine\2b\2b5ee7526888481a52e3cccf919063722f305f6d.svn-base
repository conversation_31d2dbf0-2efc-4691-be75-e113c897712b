<template>
  <div class="app-container">
    <div class="details_header">
      <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
      <div class="title">{{ $route.params.name }}</div>
    </div>
    <div class="details_body">
      <div class="body_top" v-if="caseInfo">
        <div class="caseInfo">
          <div class="case_user">
            <Avatar :age="caseInfo.age" :sex="caseInfo.sex" />
            <div class="userInfo">
              <span>{{ caseInfo.name }}</span>
              <span>{{ caseInfo.age }}岁</span>
            </div>
          </div>
          <div class="statistics_item boderItem">
            <span>总分 <i>(分)</i></span>
            <span>{{ caseInfo.allScore }}</span>
          </div>
          <div class="statistics_item">
            <span>问题数量 <i>(个)</i></span>
            <span>{{ caseInfo.questionCount ? caseInfo.questionCount : 0 }}</span>
          </div>
        </div>
        <div class="statistics">
          <div>
            <div class="statistics_item">
              <span>训练总用时 <i>(分)</i></span>
              <span> {{ caseInfo.allTime ? parseInt(caseInfo.allTime / 60) : 0 }}</span>
            </div>
            <div class="statistics_item boderItem">
              <span>操作总次数 <i>(次)</i></span>
              <span>{{ caseInfo.count ? caseInfo.count : 0 }}</span>
            </div>
            <div class="statistics_item">
              <span>训练人数<i>(人)</i></span>
              <span> {{ caseInfo.allStudentCount ? caseInfo.allStudentCount : 0 }}</span>
            </div>
          </div>
        </div>
        <div class="statistics">
          <div>
            <div class="statistics_item">
              <span>最高得分 <i>(分)</i></span>
              <span> {{ caseInfo.maxScore ? caseInfo.maxScore : 0 }}</span>
            </div>
            <div class="statistics_item boderItem">
              <span>平均得分 <i>(分)</i></span>
              <span>{{ caseInfo.avgScore ? caseInfo.avgScore : 0 }}</span>
            </div>
            <div class="statistics_item">
              <span>平均正确率<i>(%)</i></span>
              <span> {{ caseInfo.avgRate ? caseInfo.avgRate : 0 }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <el-row>
          <el-form ref="form" :model="queryInfo" label-width="95px" inline>
            <el-form-item label="学号:" label-width="60px">
              <el-input v-model="queryInfo.loginName" placeholder="学号" @keydown.native.enter="getStudentList" clearable @clear="getStudentList"></el-input>
            </el-form-item>
            <el-form-item label="所在班级:">
              <el-select v-model="queryInfo.clbumId" placeholder="请选择所在班级" @change="getStudentList">
                <el-option v-for="item in classList" :key="item.id" :label="item.clbumName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间:">
              <el-date-picker v-model="time" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" prefix-icon="el-icon-date" @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <el-form-item style="float: right">
              <el-button type="primary" @click="getStudentList">查询</el-button>
              <el-button type="primary" plain @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-table :data="list" style="width: 100%" stripe :header-cell-style="{ background: '#f4f7ff', color: '#333333', fontWeight: 400 }">
          <el-table-column prop="studentName" label="学生姓名" width="width" align="center"> </el-table-column>
          <el-table-column prop="prop" label="性别" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.sex === 'F' ? '女' : '男' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="loginName" label="学号" width="width" align="center"> </el-table-column>
          <el-table-column prop="clbumName" label="所在班级" width="width" align="center"> </el-table-column>
          <el-table-column prop="allCount" label="训练总次数" width="width" align="center"> </el-table-column>
          <el-table-column prop="allCount" label="平均得分" width="width" align="center"> </el-table-column>
          <el-table-column prop="avgRate" label="平均正确率" width="width" align="center"> </el-table-column>
          <el-table-column prop="avgTime" label="平均用时" width="width" align="center"> </el-table-column>
          <el-table-column prop="endTime" label="最近一次训练时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="prop" label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="details(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination style="text-align: center; margin-top: 15px" background :current-page.sync="queryInfo.pageNum" :page-sizes="[10, 15, 20, 30]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="getStudentList" @current-change="getStudentList"> </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { caseDetail } from '@/api/case'
import { casePractiseStudentList } from '@/api/casePractise'
import { clbumTree } from '@/api/clbum'
import { selectTeacherById } from '@/api/teacher'
import { formatDate } from '@/filters'
import Avatar from './components/Avatar.vue'

export default {
  name: '',
  components: {
    Avatar
  },
  data() {
    return {
      queryInfo: {
        clbumId: null,
        loginName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      time: null,
      classList: [],
      caseInfo: null,
      list: [],
      total: 0
    }
  },
  created() {
    this.getCaseDetails()
    this.getStudentList()
    this.getClassList()
  },
  methods: {
    goBack() {
      this.$router.push('/casePractise')
    },

    async getCaseDetails() {
      const { data } = await caseDetail({ id: this.$route.params.id })
      this.caseInfo = data
    },
    async getStudentList() {
      const { data } = await casePractiseStudentList({ ...this.queryInfo, caseId: this.$route.params.id })
      this.list = data.list
      this.total = data.total
    },
    getClassList() {
      selectTeacherById().then(async (res) => {
        const { data } = await clbumTree({ schoolId: res.data.schoolId })
        this.classList = data
      })
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getStudentList()
    },
    reset() {
      this.queryInfo = {
        clbumId: null,
        loginName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      }
      this.getStudentList()
    },
    details(item) {
      this.$router.push(`/casePractise/details/record/${item.practiseId}/${this.caseInfo.caseId}/${item.studentId}`)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  padding: 50px 20px 20px;
  padding-bottom: 0;
  .details_header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 25px;
    .goBack {
      position: absolute;
      left: 20px;
    }
    .title {
      font-size: 20px;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      color: #333333;
    }
  }
  .details_body {
    width: 100%;
    height: 100%;
    padding: 25px 20px;
    background: #ffffff;
    border: 1px solid #e0e4e8;
    .body_top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 25px;
      .caseInfo {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 90px;
        padding: 21px 0 22px;
        padding-right: 45px;
        background: #f4f7ff;
        border-radius: 4px 4px 4px 4px;

        .case_user {
          display: flex;
          align-items: center;
          width: 250px;
          padding-left: 30px;
          .userInfo {
            display: flex;
            flex-direction: column;
            margin-left: 8px;
            & > span:first-of-type {
              font-size: 18px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #333333;
            }

            & > span:last-of-type {
              margin-top: 4px;
              font-size: 16px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #999999;
            }
          }
        }
        .statistics_item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          & > span:first-of-type {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #333333;
            i {
              font-style: normal;
              font-size: 12px;
            }
          }
          & > span:last-of-type {
            margin-top: 14px;
            font-size: 18px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            color: #1890ff;
          }
        }
      }
      .statistics {
        flex: 1;
        height: 90px;
        background: #f4f7ff;
        border-radius: 4px 4px 4px 4px;
        margin-left: 11px;
        & > div {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: 100%;
          padding: 0 60px;
          .statistics_item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            & > span:first-of-type {
              font-size: 16px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 500;
              color: #333333;
              i {
                font-style: normal;
                font-size: 12px;
              }
            }
            & > span:last-of-type {
              margin-top: 14px;
              font-size: 18px;
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: bold;
              color: #1890ff;
            }
          }
          .boderItem {
            border-left: 1px dashed #d1e9ff;
            border-right: 1px dashed #d1e9ff;
            padding: 0 40px;
          }
        }
      }
    }
  }
}
.content {
  ::v-deep {
    .el-row {
      .el-form-item__label {
        font-size: 16px;
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        color: #121212;
      }
      .el-input__inner {
        height: 40px;
      }
    }

    .el-table {
      border: 1px solid #eae9e9;
      &::before {
        display: none;
      }
      .el-button--primary {
        color: #409eff;
        background: #e7f3ff;
        border-color: #e7f3ff;
      }
    }
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }
    .el-table .cell {
      color: #1a1a1a;
    }
  }
}
</style>
