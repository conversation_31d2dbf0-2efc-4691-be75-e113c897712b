<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="addDialog" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px">
        <el-form-item label="设备编号:" prop="code">
          <el-input v-model="form.code" placeholder="请输入设备编号" maxlength="40" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备名称:" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" maxlength="40" clearable></el-input>
        </el-form-item>
        <el-form-item label="设备型号:">
          <el-input v-model="form.model" placeholder="请输入设备型号" maxlength="40" clearable></el-input>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="40" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseDeviceAdd, caseDeviceUpdate } from '@/api/caseDevice'
export default {
  name: '',
  props: {
    addDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      form: { name: null, model: null, code: null, remark: null },
      rules: {
        code: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
        name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.form.deviceId ? '编辑设备' : '添加设备'
    }
  },
  created() {},
  methods: {
    close() {
      this.$refs['form'].resetFields()
      this.$emit('update:addDialog', false)
    },
    confirm() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.form.deviceId) {
            // 修改
            caseDeviceUpdate(this.form)
              .then(() => {
                this.$message.success('编辑设备成功！')
                loading.close()
                this.close()
                this.$emit('success')
              })
              .catch((err) => {
                console.log(err)
                loading.close()
              })
          } else {
            // 新增
            try {
              await caseDeviceAdd(this.form)
              this.$message.success('添加设备成功！')
              loading.close()
              this.close()
              this.$emit('success')
            } catch (error) {
              console.log(error)
              loading.close()
            }
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss"></style>
