import request from '@/utils/request'
import download from '@/utils/downloader.js'

//导入失败试题分页列表
export function questionErrorList(data) {
  return request({
    url: '/study/questionError/questionErrorList',
    method: 'post',
    data
  })
}
//根据导入失败试题id查询试题详情
export function selectQuestionErrorDetail(data) {
  return request({
    url: '/study/questionError/selectQuestionErrorDetail',
    method: 'get',
    params: data
  })
}
//导出全部失败试题（导出即删除，只能导出当前被用户导入的错题）
export function questionErrorExport(data) {
  return download({
    url: '/study/questionError/questionErrorExport',
    method: 'post',
    data
  })
}

//根据id删除导入失败普通试题（物理删除）
export function removeQuestionError(data) {
  return request({
    url: '/study/questionError/removeQuestionError',
    method: 'delete',
    params: data
  })
}
