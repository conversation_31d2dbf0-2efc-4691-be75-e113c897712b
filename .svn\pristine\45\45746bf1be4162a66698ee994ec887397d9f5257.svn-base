<template>
  <div>
    <!-- 选择病例 -->
    <el-dialog title="选择病例" :visible="selectCaseDialog" top="70px" custom-class="select-case-dialog" center @open="getAllList" @close="close" :close-on-click-modal="false">
      <el-form ref="form" :model="queryInfo" label-width="100px" inline>
        <el-form-item label="病例名称:">
          <el-input v-model="queryInfo.name" clearable placeholder="请输入病例名称" @clear="getCaseList" @keydown.native.enter="getCaseList"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" class="search-btn" size="small" @click="getCaseList">查询</el-button>
          <el-button type="primary" class="reset-btn" size="small" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="case-list">
        <div v-for="(item, index) in list" :key="item.caseId" :class="[{ isChecked: checkedCaseIds.includes(item.caseId) }, 'case-item']" @click="checked(item, index)">
          <span v-if="checkedCaseIds.includes(item.caseId)" class="checkedIcon">
            <i class="el-icon-check"></i>
          </span>
          <div class="caseItem_top_left">
            <casePhoto :sex="item.sex" :age="item.age" />
          </div>
          <div class="caseItem_top_right">
            <el-tooltip popper-class="caseNameTooltip" effect="dark" :content="item.name" placement="top">
              <div class="caseName">{{ item.name }}</div>
            </el-tooltip>
            <div class="patientInfo">
              <!-- <span> {{ item.realName }} </span> -->
              <span :class="{ man: item.sex === 'M' }" class="woman">（{{ item.sex === 'M' ? '男' : '女' }}）</span>
              <span class="sex"> {{ item.age }} 岁</span>
            </div>
            <div class="score">
              <span>病例总分</span>
              <span> {{ item.allScore }}分</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination style="margin-top: 25px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next, jumper" :total="total" @size-change="getCaseList" @current-change="getCaseList"> </el-pagination>
      </div>
      <div slot="footer">
        <el-button class="cancel__button" @click="close">取 消</el-button>
        <el-button class="confirm__button" type="primary" @click="confimSelect">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { caseList } from '@/api/case'
import casePhoto from '@/components/casePhoto'

export default {
  name: '',
  components: {
    casePhoto
  },
  props: {
    selectCaseDialog: {
      type: Boolean,
      require: true
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      // 选择病例
      queryInfo: {
        name: null,
        sort: 1,
        caseType: 2,
        isEnable: 1,
        orderby: 1,
        pageNum: 1,
        pageSize: 4
      },
      allList: [],
      list: [],
      total: 0,
      checkedCaseIds: []
    }
  },
  created() {},
  methods: {
    async getAllList() {
      const { data } = await caseList({ ...this.queryInfo, pageSize: 2000 })
      this.allList = data.list
    },
    async getCaseList() {
      const { data } = await caseList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      // 给每一个病例增加权重
      this.list.forEach((item) => {
        this.$set(item, 'weight', null)
      })
    },
    reset() {
      this.queryInfo = {
        name: null,
        sort: 1,
        caseType: 2,
        isEnable: 1,
        orderby: 1,
        pageNum: 1,
        pageSize: 4
      }
      this.getCaseList()
    },
    checked(item) {
      if (this.checkedCaseIds.includes(item.caseId)) {
        this.checkedCaseIds = this.checkedCaseIds.filter((li) => li !== item.caseId)
      } else {
        this.checkedCaseIds.push(item.caseId)
      }
    },
    confimSelect() {
      // 多选逻辑
      const list = this.allList.filter((item) => {
        return this.checkedCaseIds.includes(item.caseId)
      })
      this.close()
      this.$emit('success', list)
    },
    close() {
      this.$emit('update:selectCaseDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .select-case-dialog {
    position: relative;
    width: 1060px;
    height: 808px;
    background: #ffffff;
    border-radius: 30px;
    .el-dialog__title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #333333;
    }
    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 26px;
        width: 26px;
        height: 26px;
        color: #666;
        font-weight: 600;
      }
    }
    .el-form {
      display: flex;
      align-items: center;
      justify-content: center;
      .el-form-item {
        display: flex;
        align-items: center;
        margin-bottom: 0px;
        margin-right: 20px;
        .el-form-item__label {
          font-size: 20px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;
        }
        .el-input {
          width: 300px;
          height: 45px;
          .el-input__inner {
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            border-radius: 74px;
            border: none;
            font-size: 20px;
            color: #333333;
            &::placeholder {
              color: #cccccc;
            }
          }
          .el-input__suffix {
            .el-input__suffix-inner {
              .el-icon-circle-close {
                margin-right: 5px;
                margin-top: 2px;
                font-size: 20px;
              }
            }
          }
        }
      }
      .search-btn {
        padding: 0;
        width: 70px;
        height: 45px;
        background: #65849a;
        border-radius: 63px 63px 63px 63px;
        border: none;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
      }
      .reset-btn {
        padding: 0;
        width: 70px;
        height: 45px;
        background: #fff;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #65849a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #65849a;
        text-align: center;
      }
    }
    .el-dialog__body {
      padding-left: 40px;
    }
    .el-dialog__footer {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }
      .cancel__button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 93px;
        height: 50px;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #274e6a;
      }
      .confirm__button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 93px;
        height: 50px;
        background: #274e6a;
        border-radius: 63px 63px 63px 63px;
        border: 1px solid #274e6a;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #fff;
      }
    }

    .pagination-wrapper {
      .el-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-pagination .el-pagination__total {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #333333;
      }
      .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #65849a;
        color: #eef0f2;
      }
      .el-pagination.is-background .el-pager li {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        background-color: #eef0f2;
        color: #65849a;
      }
      .el-pagination.is-background .btn-prev,
      .el-pagination.is-background .btn-next {
        min-width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px;
        background-color: #eef0f2;
        color: #65849a;
        font-size: 16px;
      }
      .el-pagination.is-background .btn-prev:disabled {
        color: rgba($color: #65849a, $alpha: 0.3);
      }
      .el-pagination .el-pagination__jump {
        height: 32px;
        margin-left: 20px;
        font-size: 14px;
        .el-input {
          .el-input__inner {
            width: 32px;
            height: 32px;
            background: #ffffff;
            border-radius: 2px 2px 2px 2px;
            border: 1px solid #ebebeb;
          }
        }
      }
    }
  }
}
.case-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 40px;
  .case-item {
    display: flex;
    width: 480px;
    height: 197px;
    margin-bottom: 20px;
    margin-right: 20px;
    padding: 25px 20px 30px 26px;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0px 3px 5px 0px rgba(177, 177, 177, 0.15);
    border: 1px solid #e8e8e8;
    cursor: pointer;
    overflow: hidden;
    &:nth-of-type(2n) {
      margin-right: 0;
    }
    .caseItem_top_left {
      width: 115px;
      height: 140px;
      box-shadow: inset 8px 10px 50px 0px rgba(255, 255, 255, 0.3);
      border-radius: 10px 10px 10px 10px;
      ::v-deep {
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    .caseItem_top_right {
      flex: 1;
      margin-left: 18px;
      overflow: hidden;
      .caseName {
        margin-bottom: 15px;
        font-size: 28px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #07121e;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .patientInfo {
        .woman {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #f494b7;
        }
        .man {
          color: #3381b9;
        }
        .sex {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 20px;
          color: #666666;
        }
      }
      .score {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 300px;
        height: 50px;
        margin-top: 17px;
        background: #eef0f2;
        border-radius: 10px 10px 10px 10px;
        & > span:first-of-type {
          margin-right: 20px;
          font-size: 20px;
          font-family: PingFang SC;
          color: #333333;
        }
        & > span:last-of-type {
          font-size: 20px;
          font-family: PingFang SC;
          color: #3381b9;
        }
      }
    }
  }
  // 选中的数据
  .isChecked {
    position: relative;
    .checkedIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 0;
      bottom: 0;
      width: 32px;
      height: 32px;
      background: #3381b9;
      border-radius: 10px 0 10px 0px;
      .el-icon-check {
        font-size: 18px;
        font-weight: bold;
        color: #fff;
        text-align: center;
      }
    }
  }
}
</style>
