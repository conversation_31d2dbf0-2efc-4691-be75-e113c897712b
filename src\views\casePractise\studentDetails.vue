<template>
  <div class="student-details universal-layout">
    <div class="student-details_header universal-layout__header">
      <div class="header-back" @click="goBack">
        <img src="@/assets/case/goBackIcon.png" alt="" />
      </div>
      <div class="header-title" v-if="info">{{ info.caseName }}</div>
    </div>

    <div class="student-details_body">
      <div class="student-details-panel">
        <div class="studentInfo">
          <div class="title">学生信息</div>
          <div class="info" v-if="info">
            <div class="studentInfo__left">
              <div class="user">
                <div class="photo">
                  <img class="photo" :src="info.icon" alt="" />
                  <img v-if="info.sex === 'M'" class="sexIcon" src="@/assets/case/manIcon.png" alt="" />
                  <img v-else class="sexIcon" src="@/assets/case/womanIcon.png" alt="" />
                </div>
                <div class="name">
                  <span>{{ info.studentName }}</span>
                  <span>{{ info.loginName }}</span>
                </div>
              </div>
              <div class="statistics_item border_item">
                <span>{{ info.allCount ? info.allCount : 0 }}</span>
                <span>训练总次数<i>(次)</i></span>
              </div>
              <div class="statistics_item">
                <span>{{ info.avgTime ? info.avgTime : 0 }}</span>
                <span>平均用时 <i>(分)</i></span>
              </div>
            </div>
            <div class="studentInfo__statistics">
              <div class="statistics_item border_item">
                <span>{{ info.maxScore ? info.maxScore : 0 }}</span>
                <span>最高得分 <i>(分)</i></span>
              </div>
              <div class="statistics_item">
                <span>{{ info.avgScore ? info.avgScore : 0 }}</span>
                <span>平均得分 <i>(分)</i></span>
              </div>
            </div>
          </div>
        </div>
        <div class="caseInfo">
          <div class="title">病例信息</div>
          <div class="info" v-if="info">
            <div class="caseInfo__caseData">
              <div class="user border_item">
                <Avatar :sex="info.caseSex" :age="info.caseAge" />
                <div class="name">
                  <span>{{ info.caseName }}</span>
                  <span>{{ info.caseAge }}岁</span>
                </div>
              </div>
              <div class="statistics_item">
                <span>{{ info.caseAllScore ? info.caseAllScore : 0 }}</span>
                <span>病例总分 <i>(分)</i></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="student-data-panel">
        <div class="statisticsChart" v-if="info">
          <ChatStatistics ref="ChatStatistics" />
        </div>
        <div class="drillRecord">
          <div class="title">TA的训练记录</div>
          <el-table :data="list" style="width: 100%" border header-cell-class-name="tableHeaderCell" cell-class-name="tableCell">
            <el-table-column type="index" label="序号" width="60" align="center"> </el-table-column>
            <el-table-column prop="prop" label="训练时间" width="400" align="center">
              <template v-slot="{ row }">
                <span>{{ row.startTime }} ~ {{ row.endTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="times" label="训练用时(分)" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.times ? secondsToMinutes(row.times) : 0 }} </span>
              </template>
            </el-table-column>
            <el-table-column prop="allScore" label="训练得分(分)" width="width" align="center"> </el-table-column>
            <el-table-column prop="avgRate" label="平均正确率(%)" width="width" align="center"> </el-table-column>
            <el-table-column prop="prop" label="操作" width="width" align="center">
              <template v-slot="{ row }">
                <el-button type="text" @click="lookDialogue(row)">查看对话</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination style="text-align: center; margin-top: 15px" background :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" layout="total,  prev, pager, next, jumper" :total="total" @size-change="getPractiseDetailList" @current-change="getPractiseDetailList"> </el-pagination>
        </div>
      </div>
    </div>
    <!-- 查看对话 -->
    <LookDialogue ref="LookDialogue" :show-dialog.sync="dialogueDialog" :notCollect="notCollect" :record="record" />
  </div>
</template>
<script>
import { practiseStuDetail, practiseDetailList } from '@/api/casePractise'
import Avatar from '@/views/casePractise/components/Avatar'
import ChatStatistics from '@/views/casePractise/components/ChatStatistics'
import LookDialogue from '@/views/casePractise/components/LookDialogue'
export default {
  name: '',
  components: {
    Avatar,
    ChatStatistics,
    LookDialogue
  },
  data() {
    return {
      info: null,
      queryInfo: {
        caseId: this.$route.params.caseId,
        type: 1,
        studentId: this.$route.params.studentId,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      dialogueDialog: false,
      notCollect: [],
      record: []
    }
  },
  created() {
    this.getStudentDetails()
    this.getPractiseDetailList()
  },
  methods: {
    goBack() {
      this.$router.push(`/casePractise/details/${this.$route.params.caseId}/${this.info.caseName}`)
    },
    async getStudentDetails() {
      const practiseId = this.$route.params.id
      const caseId = this.$route.params.caseId
      const { data } = await practiseStuDetail({ practiseId, type: 1, caseId })
      this.info = data
      this.$nextTick(() => {
        this.$refs['ChatStatistics'].init(data.dtos, data.scores)
      })
    },
    async getPractiseDetailList() {
      const { data } = await practiseDetailList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    lookDialogue(row) {
      this.notCollect = row.notCollect
      this.record = row.record ? JSON.parse(row.record) : []
      this.dialogueDialog = true
    },
    secondsToMinutes(seconds) {
      var minutes = seconds / 60
      return minutes.toFixed(2)
    }
  }
}
</script>
<style scoped lang="scss">
.student-details {
  overflow-y: auto;
  /* 定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px; /* 滚动条宽度 */
    background-color: #f1f1f1; /* 滚动条背景色 */
  }

  /* 定义滚动条轨道样式 */
  &::-webkit-scrollbar-track {
    border-radius: 4px; /* 滚动条轨道的弧形形状 */
  }

  /* 定义滚动条滑块样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #a8a8a8; /* 滚动条滑块颜色 */
    border-radius: 4px; /* 滚动条滑块的弧形形状 */
  }

  /* 定义滚动条滑块在悬停状态时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #888787; /* 滚动条滑块悬停状态的颜色 */
  }
  .student-details_header {
    padding-top: 38px;
    .header-back {
      left: 0;
    }
  }
  .student-details_body {
    width: 100%;
    margin-top: 20px;
    .student-details-panel {
      display: flex;
      align-items: center;
      padding: 27px 30px 25px;
      margin-bottom: 20px;
      background: #fff;
      border-radius: 20px 20px 20px 20px;

      .title {
        margin-bottom: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #293543;
      }
      .studentInfo,
      .caseInfo {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        flex: 2;
        .info {
          display: flex;
          & > div {
            display: flex;
            align-items: center;
            justify-content: space-around;
            height: 90px;
            margin-right: 11px;
            background: #f4f7ff;
            border-radius: 10px;
            &:last-of-type {
              margin-right: 0;
            }
            .user {
              display: flex;
              align-items: center;
              .photo {
                position: relative;
                height: 49px;
                width: 49px;
                border-radius: 50%;
                .sexIcon {
                  position: absolute;
                  right: -3px;
                  width: 16px;
                  height: 16px;
                  bottom: 0;
                }
              }
              .name {
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 10px;
                & > span:first-of-type {
                  margin-bottom: 8px;
                  font-size: 18px;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #293543;
                }
                & > span:last-of-type {
                  font-size: 16px;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color: #67717d;
                }
              }
            }
            .statistics_item {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;

              & > span:first-of-type {
                font-size: 26px;
                font-family: PingFang SC;
                font-weight: 500;
                color: #293543;
              }
              & > span:last-of-type {
                margin-top: 14px;
                font-size: 18px;
                font-family: PingFang SC;
                color: #545d69;
                i {
                  font-style: normal;
                }
              }
            }
            .border_item {
              position: relative;
              // &::before {
              //   content: '';
              //   position: absolute;
              //   left: -50%;
              //   height: 47px;
              //   border-left: 1px dashed #d6ebff;
              // }
              &::after {
                content: '';
                position: absolute;
                right: -57%;
                height: 33px;
                width: 1px;
                background: #b7b9bf;
              }
            }
          }
          & > .studentInfo__left {
            flex: 1;
          }
          & > .studentInfo__statistics {
            position: relative;
            width: 474px;
            .border_item {
              position: static;
              &::after {
                right: 50%;
                transform: translateX(-50%);
              }
            }
          }
          & > .caseInfo__caseData {
            flex: 1;
            .border_item {
              &::after {
                right: -40%;
              }
            }
          }
        }
      }
      .caseInfo {
        flex: 1;
        margin-left: 11px;
      }
    }

    .student-data-panel {
      width: 100%;
      background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
      border-radius: 30px 30px 30px 30px;
    }
    .statisticsChart {
      width: 100%;
      height: 522px;
      border-bottom: 1px solid #c7cbd0;
    }
    .drillRecord {
      padding: 30px 20px;
      padding-bottom: 20px;
      .title {
        margin-bottom: 32px;
        font-size: 20px;
        font-family: PingFang SC;
        font-weight: bold;
        color: #333333;
      }
      ::v-deep {
        .tableCell {
          padding: 5px 0;
          font-size: 16px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #6e6f6d;
        }
        .tableHeaderCell {
          padding: 0;
          height: 40px;
          background: #f4f7ff;
          font-size: 16px;
          font-family:
            Source Han Sans CN,
            Source Han Sans CN;
          font-weight: 400;
          color: #333333;
        }
        .el-table--border .el-table__cell {
          border-right: none;
        }
      }
    }
  }
}
</style>
