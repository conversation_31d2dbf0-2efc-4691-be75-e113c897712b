import axios from 'axios'
/** 通过axios调用模型 */
// export function aiRequest(system, messages, stream) {
//   return new Promise((resolve, reject) => {
//     const token = 'sk-cedb691114764f4c90da4aed02653aed'
//     const model = 'qwq-plus'
//     const headers = {
//       Authorization: `Bearer ${token}`,
//       'Content-Type': 'application/json'
//     }
//     const options = {
//       method: 'POST',
//       body: JSON.stringify({
//         model: model,
//         messages: [
//           {
//             role: 'user',
//             content: messages
//           },
//           {
//             role: 'system',
//             content: system
//           }
//         ],
//         stream: stream,
//         max_tokens: 1500
//       })
//     }

//     // headers放到请求头中
//     axios
//       .post('https://dashscope.aliyuncs.com/compatible-mode/v1/', options.body, { headers })
//       .then((response) => resolve(response.data))
//       .catch((err) => reject(err))
//   })
// }

export const aiRequest = (systemConent, userConent) => {
  return new Promise((resolve, reject) => {
    axios
      .post('http://192.168.1.223:10086/api/chat', {
        messages: [
          {
            role: 'user',
            content: userConent
          },
          {
            role: 'system',
            content: systemConent
          }
        ],
        // prompt: systemConent + ' ' + userConent,
        model: 'qwen3:8b',
        // model: 'deepseek-r1:14b',
        // model: 'gemma3:27b',
        options: {
          // maxTokens: 2,
          // temperature: 0.3,
          // top_k: 30,
          // top_p: 0.4,
          num_ctx: 3000
        },
        stream: false
      })
      .then((res) => {
        console.log(res)

        let test = ''
        test = extractJSON(res.data.message.content)

        // res.data.forEach((item) => {
        //   test += item.result?.output?.content || ''
        // })
        resolve(test)
      })
      .catch((err) => {
        console.log(err)
        reject(err)
      })
  })
}

// 强化版JSON提取器（支持代码块/自由JSON/混合内容）
function extractJSON(str) {
  // 策略1：提取代码块内容
  const codeBlockRegex = /```json([\s\S]*?)```/
  const codeMatch = str.match(codeBlockRegex)
  if (codeMatch) {
    try {
      return JSON.parse(codeMatch[1].trim())
    } catch (e) {
      // 代码块内JSON可能仍存在问题，继续后续处理
    }
  }

  // 策略2：直接提取最外层JSON（支持数组和对象）
  const jsonRegex = /[\[{][\s\S]*[\]}]/ // 修改正则以支持数组
  const jsonMatch = str.match(jsonRegex)
  if (jsonMatch) {
    try {
      return JSON.parse(jsonMatch[0])
    } catch (e) {
      // 可能匹配到不完整结构，继续处理
    }
  }

  // 策略3：净化处理（激进模式）
  const purified = str
    .replace(/[^\w{}\[\]:,"'.+-]/g, '') // 保留JSON关键字符，添加[]和+-支持
    .replace(/(\w+)(?=:)/g, '"$1"') // 自动补全键名引号

  try {
    return JSON.parse(purified)
  } catch (e) {
    throw new Error('无法提取有效JSON数据')
  }
}
