import request from '@/utils/request'

//参数树
export function paramTree(data) {
  return request({
    url: '/system/param/paramTree',
    method: 'get',
    params: data
  })
}
//添加参数
export function saveParam(data) {
  return request({
    url: '/system/param/saveParam',
    method: 'post',
    data
  })
}
//修改参数
export function updateParam(data) {
  return request({
    url: '/system/param/updateParam',
    method: 'post',
    data
  })
}
//删除参数
export function removeParam(data) {
  return request({
    url: '/system/param/removeParam',
    method: 'delete',
    params: data
  })
}
//根据专业id查询模拟考试配置规则参数
export function selectSimulateParamList(data) {
  return request({
    url: '/system/param/selectSimulateParamList',
    method: 'get',
    params: data
  })
}
//添加模拟考试配置参数
export function saveSimulateParam(data) {
  return request({
    url: '/system/param/saveSimulateParam',
    method: 'post',
    data
  })
}
//修改模拟考试配置参数
export function updateSimulateParam(data) {
  return request({
    url: '/system/param/updateSimulateParam',
    method: 'post',
    data
  })
}
